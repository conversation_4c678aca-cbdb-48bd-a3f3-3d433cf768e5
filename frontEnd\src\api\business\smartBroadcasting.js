import request from "@/utils/request";
//  设备管理
export function GetEquipmentList(data) {
  return request({
    method: "post",
    url: "/DF/BroadcastEquipment/GetEquipmentList",
    data,
  });
}
export function GetEquipmentListSZCJ(data) {
  return request({
    method: "post",
    url: "/DF/BroadcastEquipment/GetEquipmentList/szcj",
    data,
  });
}
// 更新广播设备数据
export function PostEquipmentDataList(data) {
  return request({
    method: "post",
    url: "/DF/BroadcastEquipment/PostEquipmentDataList",
    data,
  });
}
// 更新广播设备数据
export function GetMediaFileList(data) {
  return request({
    method: "post",
    url: "/DF/BroadcastMediaList/GetMediaFileList",
    data,
  });
}
export function GetMediaFileListSZCJ(data) {
  return request({
    method: "post",
    url: "/DF/BroadcastMediaList/GetMediaFileList/szcj",
    data,
  });
}
// 更新广播媒体文件数据
export function PostMediaFileDataList(data) {
  return request({
    method: "post",
    url: "/DF/BroadcastMediaList/PostMediaFileDataList",
    data,
  });
}


