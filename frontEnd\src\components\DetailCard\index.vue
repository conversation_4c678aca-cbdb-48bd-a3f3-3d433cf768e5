<template>
  <el-card class="detail-card-box" shadow="none">
    <span class="title">{{ title }}</span>
    <div class="content">
      <div v-for="(item,index) in list" :key="index" class="item">
        <label class="content-label">{{ item.label }}：</label>
        <span>{{ item.value }}</span>
      </div>
    </div>
  </el-card>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.detail-card-box {
  height: 100px;
  background: #F7F8F9;

  ::v-deep {
    .el-card__body {
      padding: 16px 24px;
    }
  }

  .title {
    width: 90px;
    height: 24px;
    font-size: 18px;
    font-weight: bold;
    line-height: 28px;
    color: rgba(34, 40, 52, 0.85);
  }

  .content {
    display: flex;

    .item {
      margin-top: 16px;

      &:not(:first-child) {
        margin-left: 40px;
      }
    }

    .content-label {
      height: 21px;
      font-size: 16px;
      font-weight: 400;
      line-height: 26px;
      color: rgba(34, 40, 52, 0.4);
    }
  }
}
</style>
