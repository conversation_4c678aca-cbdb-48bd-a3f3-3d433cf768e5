import router, { resetRouter } from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'
import { getQueryObject } from '@/utils'
import $store from '@/store'
import { nextTick } from '@vue/composition-api'
const packageName = require('../package').name

// NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = [
  '/login',
  '/openplan/:id',
  '/auth-redirect',
  '/account/login',
  '/ssologin',
  '/account/password_reset',
  '/log/index',
  '/questionnaire/:Id/:type/:tenantId?',
  '/questionwatch/:Id/:type/:tenantId?',
  '/AssessmentMonth/:tenantId/:Id',
  '/AssessmentYear/:tenantId/:Id',
  '/singles/:page?',
  '/appdownload/app/scanme'
] // no redirect whitelist

router.beforeEach(async(to, from, next) => {
  // start progress bar
  // NProgress.start()
  // set page title
  // document.title = getPageTitle(to.meta.title, $store.state.settings.webName)

  if (to.name === 'SsoLogin') {
    store.dispatch('user/ssoLogin', to.query)
    const hasRoles = store.getters.roles && store.getters.roles.length > 0
    if (hasRoles) {
      if (router.getRoutes().some(v => v.name === to.query.pg_redirect)) {
        next({ name: to.query.pg_redirect })
      } else {
        router.push({ path: '/404' })
      }
      // NProgress.done()
    }
  }
  // determine whether the user has logged in
  const hasToken = getToken()
  if (hasToken) {
    if (to.path === '/login') {
      window.history.pushState(null, '', '/login')
      // next({ path: '/' })
      // NProgress.done()
    } else {
      await store.dispatch('user/getInfo')
      const hasRoles = store.getters.roles && store.getters.roles.length > 0
      if (hasRoles) {
        if (to.name === 'Dashboard') {
          var url = store.getters.HomePageUrl
          if (url !== 'null' && url !== '' && url !== null && url !== undefined) {
            next({ path: url })
          } else {
            next()
          }
        } else {
          next()
        }
      } else {
        next()
      }
    }
  } else {
    /* has no token*/
    if (
      whiteList.indexOf(to.path) !== -1 ||
      (to.matched[0]?.path && whiteList.indexOf(to.matched[0]?.path) !== -1)
    ) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      // next(`/login?redirect=${to.path}`)
      window.history.pushState(null, '', '/login')
      // NProgress.done()
    }
  }
})

router.afterEach((to, from) => {
  // finish progress bar
  if (to.fullPath === store.getters.HomePageUrl || (to.fullPath === '/dashboard' && !store.getters.HomePageUrl)) {
    to.meta.affix = true
    // store.dispatch('tagsView/delOtherAllViews')
  }
  // NProgress.done()
})
