import axios from "axios";
// 获取设备列表
export function GetProjectDeviceList() {
  return axios.get("/mock/GetProjectDeviceList.json")
    .then((response) => {
      return response.data;
    });
}
//  获取运行设备属性
export function GetDeviceCurrentData() {
  return axios.get("/mock/GetDeviceCurrentData.json")
    .then((response) => {
      return response.data;
    });
}
// 获取指定时间段设备运行数据
export function GetDeviceRangeData() {
  return axios.get("/mock/GetDeviceRangeData.json")
  .then((response) => {
    return response.data;
  });
}
// 设备故障列表
export function GetDeviceCurrentErrorData() {
  return axios.get("/mock/GetDeviceCurrentErrorData.json")
  .then((response) => {
    return response.data;
  });
}
// 获取负载电流
export function GetBeginI() {
  return axios.get("/mock/GetBeginI.json")
    .then((response) => {
      return response.data;
    });
}
// 获取指定时间段峰谷用电图标数据
export function GetPeakValleyElectricity() {
  return axios.get("/mock/GetPeakValleyElectricity.json")
  .then((response) => {
    return response.data;
  });
}
// 获取指定时间段的峰谷电列表数据
export function GetPeakValleyElectricityList() {
  return axios.get("/mock/GetPeakValleyElectricityList.json")
  .then((response) => {
    return response.data;
  });
}
// 导出指定时间段的峰谷电列表数据
export function ExportPeakValleyElectricity() {
  return axios.get("/mock/ExportPeakValleyElectricity.json")
  .then((response) => {
    return response.data;
  });
}
// 获取体系列表
export function GetPriceList() {
  return axios.get("/mock/GetPriceList.json")
  .then((response) => {
    return response.data;
  });
}
// 获取体系分类列表
export function GetSettingList() {
  return axios.get("/mock/GetSettingList.json")
  .then((response) => {
    return response.data;
  });
}
// 获取稼动率分析数据汇总
export function GetRangeOEECollect() {
  return axios.get("/mock/GetRangeOEECollect.json")
  .then((response) => {
    return response.data;
  });
}
// 获取设备属性
export function GetDevicePropertyList() {
  return axios.get("/mock/GetDevicePropertyList.json")
  .then((response) => {
    return response.data;
  });
}
// 导出设备属性
export function ExportCollectionDataOnRange() {
  return axios.get("/mock/ExportCollectionDataOnRange.json")
  .then((response) => {
    return response.data;
  });
}
// 导出设备
export function ExportRangeOEEList() {
  return axios.get("/mock/ExportRangeOEEList.json")
  .then((response) => {
    return response.data;
  });
}
// 获取每天数据
export function GetRangeOEE() {
  return axios.get("/mock/GetRangeOEE.json")
  .then((response) => {
    return response.data;
  });
}
// 获取表格数据
export function GetRangeOEEList() {
  return axios.get("/mock/GetRangeOEEList.json")
  .then((response) => {
    return response.data;
  });
}
// 获取表格数据
export function UpdateErrorStatus() {
  return axios.get("/mock/UpdateErrorStatus.json")
  .then((response) => {
    return response.data;
  });
}
// 导出异常台账
export function ExportRangeDeviceError() {
  return axios.get("/mock/ExportRangeDeviceError.json")
  .then((response) => {
    return response.data;
  });
}
// 异常台账列表
export function GetRangeDeviceError() {
  return axios.get("/mock/GetRangeDeviceError.json")
  .then((response) => {
    return response.data;
  });
}
