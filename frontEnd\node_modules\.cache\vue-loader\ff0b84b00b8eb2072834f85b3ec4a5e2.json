{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\realVideo\\Screen.vue?vue&type=style&index=0&id=19275a03&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\realVideo\\Screen.vue", "mtime": 1754618172895}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouc2NyZWVuQ29udGVudCB7CiAgLnRleHQtY2VudGVyIHsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICB9CiAgLnBsYXllci1idG4tZ3JvdXAgewogICAgLmVsLWJ1dHRvbi0tcHJpbWFyeSB7CiAgICAgIGNvbG9yOiAjNTY3OwogICAgICBiYWNrZ3JvdW5kOiAjZmZmOwogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2ICFpbXBvcnRhbnQ7CiAgICB9CiAgICAuYWN0aXZlIHsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzI5OGRmZjsKICAgICAgYm9yZGVyOiAxcHggc29saWQgIzI5OGRmZiAhaW1wb3J0YW50OwogICAgICBjb2xvcjogI2ZmZjsKICAgIH0KICB9CiAgI3NjcmVlbi1zdGlja3kgPiAjc2NyZWVuLXN0aWNreS1ib3R0b20gewogICAgZGlzcGxheTogbm9uZTsKICB9CiAgI3NjcmVlbi1zdGlja3ktd3JhcHBlci5zdGlja3kgPiAjc2NyZWVuLXN0aWNreSA+ICNzY3JlZW4tc3RpY2t5LWJvdHRvbSB7CiAgICBkaXNwbGF5OiBibG9jazsKICB9CiAgLmN1c3RvbUNvbnRhaW5lciB7CiAgICBoZWlnaHQ6IDEwMCU7CiAgICAuYXNpZGUgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICAuZmlsdGVyLXRyZWUgewogICAgICAgIGZsZXg6IDE7CiAgICAgICAgb3ZlcmZsb3cteTogYXV0bzsKICAgICAgICBwYWRkaW5nOiAyMHB4IDA7CiAgICAgIH0KICAgIH0KICB9CiAgLnZpZGVvLXNob3cgewogICAgLnZpZGVvIHsKICAgICAgYm9yZGVyOiAxcHggc29saWQgI2ZmZjsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["Screen.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2bA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Screen.vue", "sourceRoot": "src/views/business/safetyManagement/realVideo", "sourcesContent": ["<template>\n  <div class=\"screenContent app-container abs100\">\n    <el-card class=\"box-card h100\">\n      <div class=\"text-center\">\n        <el-button-group class=\"player-btn-group\">\n          <el-button\n            v-for=\"list in playerBtnGroup\"\n            :key=\"list.num\"\n            type=\"primary\"\n            :class=\"{ active: playerLength == list.num }\"\n            @click.prevent=\"setPlayerLength(list.num)\"\n          >{{ list.name }}</el-button>\n        </el-button-group>\n      </div>\n      <el-container class=\"customContainer\">\n        <el-aside class=\"aside\" width=\"400px\">\n          <el-input v-model=\"filterText\" placeholder=\"输入设备名称搜索\" />\n          <el-tree\n            ref=\"tree\"\n            class=\"filter-tree\"\n            :data=\"treeData\"\n            :props=\"defaultProps\"\n            highlight-current\n            node-key=\"Id\"\n            :filter-node-method=\"filterNode\"\n            :default-expanded-keys=\"defaultExpandedKeys\"\n            @node-click=\"treeNodeClick\"\n          />\n        </el-aside>\n        <el-main>\n          <el-row class=\"video-show\">\n            <el-col\n              v-for=\"(player, index) in players\"\n              :key=\"index\"\n              :span=\"colSpan\"\n              class=\"video\"\n              @click=\"clickPlayer(player, index, $event)\"\n              @touchend=\"clickPlayer(player, index, $event)\"\n            >\n              <LivePlayer\n                :video-url=\"player.url\"\n                :water-mark=\"player.osd\"\n                v-loading=\"player.bLoading\"\n                :smart=\"player.bSmart\"\n                :poster=\"player.poster\"\n                :controls=\"player.bControls && !loopPlaying\"\n                live\n                muted\n                stretch\n                :loading.sync=\"player.bLoading\"\n                element-loading-text=\"加载中...\"\n                element-loading-background=\"#000\"\n                @fullscreen=\"onFullscreenChange(player, index, $event)\"\n                @error=\"onError(player, index, $event)\"\n                @play=\"onPlay(player, index, $event)\"\n                @message=\"$message\"\n              />\n              <div\n                v-if=\"bVideoTitle && player.title\"\n                class=\"video-title\"\n                :title=\"player.title\"\n              >\n                {{ player.title }}\n              </div>\n              <div\n                v-show=\"player.url && player.bCloseShow && !loopPlaying\"\n                class=\"video-close\"\n                @click=\"closeVideo(index, true)\"\n              >\n                关闭\n              </div>\n            </el-col>\n          </el-row>\n        </el-main>\n      </el-container>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport LivePlayer from '@liveqing/liveplayer'\nimport { GetEquipmentTree, LookVideo } from '@/api/business/safetyManagement'\n\nexport default {\n  components: {\n    LivePlayer\n  },\n  data() {\n    return {\n      q: '',\n      players: [],\n      playerIdx: 0,\n      colSpan: 12,\n      playerLength: 1,\n      loadedData: false,\n      localData: {\n        num1: {},\n        num4: {},\n        num9: {}\n      },\n      channelListDlgTitle: '',\n      protocol: '',\n      showTree: true,\n      showGroupTree: false, // lazy load group tree\n      showTip: false,\n      treeLoading: false,\n      groupTreeLoading: false,\n      queryDevTreeLoading: false,\n      queryGroupTreeLoading: false,\n      defExpandDevs: [],\n      devLevelFilter: false,\n      groupLevelFilter: false,\n      fullscreenFlag: false,\n      contextMenuTarget: null,\n      contextMenuVisible: false,\n      contextMenuNodeData: null,\n      treeProps: {\n\n      },\n      bSmartStream: false,\n      bVideoTitle: false,\n      level: 0,\n      bPlayerFullscreen: false, // any player is fullscreen\n      outHevcTipIdx: -1, // idx of player is out hevc stuck\n      activeTab: 'dev',\n      filterText: '',\n      treeData: [],\n      defaultProps: {\n        children: 'Children',\n        label: 'Name'\n      },\n      defaultExpandedKeys: []\n    }\n  },\n  computed: {\n    playerBtnGroup() {\n      var list = [{\n        num: 1,\n        name: '单屏'\n      }, {\n        num: 4,\n        name: '四分屏'\n      }, {\n        num: 9,\n        name: '九分屏'\n      }]\n      return list\n    },\n    playing() {\n      var player = this.players[this.playerIdx] || {}\n      return !!player.url\n    },\n    treeEmptyText() {\n      return this.treeLoading ? '加载中...' : '暂无数据'\n    },\n    showQueryDevTree() {\n      if (!this.q) return false\n      if (this.activeTab === 'dev' && this.devLevelFilter) {\n        this.queryDevTreeLoading = true\n        return true\n      }\n      return false\n    },\n    showQueryGroupTree() {\n      if (!this.q) return false\n      if (this.activeTab === 'group' && this.groupLevelFilter) {\n        this.queryGroupTreeLoading = true\n        return true\n      }\n      return false\n    }\n  },\n  watch: {\n    bSmartStream: function(newVal, oldVal) {\n      for (const idx in this.players) {\n        const player = this.players[idx]\n        if (!player) continue\n        const _url = player.url\n        if (!_url) continue\n        player.url = ''\n        player.bSmart = newVal\n        this.$nextTick(() => {\n          player.url = _url\n        })\n      }\n    },\n    filterText(val) {\n      this.$refs.tree.filter(val)\n    }\n  },\n  mounted() {\n    this.setPlayerLength(this.playerLength)\n    this.contextMenuTarget = document.querySelector('#tab-tree-wrapper')\n    this.getEquipmentTree()\n  },\n  beforeRouteEnter(to, from, next) {\n    next(vm => {\n      if (to.query.protocol) {\n        vm.protocol = to.query.protocol\n      }\n    })\n  },\n  beforeRouteUpdate(to, from, next) {\n    this.clearVideos()\n    if (to.query.protocol) {\n      this.protocol = to.query.protocol\n    }\n    next()\n  },\n  beforeRouteLeave(to, from, next) {\n    this.clearVideos()\n    next()\n  },\n  methods: {\n    clearVideos() {\n      this.outHevcTipIdx = -1\n      for (var idx in this.players) {\n        this.closeVideo(idx)\n      }\n    },\n    play(index, channel, next) {\n      console.log(channel)\n      var i = 0\n      var player = null\n      for (var _player of this.players) {\n        if (index === i) {\n          player = _player\n          break\n        }\n        i++\n      }\n      if (!player) {\n        this.$message({\n          type: 'error',\n          message: '当前播放窗口已被占满！'\n        })\n        return\n      }\n      if (player.bLoading) return\n      player.bLoading = true\n      if (next) {\n        this.setPlayerIdx(index + 1)\n      }\n      if (channel.Node) {\n        player.node = channel.Node\n        this.$set(player.node, 'playing', true)\n        delete channel.Node\n      }\n      LookVideo({ id: channel.ID }).then(res => {\n        if (res.IsSucceed) {\n          const videoUrl = res.Data\n          player.bSmart = this.playerLength > 1 && this.bSmartStream\n          player.url = videoUrl\n          player.code = channel.ID\n          player.protocol = 'HLS'\n          player.title = channel.ID\n          if (player.node) {\n            this.$delete(player.node, 'playing')\n            var play = player.node.play || []\n            if (videoUrl) {\n              play.push(index)\n            }\n            if (play.length) {\n              this.$set(player.node, 'play', play)\n            }\n          }\n          if (!this.loadedData) {\n            this.localData['num' + this.playerLength] = {}\n          }\n          this.localData['num' + this.playerLength]['c' + index] = JSON.stringify(channel)\n          this.setLocalData()\n        } else {\n          this.closeVideo(this.playerIdx)\n          this.$message.error(res.Message)\n        }\n      })\n    },\n    closeVideo(idx, manual = false) {\n      var player = this.players[idx]\n      if (!player) return\n      if (player.closeTimer) {\n        clearTimeout(player.closeTimer)\n        player.closeTimer = 0\n      }\n      if (this.outHevcTipIdx === idx) {\n        this.outHevcTipIdx = -1\n      }\n      if (player.node) {\n        var play = player.node.play || []\n        play = play.filter(val => val !== idx)\n        if (play.length) {\n          this.$set(player.node, 'play', play)\n        } else {\n          this.$delete(player.node, 'play')\n          this.$delete(player.node, 'playing')\n        }\n        delete player.node\n      }\n      player.mediaInfo = null\n      player.bCloseShow = false\n      player.bControls = false\n      player.bLoading = false\n      player.bSmart = false\n      player.bFullscreen = false\n      player.poster = ''\n      player.title = ''\n      player.osd = ''\n      player.url = ''\n      player.ptzType = 0\n      player.serial = ''\n      player.code = ''\n      if (manual) {\n        delete this.localData['num' + this.playerLength]['c' + idx]\n        this.setLocalData()\n      }\n    },\n    setPlayerLength(len) {\n      if (len === this.players.length) return\n      this.clearVideos()\n      this.players = []\n      this.playerLength = len\n      len === 1 ? this.colSpan = 24 : len === 4 ? this.colSpan = 12 : this.colSpan = 8\n      this.loadedData = false\n      this.playerIdx = 0\n      for (var i = 0; i < len; i++) {\n        this.players.push({\n          serial: '',\n          code: '',\n          url: '',\n          ptzType: 0,\n          protocol: '',\n          poster: '',\n          title: '',\n          osd: '',\n          bLoading: false,\n          bCloseShow: false,\n          bControls: false,\n          bSmart: false,\n          bFullscreen: false,\n          closeTimer: 0,\n          closeInterval: 0,\n          mediaInfo: null\n        })\n      }\n    },\n    setPlayerIdx(idx) {\n      var newIdx = idx % this.players.length\n      this.playerIdx = newIdx\n    },\n    onError(player, idx, e) {\n      if (e === 'MediaError' && player.mediaInfo && String(player.mediaInfo.videoCodec).startsWith('hvc')) {\n        this.outHevcTipIdx = idx\n        console.log('提示: 正在播放 H265 FLV 直出流, 确保浏览器版本较新, 并且开启硬件加速')\n      }\n    },\n    onPlay(player, idx, t) {\n      if (this.outHevcTipIdx === idx && t >= 1) {\n        this.outHevcTipIdx = -1\n      }\n    },\n    onFullscreenChange(player, idx, bFullscreen) {\n      if (player) {\n        player.bFullscreen = bFullscreen\n        this.bPlayerFullscreen = bFullscreen\n        if (bFullscreen) {\n          $(`#dev-tree-right .video-show .video:eq(${idx}) .ptz-block-fs`).draggable({\n            handle: '.ptz-center',\n            cancel: '.ptz-cell',\n            containment: `#dev-tree-right .video-show .video:eq(${idx}) .video-js`,\n            delay: 100\n          })\n        } else {\n          $(`#dev-tree-right .video-show .video:eq(${idx}) .ptz-block-fs`).draggable('destroy')\n        }\n      }\n    },\n    fullscreen() {\n      // if (this.isMobile()) {\n      //   this.$message({\n      //     type: \"error\",\n      //     message: \"请在电脑浏览器上使用该功能\"\n      //   });\n      //   return;\n      // }\n      this.$fullscreen.enter(this.$el.querySelector(`.video-show > div`), {\n        wrap: false,\n        callback: f => {\n          this.fullscreenFlag = f\n        }\n      })\n    },\n    treeNodeClick(data, node) {\n      console.log(node.isLeaf && (node.data.ParentId ?? '') != '')\n      if (node.isLeaf && (node.data.ParentId ?? '') != '') {\n        this.contextMenuNodeData = null\n        this.contextMenuNode = null\n        if (node && !node.playing) {\n          if (!node.clickCnt || node.clickCnt > 1) {\n            node.clickCnt = 1\n          } else {\n            node.clickCnt++\n          }\n          var player = this.players[this.playerIdx] || {}\n          if (player.bLoading) return\n          this.closeVideo(this.playerIdx)\n          this.$nextTick(() => {\n            this.play(this.playerIdx, {\n              ID: data.Id,\n              Name: data.Name,\n              Node: node\n            }, true)\n          })\n        }\n      }\n    },\n    async getEquipmentTree() {\n      const res = await GetEquipmentTree()\n      console.log('res', res)\n      if (res.IsSucceed) {\n        this.treeData = res.Data\n        if ((res.Data ?? []).length > 0) {\n          this.$nextTick(() => {\n            this.defaultExpandedKeys = [res.Data[0]?.Id, res.Data[0]?.Children[0]?.Id, res.Data[0]?.Children[0]?.Children[0]?.Id]\n            this.play(this.playerIdx, {\n              ID: res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id,\n              Name: res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Name,\n              Node: this.$refs.tree.getNode(res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id)\n            }, true)\n            this.$refs.tree.setCurrentKey(res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id)\n          })\n        }\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    filterNode(value, data) {\n      if (!value) return true\n      return data.Name.indexOf(value) !== -1\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.screenContent {\n  .text-center {\n    text-align: center;\n  }\n  .player-btn-group {\n    .el-button--primary {\n      color: #567;\n      background: #fff;\n      border: 1px solid #dcdfe6 !important;\n    }\n    .active {\n      background-color: #298dff;\n      border: 1px solid #298dff !important;\n      color: #fff;\n    }\n  }\n  #screen-sticky > #screen-sticky-bottom {\n    display: none;\n  }\n  #screen-sticky-wrapper.sticky > #screen-sticky > #screen-sticky-bottom {\n    display: block;\n  }\n  .customContainer {\n    height: 100%;\n    .aside {\n      display: flex;\n      flex-direction: column;\n      .filter-tree {\n        flex: 1;\n        overflow-y: auto;\n        padding: 20px 0;\n      }\n    }\n  }\n  .video-show {\n    .video {\n      border: 1px solid #fff;\n    }\n  }\n}\n</style>\n"]}]}