
<template>
  <div class="CustomTable">
    <div class="button">
      <CustomTableStatistic :custom-table-statistic-config="customTableObj.statisticConfig || {}" />
      <CustomButton :custom-button-config="customTableObj.buttonConfig" />
    </div>
    <div class="table" style="margin-top:12px">
      <el-table v-loading="customTableObj.loading" :data="tableData"
        :border="customTableConfig.border == false ? false : true" :fit="true" stripe style="width: 100%"
        :height="customTableConfig.height ? customTableConfig.height : tableHeight" :row-key="getRowKeys" @selection-change="selectionChange"
        @select="select" @select-all="selectAll" ref="table">
        <!-- <el-table-column
          v-for="(item,index) in customTableObj.tableColumns"
          :key="index"
          v-bind="item.otherOptions"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
        /> -->
        <!-- <el-table-column
          v-for="(item,index) in customTableObj.tableColumns"
          :key="index"
          v-bind="item.otherOptions"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
        >
          <template slot-scope="scope">
            <slot :slot-scope="scope.row" :name="item.key">{{ scope.row[item.key] }}</slot>
          </template>
        </el-table-column> -->
        <template v-for="(item, index) in tableColumns">
          <el-table-column v-if="!item.hide" :key="index" v-bind="item.otherOptions" :prop="item.key" :label="item.label"
            :width="item.width" :resizable="false" show-overflow-tooltip>
            <template v-if="item.render" v-slot="scope">
              <!-- 使用函数式组件进行dom渲染 -->
              <render-dom :render="() => item.render(scope.row)" />
            </template>
            <!-- <template v-else v-slot="scope">
            <span class="no-wrap-cell">{{ scope.row[item.key] }}</span>
          </template> -->
            <!-- <template v-if="item.otherOptions.type == 'index'">
            <span class="no-wrap-cell">{{ scope.row[item.key] }}</span>
          </template> -->
          </el-table-column>
        </template>
        <el-table-column v-if="customTableObj.tableActions.length > 0" label="操作" v-bind="customTableObj.operateOptions"
          fixed="right">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in customTableConfig.tableActions" :key="index" v-bind="item.otherOptions"
              size="mini"  v-show="getenavl(item,scope.row)" @click="item.onclick(scope.$index, scope.row)">{{ item.actionLabel }}</el-button>
            <slot :slot-scope="scope.row" name="customBtn" />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div v-if="!customTableObj.disablidPagination" class="pagination">
      <el-pagination :total="customTableObj.total" :page-sizes="customTableObj.pageSizeOptions"
        :current-page="customTableObj.currentPage" :page-size="customTableObj.pageSize"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </div>
</template>
<script>
import CustomTableStatistic from '@/components/components/CustomTableStatistic/index.vue'
import CustomButton from '@/components/components/CustomButton/index.vue'
export default {
  components: {
    CustomTableStatistic,
    CustomButton,
    renderDom: {
      functional: true,
      props: {
        render: Function
      },
      render(createElement, renDom) {
        return <div>{renDom.props.render()}</div>
      }
    }
  },
  props: {
    customTableConfig: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      tableHeight: null, // 页面高度

    }
  },

  computed: {
    customTableObj() {
      return this.customTableConfig
    },
    tableColumns() {
      const columns = []
      return [].concat(columns, this.customTableObj.tableColumns)
    },
    tableData() {
      const data = []
      return [].concat(data, this.customTableObj.tableData)
    }
  },
  watch: {
    // 监视name属性的变化
    tableColumns(newValue, oldValue) {
      if (newValue.length > 0) {
        setTimeout(() => {
          this.getTableHeight()
          window.addEventListener('resize', this.getTableHeight) // 监听窗口大小变化，重新计算高度 }
        }, 0)
      }
    }
  },
  mounted() { },
  beforeDestroy() {
    window.removeEventListener('resize', this.getTableHeight) // 移除事件监听器
  },
  methods: {
    getTableHeight() {
      // 计算页面高度，并减去其他元素的高度（如页眉、页脚等）
      const pageHeight = document.documentElement.clientHeight
      const otherElementHeight = 340 // 其他元素的高度，根据实际情况设置
      this.tableHeight = pageHeight - otherElementHeight
    },
    getenavl(item,row){
      if((item.isShow||item.isShow===undefined)&&(eval(item.condition))||item.condition===undefined){
        return true
      }else{
        return false
      }
    },
    handleSizeChange(val) {
      this.$emit('handleSizeChange', val)
    },
    handleCurrentChange(val) {
      this.$emit('handleCurrentChange', val)
    },
    selectionChange(selection) {
      this.$emit('handleSelectionChange', selection)
    },
    select(selection, row) {
      this.$emit('select', selection, row)
    },
    selectAll(selection) {
      console.log('ffffffffffffffffff ', selection);
      this.$emit('selectall', selection, this.tableData)
    },
    setSelection(rowList, selected) {
      rowList.map(tmp => {
        let row = this.tableData.find(item => item.Id === tmp.Id)
        this.$nextTick(() => {
          this.$refs.table.toggleRowSelection(row, selected)
        })

      })

    },
    getRowKeys(row) {
      return row.Id || row.id || row;
    }
  }
}
</script>

<style lang="scss" scoped>
.CustomTable {
  display: flex;
  flex-direction: column;


  .button {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .pagination {
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }

  .no-wrap-cell {
    white-space: nowrap;
  }

  // display: flex;
  // flex-direction: column;
  // background-color: white;
  // // padding: 10px 15px;
  // .table{
  //   padding: 2px 5px;
  // }
  // .el-pagination{
  //   display: flex;
  //   justify-content: flex-end;

  // }
}
</style>
