{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\components\\gasUsed.vue?vue&type=style&index=0&id=058cdaf6&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\components\\gasUsed.vue", "mtime": 1754560054588}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["gasUsed.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "gasUsed.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis/gas/components", "sourcesContent": ["<template>\n  <div class=\"gas-container\">\n    <div class=\"title\">\n      {{ dataObj.title }}\n      <el-tooltip class=\"item\" effect=\"dark\" placement=\"top-start\">\n        <div slot=\"content\">{{ dataObj.tooltip }}</div>\n        <img\n          style=\"width: 16px; height: 16px; margin-left: 8px\"\n          src=\"@/assets/question.png\"\n          alt=\"\"\n        >\n      </el-tooltip>\n    </div>\n    <div class=\"middle\">\n      <div class=\"Bgbox\">\n        <div class=\"fill\">\n          <div\n            :style=\"{ height: dataObj.fillHeight, background: dataObj.color }\"\n          />\n        </div>\n        <div class=\"iconBg\" />\n      </div>\n      <div v-if=\"dataObj.showTotal\" class=\"middleText\">\n        剩余\n        <span :style=\"{ color: dataObj.color }\">{{\n          dataObj.residue.value\n        }}</span> {{ dataObj.unit }}\n        <span :style=\"{ color: dataObj.color }\">{{\n          dataObj.residue.percentage\n        }}</span>\n      </div>\n    </div>\n\n    <!-- 龙建科工 暂时隐藏 -->\n    <!-- <el-row :gutter=\"20\" class=\"customRow\">\n      <el-col v-for=\"(item, index) in dataObj.colData\" :key=\"index\" :span=\"8\">\n        <div class=\"bottomContainer\">\n          <img class=\"arrow\" :src=\"gas\" alt=\"\">\n          <div class=\"down\">\n            <img\n              :src=\"item.iconName == 'delivery' ? delivery : workshop\"\n              alt=\"\"\n            >\n            <div class=\"textData\">\n              <h2>{{ item.Key }}</h2>\n              <p :style=\"{ color: dataObj.color }\">\n                <span style=\"width: 70px\">{{ item.Percent }} %</span><span\n                  style=\"flex: 1\"\n                >{{ item.Value }} {{ dataObj.unit }}</span>\n              </p>\n            </div>\n          </div>\n        </div>\n      </el-col>\n    </el-row> -->\n  </div>\n</template>\n\n<script>\nexport default {\n  components: {},\n  props: {\n    customGasUsedConfig: {\n      type: Object,\n      default: () => {}\n    },\n    isPhotovoltaic: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      workshop: require('@/assets/workshop.png'),\n      delivery: require('@/assets/delivery.png'),\n      gas: require('@/assets/Business/gas.png')\n    }\n  },\n  computed: {\n    dataObj() {\n      const data = {\n        ...this.customGasUsedConfig.baseData,\n        ...this.customGasUsedConfig.gasData\n      }\n      return data\n    }\n  },\n  created() {},\n  mounted() {},\n  methods: {}\n}\n</script>\n <style scoped lang='scss'>\n.gas-container {\n  width: 100%;\n  height: 363px;\n  padding: 16px 24px;\n  border-radius: 4px;\n  display: flex;\n  flex-direction: column;\n  background: #fff;\n  .title {\n    display: flex;\n    align-items: center;\n    margin-bottom: 24px;\n  }\n  .middle {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    padding: 12px;\n    height: 133px;\n    background: linear-gradient(\n      90deg,\n      rgba(41, 141, 255, 0.05) 0%,\n      rgba(41, 141, 255, 0) 100%\n    );\n    .Bgbox {\n      width: 48px;\n      height: 110px;\n      margin-right: 24px;\n      position: relative;\n      .iconBg {\n        width: 48px;\n        height: 110px;\n        background: url(\"../../../../../../assets/Business/box.png\");\n        background-size: cover;\n        position: absolute;\n        top: 2px;\n        left: 0;\n      }\n      .fill {\n        position: absolute;\n        width: 40px;\n        height: 77px;\n        bottom: 12px;\n        left: 4px;\n        overflow: hidden;\n        div {\n          width: 40px;\n          position: absolute;\n          left: 0;\n          bottom: 0;\n        }\n      }\n    }\n    .middleText {\n      font-size: 24px;\n      color: #333;\n      span {\n        font-weight: bold;\n        margin: 0 16px;\n      }\n    }\n  }\n  .customRow {\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n    .bottomContainer {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      .arrow {\n        height: 72px;\n        width: 28px;\n      }\n      .down {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        height: 83px;\n        background: linear-gradient(\n          90deg,\n          rgba(41, 141, 255, 0.05) 0%,\n          rgba(41, 141, 255, 0) 100%\n        );\n        img {\n          width: 24px;\n          height: 24px;\n          margin-right: 16px;\n        }\n        .textData {\n          h2 {\n            color: #333;\n            font-size: 16px;\n            font-weight: 400;\n            margin-bottom: 16px;\n          }\n          p {\n            display: flex;\n          }\n          span {\n            display: block;\n            font-weight: bold;\n            font-size: 14px;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}