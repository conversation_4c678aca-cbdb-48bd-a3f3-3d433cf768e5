import request from '@/utils/request'

export function GetSchedulePageList(data) {
  return request({
    method: 'post',
    url: '/Platform/Schedule/GetSchedulePageList',
    data
  })
}

export function GetScheduleDetails(data) {
  return request({
    method: 'post',
    url: '/Platform/Schedule/GetScheduleDetails',
    data
  })
}

export function QueryLocalHandler(data) {
  return request({
    method: 'post',
    url: '/Platform/Schedule/QueryLocalHandlersFullInfo',
    data
  })
}

export function SaveSchedules(data) {
  return request({
    method: 'post',
    url: '/Platform/Schedule/SaveSchedules',
    data
  })
}

export function DeleteSchedules(data) {
  return request({
    method: 'post',
    url: '/Platform/Schedule/DeleteSchedules',
    data
  })
}

export function ChangeScheduleStatus(data) {
  return request({
    method: 'post',
    url: '/Platform/Schedule/ChangeScheduleStatus',
    data
  })
}
