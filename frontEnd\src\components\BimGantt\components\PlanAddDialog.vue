<template>
  <div style="padding:0 16px 0 10px;">
    <el-form ref="form" :model="form" label-width="120px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="类别" required prop="Type_Id">
            <el-popover
              v-if="editmode"
              v-model="visible"
              placement="bottom"
              width="240"
              height="240"
              trigger="manual"
            >
              <el-menu
                :default-active="form.Type_Id"
                style="border-right:0;"
                @select="handleSelectType"
              >
                <TypeTreeItem :data="typetree" />
              </el-menu>
              <el-input
                slot="reference"
                :value="getPlanType(form.Type_Id, typetree).Label"
                type="text"
              >
                <el-button
                  slot="append"
                  icon="el-icon-arrow-down"
                  @click="visible = !visible"
                />
              </el-input>
            </el-popover>

            <span v-else>
              {{ getPlanType(form.Type_Id, typetree).Label }}
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编号" required prop="Code">
            <el-input v-if="editmode" v-model="form.Code" />
            <span v-else>{{ form.Code }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="名称" required prop="Name">
        <el-input
          v-if="editmode"
          v-model="form.Name"
          placeholder="计划名称，最多30字"
        />
        <span v-else>{{ form.Name }}</span>
      </el-form-item>

      <el-form-item label="计划开始" required prop="Plan_Start_Date">
        <el-date-picker
          v-if="editmode"
          v-model="form.Plan_Start_Date"
          type="date"
          placeholder="选择日期"
        />
        <span v-else>{{ toDateStr(form.Plan_Start_Date) }}</span>
      </el-form-item>
      <el-form-item label="计划完成" required prop="Plan_End_Date">
        <el-date-picker
          v-if="editmode"
          v-model="form.Plan_End_Date"
          type="date"
          placeholder="选择日期"
        />
        <span v-else>{{ toDateStr(form.Plan_End_Date) }}</span>
        <span>（计划工期：<span style="color:#298DFF;">{{
          form.Plan_Duration
        }}</span>自然天）</span>
      </el-form-item>
      <el-form-item label="人工资源显示" prop="Resources">
        <el-radio-group v-if="editmode" v-model="form.Resources">
          <el-radio :label="1">工日</el-radio>
          <el-radio :label="2">人数</el-radio>
        </el-radio-group>
        <span v-else>{{
          !form.Resources
            ? ''
            : form.Resources.toString() === '1'
              ? '工日'
              : form.Resources.toString() === '2'
                ? '人数'
                : ''
        }}</span>
      </el-form-item>
      <el-form-item label="计划权限" prop="Plan_Oauth">
        <el-radio-group v-if="editmode" v-model="form.Plan_Oauth">
          <el-radio :label="'0'">私密</el-radio>
          <el-radio :label="'1'">公开</el-radio>
        </el-radio-group>
        <span v-else>{{
          !form.Plan_Oauth
            ? ''
            : form.Plan_Oauth.toString() === '0'
              ? '私密'
              : form.Plan_Oauth.toString() === '1'
                ? '公开'
                : ''
        }}</span>
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所有者" required prop="Admin">
            <el-select
              v-if="editmode"
              v-model="form.Admin"
              placeholder="请选择计划所有者"
              multiple
              style="width:100%;"
              filterable
            >
              <template v-for="m in members">
                <el-option
                  :key="m.User_Id"
                  :label="m.UserName"
                  :value="m.User_Id"
                />
              </template>
            </el-select>
            <span v-else>
              {{ getUsers(form.Admin) }}
            </span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="查看者" prop="Observer">
        <el-select
          v-if="editmode"
          v-model="form.Observer"
          placeholder="请选择计划查看者"
          multiple
          filterable
          style="width:100%;"
        >
          <template v-for="m in members">
            <el-option
              :key="m.User_Id"
              :label="m.UserName"
              :value="m.User_Id"
            />
          </template>
        </el-select>
        <span v-else>
          {{ getUsers(form.Observer) }}
        </span>
      </el-form-item>
      <el-form-item label="说明">
        <el-input
          v-if="editmode"
          v-model="form.Remark"
          type="textarea"
          rows="5"
        />
        <span v-else>
          {{ form.Remark }}
        </span>
      </el-form-item>
      <el-form-item align="right">
        <el-button @click="$emit('dialogCancel')">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import * as BGT from '../index'
import TypeTreeItem from './TypeTreeItem'
import * as moment from 'moment'
export default {
  name: 'PlanAddDialog',
  components: {
    TypeTreeItem
  },
  props: {
    plan: {
      type: Object,
      default: () => null
    },
    typetree: {
      type: Array,
      default: () => []
    },
    members: {
      type: Array,
      default: () => []
    },
    editMode: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false, // 分类选择popup可见性
      form: {
        Type_Id: '',
        Code: '',
        Id: '',
        Name: '',
        Resources: 1, // 人力资源展示方式,1人工，2工日
        Plan_Oauth: '0',
        Plan_Start_Date: null,
        Plan_End_Date: null,
        Plan_Duration: 0,
        Admin: [],
        Observer: [],
        Remark: ''
      },
      editmode: false,
      rules: {
        Type_Id: [
          { required: true, message: '选择计划类别', trigger: 'change' }
        ],
        Code: [
          { required: true, message: '请填写编号', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        Name: [
          { required: true, message: '请填写名称', trigger: 'blur' },
          { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
        ],
        Plan_Start_Date: [
          {
            type: 'date',
            required: true,
            message: '请选择计划开始时间',
            trigger: 'change'
          }
        ],
        Plan_End_Date: [
          {
            type: 'date',
            required: true,
            message: '请选择计划完成时间',
            trigger: 'change'
          },
          {
            validator: (rule, value, callback) => {
              if (value < this.form.Plan_Start_Date) {
                return callback('完成时间不能早于开始时间')
              }
              callback()
            },
            trigger: 'change'
          }
        ],
        Admin: [{ type: 'array', required: true, message: '选择所有者', trigger: 'change' }],
        // Observer: [
        //   {
        //     type: 'array',
        //     required: true,
        //     message: '请至少选择一个查看者',
        //     trigger: 'change'
        //   }
        // ],
        Resources: [
          { required: true, message: '请选择活动资源', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    role() {
      return new BGT.PlanAuth(this.plan?.Plan_Auth)
    }
  },
  watch: {
    'form.Plan_Start_Date'(nv, ov) {
      if (nv !== ov) {
        BGT.calcPlanDurations(this.form)
      }
    },
    'form.Plan_End_Date'(nv, ov) {
      if (nv !== ov) {
        BGT.calcPlanDurations(this.form)
      }
    }
  },
  created() {
    this.editmode = this.editMode
    if (this.plan) {
      if (this.editmode && !this.canEdit()) {
        this.editmode = false
      }
      Object.keys(this.form).forEach(k => {
        if (this.plan[k] !== undefined) {
          this.form[k] = this.plan[k]
          if (k === 'Resources' || k === 'Plan_Duration') {
            this.form[k] = Number(this.form[k])
          }
        }
      })
    } else {
      this.form = BGT.createEmptyPlan()
      this.form.Plan_Oauth = '1'
      Object.keys(this.form).forEach(k => {
        if (k === 'Resources' || k === 'Plan_Duration') {
          this.form[k] = Number(this.form[k])
        }
      })
      this.form.Plan_Auth = '负责'

      this.form.Plan_Data = {
        data: [],
        links: []
      }
      this.form.Type_Id = this.type || '0'
    }
    if (!this.form.Admin) {
      this.form.Admin = this.$store.state.user.userId ? [this.$store.state.user.userId] : []
    }
    if (!this.form.Plan_Start_Date) {
      this.form.Plan_Start_Date = moment(new Date())
        .startOf('date')
        .toDate()
    }
  },
  methods: {
    onSubmit() {
      if (!this.editmode) {
        return this.$emit('dialogCancel')
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$emit('dialogFormSubmitSuccess', {
            type: 'setPlanBase',
            data: this.form
          })
        } else {
          return false
        }
      })
    },
    handleSelectType(index, indexPath) {
      this.form.Type_Id = index
      this.visible = false
    },
    getPlanType(typeid, types) {
      let ntype
      let type = types.find(t => t.Id === typeid)
      if (type) {
        ntype = type
      } else {
        for (let i = 0; i < types.length; i++) {
          if (types[i].Children && types[i].Children.length > 0) {
            type = this.getPlanType(typeid, types[i].Children)
            if (type && JSON.stringify(type) !== '{}') {
              ntype = type
              break
            }
          }
        }
      }
      return ntype ?? {}
    },
    toDateStr(v) {
      if (!v) return ''
      return moment(v)
        .startOf('date')
        .format('YYYY-MM-DD')
    },
    getUsers(ids) {
      console.log(ids)
      const users = this.members.filter(m => ids.indexOf(m.User_Id) > -1)
      return users.map(u => u.UserName).join(',')
    },
    canEdit() {
      if (
        this.role.check(BGT.PlanAuth.ACCESSES.WBS | BGT.PlanAuth.ACCESSES.WBS)
      ) {
        return true
      }
      return false
    }
  }
}
</script>
