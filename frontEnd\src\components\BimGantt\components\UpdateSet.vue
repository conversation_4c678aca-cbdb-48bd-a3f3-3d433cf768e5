<template>
  <div class="update-set">
    <div class="title">{{ plan.Name }}</div>
    <el-form ref="form" :model="form" label-width="140px">
      <el-form-item label="填报截止时间">
        <div v-if="editmode" class="timer-group">
          <el-date-picker
            v-model="form.date"
            style="width:160px;"
            type="date"
            format="yyyy-MM-dd"
            placeholder="选择日期"
          />
          <el-time-select
            v-model="form.time"
            style="width:120px;"
            :picker-options="{
              start: '08:30',
              step: '00:15',
              end: '18:30'
            }"
            placeholder="选择时间"
          />
        </div>
        <span v-else> - </span>
      </el-form-item>
      <el-form-item label="数据日期">
        <el-date-picker
          v-if="editmode"
          v-model="form.data_date"
          style="width:284px;"
          type="date"
          format="yyyy-MM-dd"
          placeholder="选择日期"
        />
        <span v-else>{{
          moment(plan.Cur_Data_Date).format('YYYY-MM-DD')
        }}</span>
      </el-form-item>
      <el-form-item align="right">
        <el-button @click="$emit('dialogCancel')">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import * as moment from 'moment'
export default {
  name: 'UpdateSet',
  props: {
    plan: {
      type: Object,
      default: () => ({})
    },
    editMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {},
      editmode: false
    }
  },
  created() {
    this.editmode = this.editMode
  },
  methods: {
    submit() {
      if (!this.editmode) return this.$emit('dialogCancel')
      this.$emit('dialogFormSubmitSuccess', {
        type: 'setUpdatePlan',
        data: this.form
      })
    },
    moment(v) {
      return moment(v)
    }
  }
}
</script>
<style lang="scss" scoped>
.update-set {
  margin-top: -12px;
  .title {
    font-size: 1.6em;
    font-weight: bold;
    padding: 0 16px;
    margin-bottom: 16px;
  }
  .timer-group {
    display: flex;
    flex-direction: row;
    .el-date-editor {
      margin-right: 4px;
    }
  }
}
</style>
