<template>
  <div
    v-loading="loading"
    element-loading-background="rgba(255, 255, 255, 0.4)"
    class="bim-gantt"
    :element-loading-text="loadingStr"
  >
    <header>
      <el-row :gutter="20">
        <el-col :span="14">
          <div class="proj-title">
            <div class="flag">
              <i class="iconfont icon-gantt" />
            </div>
            <h3>{{ plan.Name }}</h3>
            <el-link
              class="set-icon"
              :underline="false"
              type="primary"
              icon="el-icon-setting"
              @click="onOpenPlanSet"
            />
          </div>
        </el-col>
        <el-col :span="10" style="text-align:right;padding-right:20px;">
          <span
            v-if="plan.Modify_Date"
            style="display:inline-block;margin-right:20px;color:#AAA;font-size:.9em;"
            ><i class="iconfont icon-check-circle" /> 最近保存:{{
              moment(plan.Modify_Date).format('HH:mm')
            }}</span
          >
          <el-button
            v-if="editMode && canHandlePlanStatus() && !plan.Is_Main_Plan"
            type="warning"
            @click="onPlanImport"
            >导入</el-button
          >
          <el-dropdown
            v-if="role.check(ACCESSES.WBS)"
            style="margin-right:12px;margin-left:12px;"
            trigger="click"
            @command="onExportChange"
          >
            <el-button type="success">导出</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="PNG">导出为PNG</el-dropdown-item>
              <el-dropdown-item command="PDF">导出为PDF</el-dropdown-item>
              <el-dropdown-item command="Excel">导出为Excel</el-dropdown-item>
              <el-dropdown-item command="ICal">导出为ICal</el-dropdown-item>
              <el-dropdown-item command="JSON">导出为JSON</el-dropdown-item>
              <el-dropdown-item command="MSProject"
                >导出为MSProject</el-dropdown-item
              >
              <el-dropdown-item command="PrimaveraP6"
                >导出为PrimaveraP6</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            v-if="editMode"
            ref="btn_save"
            type="primary"
            @click="savePlan(0)"
            >保存</el-button
          >
          <el-button
            v-if="editMode && canHandlePlanStatus() && needApprove === true"
            type="primary"
            style="padding-left:32px;"
            @click="savePlan(1)"
          >
            <i
              class="iconfont icon-stamp"
              style="margin-left:-20px;margin-top:-3px;"
            />
            提交{{ plan.Is_Main_Plan ? '审核' : '发布' }}</el-button
          >
          <el-button
            v-if="editMode && canHandlePlanStatus() && needApprove === false"
            type="primary"
            style="padding-left:32px;"
            @click="savePlan(2)"
          >
            <i
              class="iconfont icon-stamp"
              style="margin-left:-20px;margin-top:-3px;"
            />
            发布</el-button
          >
          <el-button
            v-if="
              role.check(ACCESSES.WBS) &&
                plan.Status === '2' &&
                plan.Is_Main_Plan
            "
            type="primary"
            @click="savePlan(2, true)"
          >
            提交至运营</el-button
          >
        </el-col>
      </el-row>
    </header>
    <div class="toolbar">
      <div class="flex-toolbar">
        <div class="toolbar-group">
          <el-button
            v-if="editMode && ['1', '2'].indexOf(plan.Status) < 0"
            type="primary"
            size="mini"
            icon="el-icon-plus"
            @click="onAddTask"
            >新增</el-button
          >
          <el-button
            v-if="editMode && ['1', '2'].indexOf(plan.Status) < 0"
            type="danger"
            size="mini"
            icon="el-icon-minus"
            @click="onDeleteTask"
            >删除</el-button
          >
          <el-button
            v-if="editMode && wbsMode"
            type="success"
            size="mini"
            icon="el-icon-set-up"
            @click="onWbsModeChange('task')"
            >作业编制</el-button
          >
          <el-button
            v-if="
              editMode && !wbsMode && (role.check(ACCESSES.WBS) || !plan.Id)
            "
            type="warning"
            size="mini"
            icon="iconfont icon-org-vertical"
            @click="onWbsModeChange('project')"
            >WBS编制</el-button
          >
          <el-button
            type="success"
            size="mini"
            icon="iconfont icon-edit-file"
            @click="onDetailOpen"
            >详情</el-button
          >
        </div>
        <div class="toolbar-group">
          <div class="tool-item">
            <span>大纲级别</span>
            <el-slider
              v-model="currentLvl"
              size="mini"
              :step="1"
              :max="maxDeep"
              :min="0"
              :show-tooltip="false"
              style="width:150px;"
              @change="onLevelChange"
            />
          </div>
          <div class="tool-item">
            <template>
              <span>前锋线</span>
              <el-switch
                v-model="showFowardLine"
                @change="onToggleFowardLine"
              />
            </template>
          </div>
          <div class="tool-item">
            <span>聚光灯</span>
            <el-switch v-model="showSpotLight" @change="onToggleSpotLight" />
          </div>
        </div>
        <div class="toolbar-group">
          <div class="tool-item">
            <el-input
              v-model="keyword"
              placeholder="检索作业/责任人"
              size="mini"
              suffix-icon="el-icon-search"
              style="border-width:0px !important;width:160px;"
            />
          </div>
          <div class="tool-item">
            <el-tooltip
              class="item"
              effect="light"
              content="筛选作业"
              placement="top-start"
            >
              <el-badge
                is-dot
                :hidden="gantFilters.checkers.length <= 0"
                style="margin-left:6px;"
                :class="{ hasfilter: gantFilters.checkers.length > 0 }"
              >
                <el-link
                  :underline="false"
                  icon="iconfont icon-filter-filled"
                  @click="onFilterTasksSet"
                />
              </el-badge>
            </el-tooltip>
          </div>
        </div>
        <div class="toolbar-group">
          <template
            v-if="
              role.check(ACCESSES.WBS) || $store.state.user.account == 'zhh'
            "
          >
            <div class="tool-item">
              <el-tooltip
                class="item"
                effect="light"
                content="人力资源分析"
                placement="top-start"
              >
                <el-link
                  :underline="false"
                  icon="iconfont icon-chart-bar"
                  @click="onAnalyzerOpen"
                />
              </el-tooltip>
            </div>
            <div class="tool-item">
              <el-tooltip
                v-if="editMode || $store.state.user.account == 'zhh'"
                class="item"
                effect="light"
                content="进度计算"
                placement="top-start"
              >
                <el-link
                  :underline="false"
                  icon="iconfont icon-calculate"
                  @click="onScheduleOpen"
                />
              </el-tooltip>
            </div>
          </template>
          <div class="tool-item">
            <el-tooltip
              v-if="plan.Id"
              class="item"
              effect="light"
              content="更新历史"
              placement="top-start"
            >
              <el-link
                :underline="false"
                icon="el-icon-time"
                @click="onHistoryOpen"
              />
            </el-tooltip>
          </div>
          <template v-if="role.check(ACCESSES.WBS)">
            <div class="tool-item">
              <el-dropdown trigger="click" @command="onCommandChange">
                <el-tooltip
                  class="item"
                  effect="light"
                  content="更多操作"
                  placement="top-start"
                >
                  <el-link
                    :underline="false"
                    icon="iconfont icon-more-vertical"
                  />
                </el-tooltip>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="calendar"
                    >日历设置</el-dropdown-item
                  >
                  <el-dropdown-item v-if="plan.Id" command="target"
                    >目标计划</el-dropdown-item
                  >
                  <el-dropdown-item command="updateset"
                    >填报任务设置</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </div>
        <div class="toolbar-group">
          <div class="tool-item">
            <el-tooltip
              class="item"
              effect="light"
              content="缩小时间线"
              placement="top-start"
            >
              <el-link
                :underline="false"
                icon="el-icon-zoom-out"
                @click="onZoom(-1)"
              />
            </el-tooltip>
          </div>
          <div class="tool-item">
            <el-tooltip
              class="item"
              effect="light"
              content="放大时间线"
              placement="top-start"
            >
              <el-link
                :underline="false"
                icon="el-icon-zoom-in"
                @click="onZoom(1)"
              />
            </el-tooltip>
          </div>
          <div class="tool-item">
            <el-tooltip
              class="item"
              effect="light"
              content="显示全部"
              placement="top-start"
            >
              <el-link
                :underline="false"
                icon="iconfont icon-compress2"
                @click="onFixAll"
              />
            </el-tooltip>
          </div>
          <div class="tool-item">
            <el-tooltip
              class="item"
              effect="light"
              content="切换全屏"
              placement="top-start"
            >
              <el-link
                :underline="false"
                icon="iconfont icon-expand"
                @click="onFullScreen"
              />
            </el-tooltip>
          </div>
          <div class="tool-item">
            <el-popover
              placement="bottom"
              title="图例"
              width="200"
              trigger="click"
            >
              <ul class="gantt-leggend">
                <li>
                  <span :class="['project', 'rect']" />
                  <label>WBS</label>
                </li>
                <li>
                  <span :class="['plan', 'rect']" />
                  <label>计划作业</label>
                </li>
                <li>
                  <span :class="['key', 'rect']" />
                  <label>关键作业</label>
                </li>
                <li>
                  <span :class="['diamond']"> <i /></span>
                  <label>里程碑</label>
                </li>
                <li>
                  <span :class="['actual', 'project', 'rect']" />
                  <label>实际WBS</label>
                </li>
                <li>
                  <span :class="['actual', 'rect']" />
                  <label>实际作业</label>
                </li>
                <li>
                  <span :class="['target', 'rect']" />
                  <label>目标作业</label>
                </li>
                <li>
                  <span :class="['target', 'diamond']"> <i /></span>
                  <label>目标里程碑</label>
                </li>
              </ul>
              <el-tooltip
                slot="reference"
                class="item"
                effect="light"
                content="图例"
                placement="top-start"
              >
                <el-link
                  :underline="false"
                  icon="el-icon-warning-outline"
                  @click="toggleInfo"
                />
              </el-tooltip>
            </el-popover>
          </div>
        </div>
      </div>
    </div>

    <div class="gantt-container">
      <div
        id="gantt-chart"
        ref="gantt"
        :class="{
          hasbaseline: showBaseLine,
          hasfowardline: showFowardLine,
          'has-bottom-drawer': drawerShow && drawerCfgs.direction == 'btt'
        }"
      />
    </div>
    <el-dialog
      :title="dialogCfgs.title"
      :visible.sync="dialogShow"
      :width="dialogCfgs.width"
      destroy-on-close
    >
      <keep-alive>
        <component
          :is="dialogCfgs.component"
          v-if="dialogShow"
          :name="dialogCfgs.title"
          v-bind="dialogCfgs.props"
          @dialogCancel="dialogCancel"
          @dialogFormSubmitSuccess="dialogFormSubmitSuccess"
          @restoreDefault="restoreDefault"
        />
      </keep-alive>
    </el-dialog>
    <el-drawer
      ref="drawer"
      :class="{
        gantdrawer: true,
        ismodal: drawerCfgs.modal,
        history: drawerProps.drawclass === 'history' && drawerShow,
        taskdetail:
          (drawerProps.drawclass === 'taskdetail' ||
            drawerProps.drawclass === 'analyzer-detail') &&
          drawerShow
      }"
      :style="{
        height:
          (drawerCfgs.direction == 'btt' || drawerCfgs.direction == 'ttb') &&
          drawerCfgs.size
            ? drawerCfgs.size
            : '100%',
        width:
          (drawerCfgs.direction == 'ltr' || drawerCfgs.direction == 'rtl') &&
          drawerCfgs.size
            ? drawerCfgs.size
            : '100%'
      }"
      :title="drawerCfgs.title"
      :size="drawerCfgs.size"
      :visible.sync="drawerShow"
      :modal="drawerCfgs.modal"
      :wrapper-closable="drawerCfgs.wrapperClosable"
      :modal-append-to-body="drawerCfgs.modalAppendToBody"
      :direction="drawerCfgs.direction"
      :with-header="drawerCfgs.withHeader"
      destroy-on-close
      @closed="
        canOpenNewDrawer = true
        drawerShow = false
      "
      @opened="canOpenNewDrawer = false"
    >
      <keep-alive>
        <component
          :is="drawerCfgs.component"
          v-if="drawerShow"
          :ref="drawerCfgs.component"
          v-bind="drawerCfgs.props"
          @drawerCancel="drawerCancel"
          @drawerContentUpdate="drawerContentUpdate"
        />
      </keep-alive>
    </el-drawer>
  </div>
</template>
<script>
import 'dhtmlx-gantt/codebase/dhtmlxgantt.css'
import './index.scss'
import {
  CONSTRAINT_TYPES,
  GetUserListByObjId,
  GetPlanFieldSetting,
  SavePlanFieldSetting,
  SavePlan,
  DeletePlan,
  CopyPlan,
  ChangePlanStatus,
  GetPlanEntity,
  GetPlanUpdateList,
  SendTaskInputMessage,
  CheckNeedApprovePlan
} from '@/api/plan/index'
import { GetPlanTypeTree } from '@/api/plan/plan-type'
import PlanAddDialog from './components/PlanAddDialog'
import CalendarSet from './components/CalendarSet'
import TaskDetail from './components/TaskDetail'
import GridSet from './components/GridSet'
import TargetSet from './components/TargetSet'
import UpdateSet from './components/UpdateSet'
import FilterSet from './components/FilterSet'
import ScheduleDialog from './components/ScheduleDialog'
import PlanImport from './components/PlanImport'
import PlanHistory from './components/PlanHistory'
import ResourceAnalyzer from './components/ResourceAnalyzer'
import * as BGT from './index'
import { GantFilters, PlanAuth } from './util'
import { arrayToTree } from '@/utils/treeTranfer'
import { CheckExistFlow } from '@/api/sys/flow'
import * as moment from 'moment'
import { ep1, ep2 } from './api'
import TagBack from '@/mixins/tag-back/index'

export default {
  name: 'BimGanttDemo',
  components: {
    PlanAddDialog,
    CalendarSet,
    TaskDetail,
    GridSet,
    TargetSet,
    PlanImport,
    UpdateSet,
    FilterSet,
    ScheduleDialog,
    PlanHistory,
    ResourceAnalyzer
  },
  mixins: [TagBack],
  props: {
    plan: {
      type: Object,
      default: () => ({})
    },
    editmode: {
      type: Boolean,
      default: true
    },
    needApprovePlan: {
      // 是否需要审核计划
      type: Boolean,
      default: false
    },
    existFlow: {
      // 是否已配置审核流程
      type: Boolean,
      default: false
    },
    extend: {
      // 是否具有附加的抽屉tab
      type: Boolean,
      default: false
    },
    /**
     * 扩展无副作用属性，通过 :extend-fields="[]" 开启
     * label 为表单标签显示名
     * code 表示在作业对象的属性名称
     * type 支持： text - 对应普通 input / longtext - 对应 textarea / number - 对应数值输入框 / date - 对应日期，值格式如 1970-01-01
     * 如：[{label:'姓名', code:'name', type:'text'}]
     */
    extendfields: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      loadingStr: '',
      editMode: true,
      wbsMode: false,
      showBaseLine: false,
      showFowardLine: false,
      showSpotLight: false,
      spot_range: {}, // 聚光灯范围
      fowards: {
        // 前锋线相关图层id
        overlay: ''
      },
      legendShow: false, // 图例显示
      needApprove: false,
      flowReady: false,
      dialogShow: false,
      dialogCfgs: {},
      drawerShow: false,
      canOpenNewDrawer: true,
      drawerCfgs: {
        direction: 'btt',
        title: '',
        size: '260px',
        modal: false,
        wrapperClosable: false,
        modalAppendToBody: false,
        withHeader: true,
        component: ''
      },
      drawerProps: {},
      planTypes: [],
      members: [],
      gantt: null,
      reshecdeledData: null,
      /**
       * FILTERS 例子
        new BGT.GantFilters([{
            type:'field',
            field:'type',
            value:'project'
          },{
            type:'daterange',
            value:['2020-01-01','2021-12-31']
          },{
            type:'keyword',
            value:'xxx'
          }], 'ONE')
       */
      gantFilters: new GantFilters([], 'ONE'),
      keyword: '', //
      AllSettings: [], // 所有可用列
      AvailableSettings: [], // 用户选用列
      fromData: [], //
      maxDeep: 10, // 大纲最大深度
      currentLvl: 0, //
      foldedTask: false, // 是否折叠task类型
      saveParam: {
        isSaveHistory: false
      }
    }
  },
  computed: {
    role() {
      return new BGT.PlanAuth(this.plan.Plan_Auth)
    },
    ACCESSES() {
      return BGT.PlanAuth.ACCESSES
    },
    ganttData() {
      console.log('recomputed gantt data...')
      const d = this.plan?.Plan_Data || { data: [], links: [] }
      return d
    },
    ganttColumns() {
      const cols = this.AvailableSettings.map(s => {
        const col = {
          label: s.Label,
          align: ['text', 'remark'].indexOf(s.Code) > -1 ? 'left' : 'center',
          name: s.Code,
          hide: false,
          resize: true,
          tree: s.Code === 'text',
          Display_Name: s.Label
        }
        if (s.Code === 'Responsible_User') {
          col.template = t => {
            return (
              this.members.find(u => u.User_Id === t['Responsible_User'])
                ?.UserName ?? ''
            )
          }
        }
        if (s.Data?.Width !== 0) {
          col.min_width = s.Data.Width
        } else {
          if (s.Code === 'text') {
            col.min_width = 200
            col.max_width = 360
          }
        }
        if (s.Code === 'text') {
          col.label = `<div id="wbs2task"><span class="tag ${
            this.foldedTask ? 'wbs' : 'task'
          }"><span style="pointer-events: none;">${
            this.foldedTask ? 'W' : '作'
          }</span></span>${col.Display_Name}</div>`
        }

        if (s.Code === 'Dynamic_Start_Date' || s.Code === 'Dynamic_End_Date') {
          col.template = t => {
            let text = t[s.Code]
            // 具有运营计划时间
            if (t.Control_Plan_Start_Date && t.Control_Plan_End_Date) {
              if (
                // 动态结束晚于计划运营结束时间
                moment(t.Control_Plan_End_Date).isBefore(
                  t['Dynamic_End_Date'].split('A')[0]
                )
              ) {
                text = `<span style="font-weight:bold;color:#e63030;">${text}</span>`
              }
            }
            return text
          }
        }

        if (
          (s.Code.indexOf('progress') > -1 ||
            s.Code.indexOf('Progress') > -1) &&
          s.Code !== 'Laster_Progress_Date'
        ) {
          col.template = t => {
            return (t[s.Code] * 100).toFixed(2) + '%'
          }
        }
        return col
      })
      console.log(cols)
      return cols
    }
  },
  watch: {
    $route(nv) {
      const ganttTip = document.querySelector('.gantt_tooltip')
      if (ganttTip) ganttTip.remove()
    },
    plan() {
      this.buildGantt()
    },
    wbsMode(nv, ov) {
      this.gantt?.unselectTask()
      if (nv === true) {
        this.gantFilters.checkers.push({
          type: 'field',
          field: 'type',
          value: 'project'
        })
      } else {
        this.gantFilters.checkers = this.gantFilters.checkers.filter(chk => {
          return chk.field !== 'type' || chk.value !== 'project'
        })
      }
      this.gantt?.refreshData()
      this.reRenderGantt()
    },
    drawerShow(nv) {
      this.$nextTick(() => {
        this.gantt?.render()
      })
    },
    keyword(nv) {
      const keywordChecker = this.gantFilters.checkers.find(
        c => c.type === 'keyword'
      )
      if (nv) {
        if (!keywordChecker) {
          this.gantFilters.checkers.push({
            type: 'keyword',
            value: nv
          })
        } else {
          keywordChecker.value = nv
        }
      } else {
        this.gantFilters.checkers = this.gantFilters.checkers.filter(chk => {
          return chk.type !== 'keyword'
        })
      }
      this.gantt?.refreshData()
      this.reRenderGantt()
    }
  },
  created() {
    this.needApprove = this.needApprovePlan
    this.flowReady = this.existFlow
    // props初始化 editMode
    this.editMode = this.editmode
    // 角色验证
    if (
      this.plan.Id &&
      this.editmode &&
      !this.role.check(BGT.PlanAuth.ACCESSES.EDIT)
    ) {
      this.editMode = false
    }

    // 获取计划类型
    GetPlanTypeTree(this.$store.state.user.Last_Working_Object_Id).then(res => {
      if (res.IsSucceed) {
        this.planTypes = res.Data
      }
    })
    // 获取项目成员
    GetUserListByObjId(this.$store.getters.Last_Working_Object_Id, {
      Page: 1,
      PageSize: 1000
    }).then(res => {
      if (res.IsSucceed) {
        this.members = res.Data.Data
      }
    })

    CheckExistFlow({ webId: 'PlanView' }).then(res => {
      this.flowReady = res.Data
    })
    CheckNeedApprovePlan().then(res => {
      this.needApprove = res.Data
    })
  },
  mounted() {
    this.buildGantt()
    this.bindEvents()
    // 导出功能挂载
    ep1()
    ep2()
  },
  methods: {
    reRenderGantt() {
      if (this.showFowardLine) {
        this.removeFowardLine()
        this.drawFowardLine()
      }
      this.gantt?.render()
      if (this.showSpotLight) {
        setTimeout(() => {
          this.bindSpotMarkerEvents(
            document.querySelector('[data-marker-id=spot-marker]'),
            this.gantt.getMarker('spot-marker')
          )
        }, 30)
      }
    },
    onAfterTaskMove(id, parent, tindex) {
      this.reRenderGantt()
    },
    // 处理父级ParentNodes以及Id
    changeId(arr) {
      arr.map(item => {
        item.id = item.Id
        if (item.ParentNodes === '0') {
          item.ParentNodes = 0
        }
        delete item.Id
        if (item.Children) {
          this.changeId(item.Children)
        }
      })
    },
    /**
     * 获取计划栏位
     * plan_id 随意设置其它值获取默认，正确值获取计划栏位
     * 不能为空
     */
    loadUserColumns(plan_id, is_default) {
      return GetPlanFieldSetting(plan_id, is_default)
    },
    changeData(arr1, arr2) {
      let leftArr = []
      const rightArr = []
      arr2.map(item => {
        rightArr.push(item)
        // rightArr.push(...item.Children)
      })
      arr1.map(item => {
        leftArr.push(...item.Children)
        delete item.Children
        leftArr.push(item)
      })
      rightArr.map(i => {
        leftArr.map((item, index) => {
          if (item.Label === i.Label && item.ParentNodes === i.ParentNodes) {
            leftArr.splice(index, 1)
          }
        })
      })
      leftArr = arrayToTree(leftArr, {
        id: 'id',
        pid: 'ParentNodes',
        children: 'Children'
      })
      this.fromData = leftArr.filter(item => {
        return item.Children.length !== 0
      })
    },
    buildGantt() {
      console.log(this.plan)
      this.fowards = {
        overlay: ''
      }
      this.showFowardLine = false

      this.loadUserColumns(this.plan.Id || 'X', false).then(res => {
        if (res.IsSucceed) {
          const Data = JSON.parse(JSON.stringify(res.Data.AllSettings))
          this.changeId(Data)
          this.AvailableSettings = res.Data.AvailableSettings
          this.changeId(this.AvailableSettings)
          this.AllSettings = Data
          this.changeData(Data, this.AvailableSettings)

          if (
            this.plan.Target_Plan_Id !== null &&
            this.plan.Target_Plan_Id !== undefined &&
            this.plan.Target_Plan_Id !== ''
          ) {
            this.showBaseLine = true
          } else {
            this.showBaseLine = false
          }

          console.log(this.showBaseLine)

          BGT.createGanttInstance(this, {
            el: 'gantt-chart',
            locale: 'cn',
            calendar: this.plan.Plan_Calendar,
            filters: this.gantFilters,
            gridColumns: this.ganttColumns,
            editMode: this.editMode,
            showBaseLine: this.showBaseLine,
            start: this.plan.Plan_Start_Date
          })

          if (!this.plan.Id && this.gantt.getTaskByTime().length <= 0) {
            // 新建计划时默认增加一条wbs
            BGT.createNewTask(this.gantt, this.plan, {
              parent: '0',
              type: 'project',
              text: this.plan.Name
            })
          }
        }
      })
    },
    bindEvents() {
      this.$el.addEventListener('click', e => {
        const btnGridSet = this.$el.querySelector('#column-set')
        const btnWbs2task = this.$el.querySelector('#wbs2task>.tag')
        if (e.target == btnGridSet) {
          this.onColumnsSetOpen()
        } else if (e.target == btnWbs2task) {
          const isTask = Array.from(e.target.classList).indexOf('task') > -1
          if (isTask) {
            e.target.classList.remove('task')
            e.target.classList.add('wbs')
            e.target.querySelector('span').innerText = 'W'
            this.foldedTask = true
          } else {
            e.target.classList.remove('wbs')
            e.target.classList.add('task')
            e.target.querySelector('span').innerText = '作'
            this.foldedTask = false
          }

          const column = this.gantt.getGridColumn('text')
          column.label = `<div id="wbs2task"><span class="tag ${
            this.foldedTask ? 'wbs' : 'task'
          }"><span style="pointer-events: none;">${
            this.foldedTask ? 'W' : '作'
          }</span></span>${column.Display_Name}</div>`

          // return
          const showType = isTask ? 'project' : 'task'
          this.foldTaskType(showType)

          this.reRenderGantt()
          this.$forceUpdate()
        }
      })
      // RESIZE
      window.addEventListener('resize', e => {
        if (this.showFowardLine) {
          this.reRenderGantt()
        }
      })
    },
    // 折叠树类型
    foldTaskType(showType) {
      this.gantt.batchUpdate(() => {
        var tasks = this.gantt.getTaskByTime()
        for (var i = 0; i < tasks.length; i++) {
          var task = tasks[i]
          task.$open = true
          if (showType === 'project') {
            const childs = tasks.filter(t => t.parent === task.id)
            // 折叠 project 类,并且无project类子元素
            if (
              task.type == 'project' &&
              !childs.find(c => c.type == 'project')
            ) {
              task.$open = false
            }
          }
          this.gantt.updateTask(task.id)
        }
      })
    },
    // 折叠树层级
    foldTaskLevel(level) {
      // 批量更新效率较高
      this.gantt.batchUpdate(() => {
        var tasks = this.gantt.getTaskByTime()
        for (var i = 0; i < tasks.length; i++) {
          var task = tasks[i]
          task.$open = true
          if (task.$level > level - 1) {
            task.$open = false
          }
          this.gantt.updateTask(task.id)
        }
      })
      this.reRenderGantt()
    },
    savePlan(saveModel, isSubmitControl, isTargetPlan) {
      // console.log('Plan 属性：', this.plan)
      // console.log('Gantt 数据：', this.gantt.serialize())
      console.log(this.plan.Cur_Data_Date)
      if (!this.plan.Plan_End_Date) {
        return this.$message.warning('当前计划无计划完成时间，请先完善计划信息')
      }
      this.plan.Dynamic_Duration =
        Math.abs(
          this.gantt.calculateDuration({
            start_date: moment(
              this.plan.Actual_Start_Date || this.plan.Plan_Start_Date
            ).toDate(),
            end_date: moment(
              this.plan.Actual_End_Date || this.plan.Plan_End_Date
            ).toDate()
          })
        ) + 1
      console.log(this.plan)
      // return
      if (saveModel == '1' && this.plan.Is_Main_Plan && !this.flowReady) {
        this.$message({
          type: 'error',
          message: '尚未配置审核流程！'
        })
        return
      }
      this.loading = true
      const gdata = this.gantt.serialize()
      gdata.data.forEach(t => {
        Object.keys(t).forEach(k => {
          if (k.indexOf('_Date') > -1 || k.indexOf('_date') > -1) {
            if (!t[k]) t[k] = null
          }
        })
      })
      const postObject = {
        ...this.plan,
        Plan_Data: gdata,
        Observer: JSON.stringify(this.plan.Observer),
        Admin: JSON.stringify(this.plan.Admin)
      }
      console.log('111', postObject)
      Object.keys(postObject).forEach(k => {
        if (
          ['Plan_End_Date', 'Plan_Start_Date', 'Cur_Data_Date'].indexOf(k) > -1
        ) {
          postObject[k] = postObject[k]
            ? moment(postObject[k])
                .startOf('date')
                .format('YYYY-MM-DD')
            : null
        }
        if (k.indexOf('_Date') > -1 || k.indexOf('_date') > -1) {
          if (!postObject[k]) postObject[k] = null
        }
      })
      console.log('222', postObject)

      let isCollaboration = false
      if (this.plan.Id && this.plan.Plan_Auth === '参与') {
        isCollaboration = true
      }
      // return
      return SavePlan(
        {},
        {
          planModel: postObject,
          webfromId: 'PlanView',
          saveModel: saveModel,
          isSubmitControl: isSubmitControl ?? '',
          isCollaboration: isCollaboration,
          isSaveHistory: this.saveParam.isSaveHistory,
          isTargetPlan: isTargetPlan ?? false
        }
      )
        .then(res => {
          if (res.IsSucceed) {
            this.$message.success(res.Message)
            if (this.saveParam.isSaveHistory) {
              this.saveParam.isSaveHistory = false
            }
            if (!this.plan.Id) {
              this.plan.Id = res.Data
              this.$router.push('/plan/edit/' + this.plan.Id).then(() => {
                this.$store.dispatch(
                  'tagsView/delView',
                  this.$store.state.tagsView.visitedViews.splice(
                    this.$store.state.tagsView.visitedViews.length - 2,
                    1
                  )
                )
              })
            }
            if (isTargetPlan) {
              return res
            }
            if (saveModel == '1' || saveModel == '2' || saveModel == '3') {
              this.tagBack()
            }
          } else {
            this.$message.warning(res.Message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    onAddTask() {
      const add = this.canAddTaskInfo()
      if (!add.can) {
        return this.$message.warning(add.msg)
      }
      const t = BGT.createNewTask(this.gantt, this.plan, {
        type: this.wbsMode ? 'project' : 'task',
        parent: this.gantt.getSelectedId() || '0'
      })
      this.gantt.open(t.id)
      if (t.parent && t.parent !== '0') {
        this.gantt.open(this.gantt.getSelectedId())
      }
      this.reRenderGantt()
    },

    onDeleteTask() {
      const selected = this.gantt.getSelectedId()
      if (!selected) {
        return this.$message.warning('当前没有选中的作业')
      }
      const task = this.gantt.getTask(selected)
      const del = this.canDelTaskInfo(task)
      if (!del.can) {
        return this.$message.warning(del.msg)
      }
      this.$confirm(
        `如果 [${task.text}] 具有子作业，也将被同时删除。是否确定继续删除?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.gantt.deleteTask(selected)
          this.reRenderGantt()
        })
        .catch(() => {})
    },
    onWbsModeChange(taskType) {
      console.log(taskType)
      this.wbsMode = taskType === 'project'
    },
    onColumnsSetOpen() {
      BGT.openDialog(
        {
          title: '栏位设置',
          width: '750px',
          component: 'GridSet',
          props: {
            origin: this.AvailableSettings,
            all: this.fromData,
            oall: this.AllSettings,
            gantt: this.gantt
          }
        },
        this
      )
    },
    onDetailOpen() {
      const id = this.gantt.getSelectedId()
      if (!id) return this.$message.warning('当前没有选中的任务')
      const task = this.gantt.getTask(id)
      console.log(task)
      // 更新props
      console.log('update drawer props...')
      this.drawerProps = {
        drawclass: 'taskdetail',
        tabname: 'profile',
        taskid: id,
        planid: this.plan.Id,
        plan: this.plan,
        task: task,
        constraints: CONSTRAINT_TYPES,
        members: this.members,
        gantt: this.gantt,
        editMode: this.editMode,
        canEditWBS: this.canEditWBS(task),
        canEditTask: this.canEditTask(task),
        canUpdateProgress: this.canUpdateProgress(task),
        extend: this.extend,
        extendfields: this.extendfields
      }

      this.drawerCfgs.props = this.drawerProps
      if (!this.$refs['TaskDetail']) {
        // 详情会挤压甘特图，重新绘制
        setTimeout(() => {
          this.reRenderGantt()
        }, 60)
        // 当前具有打开的详情面板
        BGT.openDrawer(
          {
            title: '查看工作任务',
            withHeader: false,
            component: 'TaskDetail',
            direction: 'btt',
            wrapperClosable: true,
            size: '260px',
            props: this.drawerCfgs.props
          },
          this
        )
      }
    },
    onTaskDblClick(id, event) {
      this.onDetailOpen()
    },
    onTaskUnselected() {
      if (
        (this.$refs['TaskDetail'] || this.$refs['ResourceAnalyzer']) &&
        this.drawerShow
      ) {
        this.drawerCancel()
      }
    },
    onTaskSelected(id) {
      if (this.$refs['TaskDetail'] && this.drawerShow) {
        this.onDetailOpen()
      }
    },
    onHistoryOpen() {
      if (!this.plan.Id) return

      // 更新props
      console.log('update drawer props...')
      this.drawerProps = {
        plan: this.plan,
        begin_date: '',
        end_date: '',
        drawclass: 'history'
      }
      this.drawerCfgs.props = this.drawerProps
      BGT.openDrawer(
        {
          title: '历史版本查看',
          withHeader: true,
          component: 'PlanHistory',
          direction: 'rtl',
          wrapperClosable: true,
          size: '320px',
          props: this.drawerCfgs.props
        },
        this
      )
    },
    toggleInfo() {
      this.legendShow = !this.legendShow
    },
    onCommandChange(cmd) {
      switch (cmd) {
        case 'calendar':
          this.onOpenCalendarSet()
          break
        case 'target':
          this.onOpenTargetSet()
          break
        case 'updateset':
          this.onOpenUpdateSet()
          break
      }
    },
    onExportChange(cmd) {
      console.log(cmd)
      if (this.gantt && this.gantt['exportTo' + cmd]) {
        const opts = {}
        if (cmd === 'Excel') {
          opts.columns = this.ganttColumns.map(c => ({
            id: c.name,
            header: c.name === 'text' ? '作业名称' : c.label,
            type:
              [
                'Actual_Progress',
                'Start_Difference',
                'End_Difference',
                'Duration_Difference',
                'Needed_Duration',
                'Plan_Duration',
                'Actual_Duration',
                'Target_Duration',
                'Dynamic_Duration',
                'Actual_Resources',
                'Difference',
                'Free_Float',
                'Old_Actual_Progress',
                'Plan_Resources',
                'Target_Resources',
                'Show_Duration',
                'Show_Progress',
                'Total_Float'
              ].indexOf(c.name) > -1
                ? 'number'
                : 'string'
          }))
        }
        console.log(opts)
        this.gantt['exportTo' + cmd](opts)
      }
    },
    onOpenCalendarSet() {
      BGT.openDialog(
        {
          title: '计划日历设置',
          width: '950px',
          component: 'CalendarSet',
          props: {
            origin: this.plan.Plan_Calendar,
            editMode: this.editMode && ['1', '2'].indexOf(this.plan.Status) < 0
          }
        },
        this
      )
    },
    onOpenTargetSet() {
      BGT.openDialog(
        {
          title: '目标计划设置',
          width: '560px',
          component: 'TargetSet',
          props: {
            plan: this.plan,
            editMode: this.editMode
          }
        },
        this
      )
    },
    onOpenUpdateSet() {
      BGT.openDialog(
        {
          title: '填报任务设置',
          width: '560px',
          component: 'UpdateSet',
          props: {
            plan: this.plan,
            editMode: this.editMode
          }
        },
        this
      )
    },
    onZoom(direct) {
      if (direct > 0) {
        this.gantt.ext.zoom.zoomIn()
      } else {
        this.gantt.ext.zoom.zoomOut()
      }
      this.gantt.$zoomToFit = false
      this.reRenderGantt()
    },
    onFullScreen() {
      this.gantt?.ext.fullscreen.toggle()
    },
    onFullScreenChanged() {
      setTimeout(() => {
        this.reRenderGantt()
      }, 0)
    },
    onGridResizeEnd() {
      this.$nextTick(() => {
        this.reRenderGantt()
      })
    },
    applyConfig(config, dates) {
      this.gantt.config.scales = config.scales
      var lowest_scale = config.scales.reverse()[0]

      if (dates && dates.start_date && dates.end_date) {
        this.gantt.config.start_date = this.gantt.date.add(
          dates.start_date,
          -1,
          lowest_scale.unit
        )
        this.gantt.config.end_date = this.gantt.date.add(
          this.gantt.date[lowest_scale.unit + '_start'](dates.end_date),
          2,
          lowest_scale.unit
        )
      } else {
        this.gantt.config.start_date = this.gantt.config.end_date = null
      }

      // restore the previous scroll position
      if (config.scroll_position) {
        setTimeout(() => {
          this.gantt.scrollTo(
            config.scroll_position.x,
            config.scroll_position.y
          )
        }, 4)
      }
    },
    zoomToFit() {
      if (this.cachedSettings) {
        // console.log(this.cachedSettings)
        this.applyConfig(this.cachedSettings)
        this.cachedSettings = null
        return
      }

      var config = this.gantt.config
      this.cachedSettings = {}
      this.cachedSettings.scales = config.scales
      this.cachedSettings.start_date = config.start_date
      this.cachedSettings.end_date = config.end_date
      this.cachedSettings.scroll_position = this.gantt.getScrollState()
      // console.log(this.cachedSettings)

      var project = this.gantt.getSubtaskDates()
      var areaWidth = this.gantt.$task.offsetWidth
      var scaleConfigs = BGT.ZOOM_LEVELS

      for (var i = 0; i < scaleConfigs.length; i++) {
        var columnCount = this.getUnitsBetween(
          project.start_date,
          project.end_date,
          scaleConfigs[i].scales[scaleConfigs[i].scales.length - 1].unit,
          scaleConfigs[i].scales[0].step
        )
        if (
          (columnCount + 2) * this.gantt.config.min_column_width <=
          areaWidth
        ) {
          break
        }
      }

      if (i == scaleConfigs.length) {
        i--
      }
      this.gantt.ext.zoom.setLevel(scaleConfigs[i].name)
      this.applyConfig(scaleConfigs[i], project)
    },
    // get number of columns in timeline
    getUnitsBetween(from, to, unit, step) {
      var start = new Date(from)
      var end = new Date(to)
      var units = 0
      while (start.valueOf() < end.valueOf()) {
        units++
        start = this.gantt.date.add(start, step, unit)
      }
      return units
    },
    onFixAll() {
      this.gantt.$zoomToFit = !this.gantt.$zoomToFit
      this.zoomToFit()
      this.reRenderGantt()
    },
    onTaskDragMoveEnd(id) {
      const task = this.gantt.getTask(id)
      BGT.setTaskConstraintType(task, 'snet', this.gantt)
      BGT.setTaskConstraintDate(task, task.start_date, this.gantt)
      this.gantt.updateTask(task.id, task)
      this.reRenderGantt()
      BGT.updateParentWBS(this.gantt)
      this.$refs['TaskDetail']?.buildFormFromProp()
    },
    onTaskResizeEnd(id) {
      const task = this.gantt.getTask(id)
      BGT.setTaskSize(task)
      this.gantt.updateTask(task.id, task)
      BGT.updateParentWBS(this.gantt, task)
      this.reRenderGantt()
      this.$refs['TaskDetail']?.buildFormFromProp()
    },
    onClickDragEnd(
      startPoint,
      endPoint,
      startDate,
      endDate,
      tasksBetweenDates,
      tasksInRow
    ) {
      console.log(tasksInRow)
    },
    onFilterTasksSet() {
      BGT.openDialog(
        {
          title: '过滤器',
          width: '560px',
          component: 'FilterSet',
          props: {
            plan: this.plan,
            checkers: this.gantFilters.checkers,
            matchto: this.gantFilters.matchto
          }
        },
        this
      )
      console.log(this.gantFilters)
    },
    onPlanImport() {
      if (this.plan.Is_Main_Plan) {
        return this.$message.warning('主计划不支持导入')
      }
      const api = new URL('/Plan/Plan/ImportPlan', this.$baseUrl).href
      // return console.log(api)
      BGT.openDialog(
        {
          title: '导入',
          width: '450px',
          component: 'PlanImport',
          props: {
            action: api,
            exts: ['mpp'],
            filesize: 50
          }
        },
        this
      )
    },
    onOpenPlanSet() {
      BGT.openDialog(
        {
          title: '计划设置',
          width: '50%',
          component: 'PlanAddDialog',
          props: {
            typetree: this.planTypes,
            members: this.members,
            plan: this.plan,
            editMode: this.editMode
          }
        },
        this
      )
    },
    onScheduleOpen() {
      BGT.openDialog(
        {
          title: '进度计算',
          width: '480px',
          component: 'ScheduleDialog',
          props: {
            gantt: this.gantt,
            plan: this.plan
          }
        },
        this
      )
    },
    onAnalyzerOpen() {
      this.drawerCancel()
      this.drawerProps = {
        drawclass: 'analyzer-detail',
        plan: this.plan,
        gantt: this.gantt
      }

      this.drawerCfgs.props = this.drawerProps
      if (!this.$refs['ResourceAnalyzer']) {
        // 详情会挤压甘特图，重新绘制
        setTimeout(() => {
          this.reRenderGantt()
        }, 60)
        // 当前具有打开的详情面板
        BGT.openDrawer(
          {
            title: '人工资源分析',
            withHeader: false,
            component: 'ResourceAnalyzer',
            direction: 'btt',
            wrapperClosable: true,
            size: '260px',
            props: this.drawerCfgs.props
          },
          this
        )
      }
    },
    onLinkDblClick(id) {
      const link = this.gantt.getLink(id)
      const src = this.gantt.getTask(link.source)
      const to = this.gantt.getTask(link.target)
      this.$confirm(
        `确认要删除 [${src.text}] 和 [${to.text}] 之间的连接吗?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.gantt.deleteLink(link.id)
        })
        .catch(() => {})
    },
    onGanttScroll(left, top) {
      const area = document.querySelector('.gantt_overlay_area')
      if (this.fowards.overlay) {
        const layer = document.querySelector('.gantt_overlay')
        setTimeout(() => {
          if (layer) {
            layer.style.top = `-${area.style.top}`
          }
        }, 10)
      }
    },
    drawerCancel() {
      // 详情挤压甘特图，重新绘制
      if (this.$refs['TaskDetail']) {
        setTimeout(() => {
          this.reRenderGantt()
        }, 60)
      }
      this.$refs.drawer.closeDrawer()
    },
    drawerContentUpdate({ type, data }) {
      console.log(type, data)
      if (
        this[type] &&
        Object.prototype.toString.call(this[type]) === '[object Function]'
      ) {
        this[type](data)
      }
    },
    updateGanttTask({ gantt, task, field, value }) {
      BGT.updateGanttTask({ gantt, task, field, value })
      this.$refs['TaskDetail']?.buildFormFromProp()
      this.reRenderGantt()
    },
    updateLagDays({ gantt, link, value }) {
      const task = this.gantt.getTask(link.target)
      this.updateGanttTask({
        gantt: this.gantt,
        task,
        field: 'Plan_Start_Date',
        value: this.gantt.calculateEndDate({
          start_date: task.Plan_Start_Date,
          duration: value
        })
      })
    },
    dialogCancel() {
      this.dialogShow = false
    },
    dialogFormSubmitSuccess({ type, data }) {
      console.log(type, data)
      this.dialogCancel()
      // return
      if (
        this[type] &&
        Object.prototype.toString.call(this[type]) === '[object Function]'
      ) {
        this[type](data)
        // console.log('111',data,JSON.stringify(data))
        if (type === 'setGanttColumns') {
          // 保存列配置
          SavePlanFieldSetting({ filedSetting: JSON.stringify(data) }).then(
            res => {
              if (res.IsSucceed) {
                // this.$message({
                //   type: 'success',
                //   message: '操作成功'
                // })
              } else {
                this.$message({
                  type: 'error',
                  message: res.Message
                })
              }
            }
          )
        }
      }
    },
    restoreDefault() {
      console.log('111')
      this.dialogCancel()
      this.loadUserColumns(this.plan.Id || 'X', true).then(res => {
        if (res.IsSucceed) {
          this.setGanttColumns(res.Data.AvailableSettings)
        }
      })
    },
    onMppImported(data) {
      this.removeFowardLine()
      this.showFowardLine = false
      const newPlan = BGT.parseServerPlanEntity(data)
      Object.keys(this.plan).forEach(k => {
        if (
          (k === 'Plan_Data' ||
            k === 'Plan_Calendar' ||
            this.plan[k] === undefined ||
            this.plan === null) &&
          k !== 'Id'
        ) {
          this.plan[k] = newPlan[k]
        }
      })
      BGT.createGanttInstance(this, {
        el: 'gantt-chart',
        locale: 'cn',
        calendar: this.plan.Plan_Calendar,
        filters: this.gantFilters,
        gridColumns: this.ganttColumns,
        editMode: this.editMode,
        showBaseLine: this.showBaseLine,
        start: this.plan.Plan_Start_Date
      })
    },
    setPlanBase(opts) {
      for (const k in opts) {
        BGT.updatePlanField(this.plan, k, opts[k])
      }
    },
    setCalendar(data) {
      BGT.updateCalendar(this.plan, this.gantt, data)
    },
    setGanttColumns(data) {
      this.AvailableSettings = data
      this.gantt.config.columns = [
        this.gantt.config.columns[0],
        ...this.ganttColumns
      ]
      this.gantt.render()
    },
    setTargetPlan(id) {
      this.plan.Target_Plan_Id = id
      const tasks = this.gantt.getTaskByTime()
      new Promise((resolve, reject) => {
        if (id === 0) {
          // 先保存目标副本
          this.savePlan(0, false, true).then(res => {
            if (res.IsSucceed) {
              this.plan.Target_Plan_Id = res.Data
              console.log(this.plan)
            }
          })
          // 本地数据处理自身目标横道
          tasks.forEach(t => {
            t.Target_Start_Date = t.Plan_Start_Date
            t.Target_End_Date = moment(t.Plan_End_Date).toDate()
            t.Target_Resources = t.Plan_Resources
            t.Target_Duration = t.Plan_Duration
          })
          resolve()
        } else if (id === '') {
          // 清除
          this.plan.Target_Plan_Id = null
          tasks.forEach(t => {
            t.Target_Start_Date = t.Target_End_Date = null
            t.Target_Duration = t.Target_Resources = 0
          })
          resolve()
        } else {
          //  具有目标计划ID
          // 拉取计划
          GetPlanEntity(id).then(res => {
            if (res.IsSucceed) {
              const targetTasks = res.Data.Plan_Data.data
              tasks.forEach(t => {
                const tt = targetTasks.find(tk => tk.id === t.id)
                if (tt) {
                  t.Target_Start_Date = tt.Plan_Start_Date
                  t.Target_End_Date = moment(tt.Plan_End_Date).toDate()
                  t.Target_Resources = tt.Plan_Resources
                  t.Target_Duration = tt.Plan_Duration
                }
              })
            }
            resolve()
          })
        }
      })
        .then(() => {
          this.plan.Plan_Data = {
            data: this.gantt.getTaskByTime(),
            links: this.gantt.getLinks()
          }
        })
        .finally(() => {
          if (
            this.plan.Target_Plan_Id !== null &&
            this.plan.Target_Plan_Id !== undefined &&
            this.plan.Target_Plan_Id !== ''
          ) {
            this.showBaseLine = true
          } else {
            this.showBaseLine = false
          }
          console.log(this.showBaseLine)
          BGT.createGanttInstance(this, {
            el: 'gantt-chart',
            locale: 'cn',
            calendar: this.plan.Plan_Calendar,
            filters: this.gantFilters,
            gridColumns: this.ganttColumns,
            editMode: this.editMode,
            showBaseLine: this.showBaseLine,
            start: this.plan.Plan_Start_Date
          })
        })
    },
    setUpdatePlan({ date, time, data_date }) {
      console.log(date, time, data_date)
      SendTaskInputMessage({
        plan_id: this.plan.Id,
        inputEndDate: moment(date).format('YYYY-MM-DD') + ' ' + time,
        dataDate: moment(data_date).format('YYYY-MM-DD')
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            type: 'success',
            message: res.Message
          })
        }
      })
    },
    setGanttFilters(filter) {
      this.gantFilters.checkers = filter.checkers
      this.gantFilters.matchto = filter.matchto
      this.gantt?.refreshData()
      this.reRenderGantt()
    },
    onLevelChange(lvl) {
      this.foldTaskLevel(this.maxDeep - lvl)
    },
    canDrawLine(task, dataDate) {
      // 不绘制前锋线条件
      // 1.wbs 2.实际结束 3.没有目标横道 4.未开始且目标计划开始不早于数据日期;
      if (task.type !== 'task') return false
      if (task.Actual_End_Date) return false
      if (!task.Target_Start_Date) return false
      if (moment(task.Target_Start_Date).isSameOrAfter(dataDate)) return false
      return true
    },
    drawFowardLine() {
      var date = new Date(
        moment(this.plan.Cur_Data_Date ?? this.plan.Plan_Start_Date)
      )

      const scrollstate = this.gantt.getScrollState()
      const overlay = this.gantt.ext.overlay.addOverlay(container => {
        container.style.top = -scrollstate.y + 'px'
        var divDataArea = document.querySelector('.gantt_task_bg')
        const div = document.createElement('div')
        div.style.height = divDataArea.offsetHeight + 'px'
        div.style.width = divDataArea.offsetWidth + 'px'
        container.appendChild(div)
        const tasks = this.gantt.getTaskByTime()
        // -- SVG 方案 --
        let svg = `<svg style="width:${divDataArea.offsetWidth}px;height:${divDataArea.offsetHeight}px;">`
        const daySize = { h: 0, w: 0 }
        const startPos = this.gantt.posFromDate(date)
        tasks.forEach(t => {
          const size = this.gantt.getTaskPosition(
            t,
            t.Target_Start_Date,
            t.Target_End_Date
          )
          // console.log(size)
          daySize.h = size.height
          daySize.w =
            size.width /
            this.gantt.calculateDuration({
              start_date: t.Target_Start_Date,
              end_date: t.Target_End_Date
            })
          const taskTargetStart = this.gantt.posFromDate(
            moment(t.Target_Start_Date)
              .startOf('date')
              .toDate()
          )
          const taskTargetEnd = this.gantt.posFromDate(
            moment(t.Target_End_Date)
              .startOf('date')
              .toDate()
          )
          const targetPos =
            (taskTargetEnd - taskTargetStart + daySize.w) * t.Actual_Progress +
            taskTargetStart
          // console.log(daySize.w)
          // console.log(t.text, taskTargetStart, taskTargetEnd, targetPos, t.Actual_Progress, taskTargetEnd - taskTargetStart, (taskTargetEnd - taskTargetStart) * t.Actual_Progress)
          if (this.canDrawLine(t, date)) {
            svg += `<line x1="${startPos}" y1="${size.top +
              size.height +
              4}" x2="${targetPos}" y2="${size.top +
              size.height +
              4 +
              size.height / 2}" style="stroke:rgb(255,0,0);stroke-width:2" />`
            svg += `<line x1="${targetPos}" y1="${size.top +
              size.height +
              size.height / 2 +
              4}" x2="${startPos}" y2="${size.top +
              size.rowHeight}" style="stroke:rgb(255,0,0);stroke-width:2" />`
            // 不绘制小圆环
            // svg += `<circle cx="${targetPos}" cy="${size.top +
            //   size.height +
            //   4 +
            //   size.height /
            //     2}" r="4" stroke="rgb(255,0,0)" stroke-width="2" fill="none" />`
          }
        })

        svg += `</svg>`
        div.innerHTML = svg
        // -- CANVAS 方案 --
        // const cvs = document.createElement('canvas')
        // cvs.setAttribute('width', divDataArea.offsetWidth + 'px')
        // cvs.setAttribute('height', divDataArea.offsetHeight + 'px')
        // cvs.style.width = '100%'
        // cvs.style.height = '100%'
        // const ctx = cvs.getContext('2d')
        // ctx.strokeStyle = '#FF0000'
        // const daySize = { h: 0, w: 0 }
        // const startPos = this.gantt.posFromDate(date)
        // ctx.beginPath()
        // ctx.moveTo(startPos, 0)
        // tasks.forEach(t => {
        //   const size = this.gantt.getTaskPosition(t)
        //   daySize.h = size.height
        //   daySize.w = size.width
        //   if (t.type === 'task' && !t.Actual_End_Date) {
        //     // 非wbs且无实际结束
        //     ctx.moveTo(startPos, size.top)
        //     ctx.lineWidth = 1
        //     ctx.lineTo(
        //       size.left + size.width * t.progress,
        //       size.top + size.height / 2 + 2
        //     )
        //     ctx.lineTo(startPos, size.top + size.height + 2)
        //     ctx.stroke()
        //     ctx.beginPath()
        //     ctx.lineWidth = 3
        //     ctx.arc(
        //       size.left + size.width * t.progress,
        //       size.top + size.height / 2 + 2,
        //       4,
        //       0,
        //       2 * Math.PI
        //     )
        //     ctx.stroke()
        //   }
        // })
        // div.appendChild(cvs)
        return div
      })
      this.fowards.overlay = overlay
      this.gantt.ext.overlay.showOverlay(overlay)
      this.gantt.render()
    },

    removeFowardLine() {
      const { overlay } = { ...this.fowards }
      this.gantt.ext.overlay.deleteOverlay(overlay)
      // overlay删除当前版本有bug，手动移除元素
      const ele = document.querySelector(`[data-overlay-id="${overlay}"]`)
      ele?.remove()
      this.fowards = {
        overlay: ''
      }
      this.gantt.render()
    },
    onToggleFowardLine(show) {
      if (!show) {
        this.removeFowardLine()
      } else {
        this.drawFowardLine()
      }
    },
    onToggleSpotLight(show) {
      if (!show) {
        this.gantt.deleteMarker('spot-marker')
        return this.reRenderGantt()
      }
      // 当前时间线长度
      const dur = this.gantt.getSubtaskDuration()
      // 聚光灯设置
      let spotMarker
      if (this.plan && this.plan.Cur_Data_Date) {
        const today = moment(
          this.spot_range.start || this.plan.Cur_Data_Date
        ).toDate()
        const end =
          this.spot_range.end ||
          this.gantt.calculateEndDate({
            start_date: today,
            duration: dur < 6 ? dur : 6
          })
        spotMarker = this.gantt.addMarker({
          start_date: today,
          end_date: end,
          id: 'spot-marker',
          css: 'spot-marker',
          text: '聚光灯',
          title: `${moment(today).format('YYYY-MM-DD')} ~ ${moment(end).format(
            'YYYY-MM-DD'
          )}`
        })
        this.gantt.render()
        this.bindSpotMarkerEvents(
          document.querySelector('[data-marker-id=spot-marker]'),
          this.gantt.getMarker('spot-marker')
        )
      }
    },
    // 绑定聚光灯事件
    bindSpotMarkerEvents(ele, marker) {
      const leftBar = document.createElement('div')
      leftBar.className = 'l-bar'
      const rightBar = document.createElement('div')
      rightBar.className = 'r-bar'
      ele?.append(leftBar, rightBar)
      const bgW = document.querySelector('.gantt_task_bg')?.offsetWidth
      if (!ele) return
      // console.log(ele, marker)
      rightBar.addEventListener('mousedown', e => {
        e.stopPropagation()
        rightBar.__start_pos = [e.pageX, e.pageY]
        rightBar.__draging = true
      })
      rightBar.addEventListener('mouseup', e => {
        if (rightBar.__draging) this.dragEnd(e)
      })
      rightBar.addEventListener('mouseleave', e => {
        if (rightBar.__draging) this.dragEnd(e)
      })
      rightBar.addEventListener('mousemove', e => {
        if (rightBar.__draging) {
          if (
            Number(ele.style.left.split('px')[0]) +
              10 +
              Number(ele.style.width.split('px')[0]) >
            bgW
          ) {
            ele.style.left = Number(ele.style.left.split('px')[0]) - 2 + 'px'
            return this.dragEnd(e)
          }
          rightBar.__to_pos = [e.pageX, e.pageY]
          ele.style.width =
            Math.max(
              Number(ele.style.width.split('px')[0]) +
                (rightBar.__to_pos[0] - rightBar.__start_pos[0]),
              60
            ) + 'px'

          rightBar.__start_pos = rightBar.__to_pos
        }
      })

      leftBar.addEventListener('mousedown', e => {
        e.stopPropagation()
        leftBar.__start_pos = [e.pageX, e.pageY]
        leftBar.__draging = true
      })
      leftBar.addEventListener('mouseup', e => {
        if (leftBar.__draging) this.dragEnd(e)
      })
      leftBar.addEventListener('mouseleave', e => {
        if (leftBar.__draging) this.dragEnd(e)
      })
      leftBar.addEventListener('mousemove', e => {
        if (leftBar.__draging) {
          if (Number(ele.style.left.split('px')[0]) < 10) {
            ele.style.left = '10px'
            return this.dragEnd(e)
          }
          leftBar.__to_pos = [e.pageX, e.pageY]
          const moved = leftBar.__to_pos[0] - leftBar.__start_pos[0]
          ele.style.width =
            Math.max(Number(ele.style.width.split('px')[0]) - moved, 60) + 'px'
          ele.style.left = Number(ele.style.left.split('px')[0]) + moved + 'px'
          leftBar.__start_pos = leftBar.__to_pos
        }
      })
      const days = Math.abs(
        moment(marker.start_date).diff(marker.end_date, 'days')
      )
      // console.log(marker, days)
      const unit = ele.clientWidth / days // 天单元格长度
      ele.addEventListener('mousedown', e => {
        ele.__draging = true
        ele.__start_pos = [e.pageX, e.pageY]
      })
      ele.addEventListener('mouseup', e => {
        if (ele.__draging) this.dragEnd(e)
      })
      ele.addEventListener('mouseleave', e => {
        if (ele.__draging) this.dragEnd(e)
      })
      ele.addEventListener('mousemove', e => {
        if (ele.__draging) {
          if (Number(ele.style.left.split('px')[0]) < 10) {
            ele.style.left = '10px'
            return this.dragEnd(e)
          }
          if (
            Number(ele.style.left.split('px')[0]) +
              10 +
              Number(ele.style.width.split('px')[0]) >
            bgW
          ) {
            ele.style.left = Number(ele.style.left.split('px')[0]) - 2 + 'px'
            return this.dragEnd(e)
          }

          ele.__to_pos = [e.pageX, e.pageY]
          ele.style.left =
            Number(ele.style.left.split('px')[0]) +
            (ele.__to_pos[0] - ele.__start_pos[0]) +
            'px'
          // 按天移动
          // if (Math.abs(ele.__to_pos[0] - ele.__start_pos[0]) > unit / 2) {
          //   const m = mod.gantt.getMarker(spotMarker)
          //   m.start_date = mod.gantt.calculateEndDate({
          //     start_date: m.start_date,
          //     duration: ele.__to_pos[0] - ele.__start_pos[0] > 0 ? 1 : -1
          //   })
          //   m.end_date = mod.gantt.calculateEndDate({
          //     start_date: m.end_date,
          //     duration: ele.__to_pos[0] - ele.__start_pos[0] > 0 ? 1 : -1
          //   })
          //   mod.gantt.updateMarker(spotMarker)
          //   bindSpotMarkerEvents(
          //     document.querySelector('[data-marker-id=spot-marker]'),
          //     marker
          //   )
          //   dragEnd(e)
          // }
          ele.__start_pos = ele.__to_pos
        }
      })

      const ms = document.querySelectorAll('.gantt_marker_area')
      ms[ms.length - 1].style.pointerEvents = 'none'
      ele.style.pointerEvents = 'initial'
    },
    dragEnd(e) {
      const ele = document.querySelector('[data-marker-id=spot-marker]')
      e.target.__draging = false
      e.target.__start_pos = null
      e.target.__to_pos = null
      const s_date = this.gantt.dateFromPos(
        Number(ele.style.left.split('px')[0])
      )
      const e_date = this.gantt.dateFromPos(
        Number(ele.style.left.split('px')[0]) +
          Number(ele.style.width.split('px')[0])
      )
      const m = this.gantt.getMarker('spot-marker')
      m.start_date = s_date
      m.end_date = e_date
      this.spot_range.start = m.start_date
      this.spot_range.end = m.end_date
      this.gantt.updateMarker('spot-marker')
      this.gantt.render()
      this.bindSpotMarkerEvents(
        document.querySelector('[data-marker-id=spot-marker]'),
        this.gantt.getMarker('spot-marker')
      )
      console.log('drag end.....')
    },
    onComputedSchedule({ Cur_Data_Date, Is_Save_History }) {
      // 先关闭 detail，关闭前锋线
      this.drawerCancel()
      this.removeFowardLine()
      this.showFowardLine = false

      this.plan.Plan_Data.data = this.gantt.getTaskByTime()
      this.plan.Plan_Data.links = this.gantt.getLinks()
      // 后续处理
      console.log(Cur_Data_Date, Is_Save_History)
      this.loading = true
      this.loadingStr = '正在为您计算结果...'
      this.plan.Cur_Data_Date = Cur_Data_Date
      this.saveParam.isSaveHistory = Is_Save_History
      BGT.compute(this.gantt, this.plan)
        .then(res => {
          this.plan.Plan_Data.data = res.tasks
          this.plan.Plan_Data = { ...this.plan.Plan_Data }
          this.reshecdeledData = this.plan?.Plan_Data
          BGT.createGanttInstance(this, {
            el: 'gantt-chart',
            locale: 'cn',
            calendar: this.plan.Plan_Calendar,
            filters: this.gantFilters,
            gridColumns: this.ganttColumns,
            editMode: this.editMode,
            showBaseLine: this.showBaseLine,
            start: this.plan.Plan_Start_Date
          }, this.reshecdeledData)
          this.reRenderGantt()
        })
        .catch(err => {
          this.$message.warning(err)
        })
        .finally(() => {
          this.loading = false
          this.loadingStr = ''
        })
    },
    moment(v) {
      return moment(v)
    },
    // 用户可否删除作业/WBS
    canDelTaskInfo(task) {
      let can = true
      let msg = 'ok'
      // 动态运营同步的，无法删除
      if (task.Is_Sync && task.Is_Sync != '0') {
        can = false
        msg = '通过动态运营表同步，无法删除'
      }
      // 作业已经实际开始，无法删除 /
      if (task.Actual_Start_Date || task.Actual_End_Date) {
        can = false
        msg = '作业已经开始，无法删除'
      }
      // 无删除权限
      if (!this.role.check(this.ACCESSES.DELETE)) {
        can = false
        msg = '您无权删除当前作业'
      }
      // 无WBS管理权限
      if (task.type !== 'task' && !this.role.check(this.ACCESSES.WBS)) {
        can = false
        msg = '您无权删除当前作业'
      }
      // 有删除权限但任务有所属wbs，用户非任务WBS负责人切无WBS权限
      if (
        this.role.check(this.ACCESSES.DELETE) &&
        task.parent != '0' &&
        this.gantt.getTask(task.parent).Responsible_User !==
          this.$store.state.user.userId &&
        !this.role.check(this.ACCESSES.WBS)
      ) {
        can = false
        msg = '您无权删该WBS下的作业'
      }
      return {
        can,
        msg
      }
    },
    // 用户可否添加作业/WBS
    canAddTaskInfo() {
      let can = true
      let msg = 'ok'
      if (this.plan.Id) {
        // 无添加权限
        if (!this.role.check(this.ACCESSES.ADD)) {
          can = false
          msg = '你无权新增作业'
        }
        // 无WBS权限，不能添加WBS
        if (!this.role.check(this.ACCESSES.WBS) && this.wbsMode) {
          can = false
          msg = '你无权操作WBS'
        }
        // 有选中WBS，但非WBS责任人且无WBS权限
        const sid = this.gantt.getSelectedId()
        const st = sid ? this.gantt.getTask(sid) : null
        if (
          st &&
          st.type === 'project' &&
          st.Responsible_User !== this.$store.state.user.userId &&
          !this.role.check(this.ACCESSES.WBS)
        ) {
          can = false
          msg = '你无权操作当前WBS'
        }
      }
      return {
        can,
        msg
      }
    },
    // 导入及改变计划状态
    canHandlePlanStatus() {
      if (!this.plan.Id) return true
      return (
        (this.plan.Status?.toString() === '0' ||
          this.plan.Status?.toString() === '3') &&
        this.role.check(BGT.PlanAuth.ACCESSES.WBS)
      )
    },
    // 可拖动及调整任务长度
    canDragAndResizeTask(task) {
      if (this.plan.Status === '1' || this.plan.Status === '2') return false // 发布及审核中的不允许修改计划相关
      if (this.plan.Id) {
        if (!this.role.check(this.ACCESSES.EDIT)) return false
        if (this.role.check(this.ACCESSES.WBS)) return true
        // 如果任务有上级wbs，且用户非wbs责任人
        if (task.parent.toString() !== '0') {
          const p = this.gantt.getTask(task.parent)
          if (p.Responsible_User !== this.$store.state.user.userId) {
            return false
          }
        }
      }
      return true
    },
    // 可连接关系
    canAddLink(l) {
      if (this.plan.Status === '1' || this.plan.Status === '2') return false // 发布及审核中的不允许修改计划相关
      const sourceParent = this.gantt.getTask(l.source)
      const targetParent = this.gantt.getTask(l.targat)
      if (this.plan.Id) {
        if (!this.role.check(this.ACCESSES.RELATION)) {
          if (
            sourceParent.Responsible_User === this.$store.state.user.userId &&
            targetParent.Responsible_User === this.$store.state.user.userId
          ) {
            return true
          } else {
            return false
          }
        } else {
          return true
        }
      }
      return true
    },
    // 可修改作业
    canEditTask(task) {
      if (!this.plan.Id) return true
      if (this.plan.Status === '1' || this.plan.Status === '2') return false // 发布及审核中的不允许修改计划相关
      // 动态运营同步的，无法修改
      if (task.Is_Sync && task.Is_Sync != '0') {
        return false
      }
      if (this.role.check(this.ACCESSES.WBS)) return true
      if (task.type === 'project') {
        if (task.Responsible_User == this.$store.state.user.userId) return true
      }
      if (task.parent.toString() !== '0') {
        const p = this.gantt.getTask(task.parent)
        if (p.Responsible_User == this.$store.state.user.userId) return true
      }
      return false
    },
    // 可修改WBS
    canEditWBS(task) {
      if (!this.plan.Id) return true
      return this.role.check(this.ACCESSES.WBS)
    },
    // 可更新进度
    canUpdateProgress(task) {
      if (!this.plan.Id) return true
      if (task.Responsible_User == this.$store.state.user.userId) {
        return true
      }
      if (this.role.check(this.ACCESSES.WBS)) return true
      if (task.type === 'task') {
        const wbs = this.gantt.getTask(task.parent)
        if (wbs && wbs.Responsible_User == this.$store.state.user.userId) {
          return true
        }
      }
      if (this.canEditTask(task)) return true
      return false
    }
  }
}
</script>
