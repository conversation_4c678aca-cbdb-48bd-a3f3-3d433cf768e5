{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\index.vue", "mtime": 1754613278767}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAu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file": "index.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis/gas", "sourcesContent": ["<template>\n  <el-row v-loading=\"loading\" :gutter=\"20\" class=\"containerBox\">\n    <template v-for=\"(item, index) in componentsData\">\n      <el-col :key=\"`gasUsed-${index}`\" :span=\"10\">\n        <gasUsed :custom-gas-used-config=\"item\" :is-photovoltaic=\"Is_Photovoltaic\" />\n      </el-col>\n      <el-col :key=\"`barChat-${index}`\" :span=\"14\">\n        <barChat\n          :custom-bar-chat-config=\"item\"\n          @radioChange=\"radioChange\"\n          @gasFlow=\"gasFlow\"\n        />\n      </el-col>\n    </template>\n    <gasFlowDialog\n      ref=\"gasFlowDialog\"\n      :custom-bar-chat-config=\"dialogObj\"\n      @radioChangeDialog=\"gasFlow\"\n    />\n  </el-row>\n</template>\n\n<script>\nimport gasUsed from './components/gasUsed'\nimport barChat from './components/barChat'\nimport gasFlowDialog from './components/dialog'\nimport {\n  GetGasTimePeriodDosageBarDiagram,\n  GetGasEachNodeDosageTreeDiagram,\n  GetGasLiquidPercent\n} from '@/api/business/pJEnergyAnalysis'\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\n\nexport default {\n  components: {\n    gasUsed,\n    barChat,\n    gasFlowDialog\n  },\n  props: {\n    componentsConfig: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  data() {\n    return {\n      componentsData: [\n        {\n          baseData: {\n            title: '氧气',\n            color: '#00A8FE',\n            unit: '吨',\n            Total: 0,\n            DataType: '全部'\n          },\n          gasData: {\n            tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',\n            showTotal: true,\n            fillHeight: '100%',\n            residue: {\n              code: 'Oxygen',\n              value: 0,\n              percentage: '0%'\n            },\n            colData: [\n              {\n                title: '一车间',\n                value: 0,\n                percentage: '80%',\n                iconName: 'workshop'\n              },\n              {\n                title: '二车间',\n                value: 0,\n                percentage: '60%',\n                iconName: 'workshop'\n              },\n              {\n                title: '配送中心',\n                value: 456,\n                percentage: '40%',\n                iconName: 'delivery'\n              }\n            ]\n          },\n          radioGroupData: [],\n          barData: {\n            tooltip: {\n              trigger: 'axis'\n            },\n            xAxis: {\n              type: 'category',\n              data: [],\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              }\n            },\n            grid: {\n              left: '2%',\n              right: '0%',\n              bottom: '10%',\n              // top: '20%',\n              containLabel: true\n            },\n            yAxis: {\n              type: 'value',\n              nameTextStyle: {\n                color: '#888888'\n              }\n            },\n            series: [\n              {\n                data: [],\n                type: 'bar',\n                symbol: 'emptyCircle',\n                barWidth: 10,\n                itemStyle: {\n                  color: '#00A8FE'\n                },\n                tooltip: {\n                  valueFormatter: function(value) {\n                    return value + ' 吨'\n                  }\n                }\n              }\n            ]\n          }\n        },\n        {\n          baseData: {\n            title: '二氧化碳',\n            color: '#3BC9FF',\n            unit: '吨',\n            Total: 0\n          },\n          gasData: {\n            tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',\n            showTotal: true,\n            fillHeight: '100%',\n            residue: {\n              code: 'Carbon',\n              value: 0,\n              percentage: '0%'\n            },\n            colData: [\n              {\n                title: '一车间',\n                value: 0,\n                percentage: '80%',\n                iconName: 'workshop'\n              },\n              {\n                title: '二车间',\n                value: 0,\n                percentage: '60%',\n                iconName: 'workshop'\n              },\n              {\n                title: '配送中心',\n                value: 456,\n                percentage: '40%',\n                iconName: 'delivery'\n              }\n            ]\n          },\n          radioGroupData: [],\n          barData: {\n            tooltip: {\n              trigger: 'axis'\n            },\n            xAxis: {\n              type: 'category',\n              data: [],\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              }\n            },\n            grid: {\n              left: '2%',\n              right: '0%',\n              bottom: '10%',\n              // top: '20%',\n              containLabel: true\n            },\n            yAxis: {\n              type: 'value',\n              nameTextStyle: {\n                color: '#888888'\n              }\n            },\n            series: [\n              {\n                data: [],\n                type: 'bar',\n                symbol: 'emptyCircle',\n                barWidth: 10,\n                itemStyle: {\n                  color: '#3BC9FF'\n                },\n                tooltip: {\n                  valueFormatter: function(value) {\n                    return value + ' 吨'\n                  }\n                }\n              }\n            ]\n          }\n        }\n        // {\n        //   baseData: {\n        //     title: '氩气',\n        //     unit: '吨',\n        //     color: '#A190FC',\n        //     Total: 0\n        //   },\n        //   gasData: {\n        //     tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',\n        //     showTotal: true,\n        //     fillHeight: '100%',\n        //     residue: {\n        //       code: 'Argon',\n        //       value: 0,\n        //       percentage: '0%'\n        //     },\n        //     colData: [\n        //       {\n        //         title: '一车间',\n        //         value: 0,\n        //         percentage: '80%',\n        //         iconName: 'workshop'\n        //       },\n        //       {\n        //         title: '二车间',\n        //         value: 0,\n        //         percentage: '60%',\n        //         iconName: 'workshop'\n        //       }\n        //     ]\n        //   },\n        //   radioGroupData: [],\n        //   barData: {\n        //     tooltip: {\n        //       trigger: 'axis'\n        //     },\n        //     xAxis: {\n        //       type: 'category',\n        //       data: [],\n        //       axisTick: {\n        //         show: false\n        //       },\n        //       axisLine: {\n        //         show: false\n        //       }\n        //     },\n        //     grid: {\n        //       left: '2%',\n        //       right: '0%',\n        //       bottom: '10%',\n        //       // top: '20%',\n        //       containLabel: true\n        //     },\n        //     yAxis: {\n        //       type: 'value',\n        //       nameTextStyle: {\n        //         color: '#888888'\n        //       }\n        //     },\n        //     series: [\n        //       {\n        //         data: [],\n        //         type: 'bar',\n        //         symbol: 'emptyCircle',\n        //         barWidth: 10,\n        //         itemStyle: {\n        //           color: '#A190FC'\n        //         },\n        //         tooltip: {\n        //           valueFormatter: function(value) {\n        //             return value + ' 吨'\n        //           }\n        //         }\n        //       }\n        //     ]\n        //   }\n        // },\n        // {\n        //   baseData: {\n        //     title: '丙烷',\n        //     unit: '立方',\n        //     color: '#F86161',\n        //     Total: 0\n        //   },\n        //   gasData: {\n        //     tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',\n        //     showTotal: false,\n        //     fillHeight: '100%',\n        //     residue: {\n        //       value: 0,\n        //       percentage: '80%'\n        //     },\n        //     colData: [\n        //       {\n        //         title: '一车间',\n        //         value: 0,\n        //         percentage: '80%',\n        //         iconName: 'workshop'\n        //       },\n        //       {\n        //         title: '二车间',\n        //         value: 0,\n        //         percentage: '60%',\n        //         iconName: 'workshop'\n        //       },\n        //       {\n        //         title: '配送中心',\n        //         value: 456,\n        //         percentage: '40%',\n        //         iconName: 'delivery'\n        //       }\n        //     ]\n        //   },\n        //   radioGroupData: [],\n        //   barData: {\n        //     tooltip: {\n        //       trigger: 'axis'\n        //     },\n        //     xAxis: {\n        //       type: 'category',\n        //       data: [],\n        //       axisTick: {\n        //         show: false\n        //       },\n        //       axisLine: {\n        //         show: false\n        //       }\n        //     },\n        //     grid: {\n        //       left: '2%',\n        //       right: '0%',\n        //       bottom: '10%',\n        //       // top: '20%',\n        //       containLabel: true\n        //     },\n        //     yAxis: {\n        //       type: 'value',\n        //       nameTextStyle: {\n        //         color: '#888888'\n        //       }\n        //     },\n        //     series: [\n        //       {\n        //         data: [],\n        //         type: 'bar',\n        //         symbol: 'emptyCircle',\n        //         barWidth: 10,\n        //         itemStyle: {\n        //           color: '#F86161'\n        //         },\n        //         tooltip: {\n        //           valueFormatter: function(value) {\n        //             return value + ' 立方'\n        //           }\n        //         }\n        //       }\n        //     ]\n        //   }\n        // }\n      ],\n      componentsDialogData: [\n        {\n          radioGroupData: [],\n          barData: {\n            tooltip: {\n              trigger: 'axis'\n            },\n            xAxis: {\n              type: 'category',\n              data: [],\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              }\n            },\n            grid: {\n              left: '2%',\n              right: '0%',\n              bottom: '10%',\n              // top: '20%',\n              containLabel: true\n            },\n            yAxis: {\n              type: 'value',\n              nameTextStyle: {\n                color: '#888888'\n              }\n            },\n            series: [\n              {\n                data: [],\n                type: 'bar',\n                symbol: 'emptyCircle',\n                barWidth: 10,\n                itemStyle: {\n                  color: '#00A8FE'\n                },\n                tooltip: {\n                  valueFormatter: function(value) {\n                    return value + ' 立方'\n                  }\n                }\n              }\n            ]\n          },\n          GasType: 1\n        },\n        {\n          radioGroupData: [],\n          barData: {\n            tooltip: {\n              trigger: 'axis'\n            },\n            xAxis: {\n              type: 'category',\n              data: [],\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              }\n            },\n            grid: {\n              left: '2%',\n              right: '0%',\n              bottom: '10%',\n              // top: '20%',\n              containLabel: true\n            },\n            yAxis: {\n              type: 'value',\n              nameTextStyle: {\n                color: '#888888'\n              }\n            },\n            series: [\n              {\n                data: [],\n                type: 'bar',\n                symbol: 'emptyCircle',\n                barWidth: 10,\n                itemStyle: {\n                  color: '#3BC9FF'\n                },\n                tooltip: {\n                  valueFormatter: function(value) {\n                    return value + ' 立方'\n                  }\n                }\n              }\n            ]\n          },\n          GasType: 2\n        },\n        {\n          radioGroupData: [],\n          barData: {\n            tooltip: {\n              trigger: 'axis'\n            },\n            xAxis: {\n              type: 'category',\n              data: [],\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              }\n            },\n            grid: {\n              left: '2%',\n              right: '0%',\n              bottom: '10%',\n              // top: '20%',\n              containLabel: true\n            },\n            yAxis: {\n              type: 'value',\n              nameTextStyle: {\n                color: '#888888'\n              }\n            },\n            series: [\n              {\n                data: [],\n                type: 'bar',\n                symbol: 'emptyCircle',\n                barWidth: 10,\n                itemStyle: {\n                  color: '#A190FC'\n                },\n                tooltip: {\n                  valueFormatter: function(value) {\n                    return value + ' 立方'\n                  }\n                }\n              }\n            ]\n          },\n          GasType: 3\n        }\n      ],\n      dialogObj: {},\n      loading: false,\n      Is_Photovoltaic: false\n    }\n  },\n  // inject: ['DateType', 'StartTime', 'EndTime'],\n  watch: {\n    componentsConfig: {\n      handler(nv, ov) {\n        console.log('watch')\n        this.fetchData()\n      }\n    }\n  },\n  async created() {\n    await this.getPreferenceSettingValue()\n    this.fetchData()\n    this.getGasLiquidPercent()\n  },\n  mounted() {\n\n  },\n  // computed: {\n  //   parentData() {\n  //     return {\n  //       DateType: this.DateType(),\n  //       StartTime: this.StartTime(),\n  //       EndTime: this.EndTime(),\n  //     }\n  //   }\n  // },\n  methods: {\n    async getPreferenceSettingValue() {\n      const res = await GetPreferenceSettingValue({ Code: 'Is_Photovoltaic' })\n      if (res && res.IsSucceed) {\n        this.Is_Photovoltaic = res.Data === 'true'\n        this.componentsData.splice(2)\n      }\n    },\n\n    async fetchData() {\n      const promises = ['1', '2', '3', '4'].map(async(i) => {\n        const [leftResData, rightResData] = await Promise.all([\n          this.getGasEachNodeDosageTreeDiagram({\n            ...this.componentsConfig,\n            GasType: i\n          }),\n          this.getGasTimePeriodDosageBarDiagram({\n            ...this.componentsConfig,\n            GasType: i,\n            IsTotalNode: true\n          })\n        ])\n\n        this.componentsData[i - 1].gasData.colData = leftResData.Nodes\n        // this.componentsData[i - 1].gasData.showTotal = true;\n        this.componentsData[i - 1].gasData.tooltip =\n          '假定罐内气压约为1.6兆帕，温度约为-193度'\n        this.componentsData[i - 1].baseData.Total = rightResData.Total\n        this.componentsData[i - 1].baseData.GasType = i\n        if (i !== 4) {\n          this.componentsDialogData[i - 1].GasType = i\n          this.componentsDialogData[i - 1].radioGroupData = [\n            '全部',\n            ...leftResData.Nodes.map((item) => item.Key)\n          ]\n        }\n        this.componentsData[i - 1].radioGroupData = [\n          '全部',\n          ...leftResData.Nodes.map((item) => item.Key)\n        ]\n\n        const xAxisData = (rightResData.List ?? []).map((item) => item.Key)\n        const seriesData = (rightResData.List ?? []).map((item) => item.Value)\n\n        this.componentsData[i - 1].barData.xAxis.data = xAxisData\n        this.componentsData[i - 1].barData.series[0].data = seriesData\n      })\n\n      await Promise.all(promises)\n    },\n\n    async getGasLiquidPercent(data) {\n      const res = await GetGasLiquidPercent(data)\n      this.componentsData.forEach((element) => {\n        if (res.Data.length > 0) {\n          const obj = res.Data.find(\n            (item) => element.gasData.residue.code === item.Code\n          )\n          element.gasData.residue = {\n            value: obj.Volume,\n            percentage: `${obj.Percent}%`\n          }\n          element.gasData.fillHeight = `${obj.Percent}%`\n        }\n      })\n    },\n    async getGasTimePeriodDosageBarDiagram(data) {\n      const res = await GetGasTimePeriodDosageBarDiagram(data)\n      return res.Data\n    },\n    async getGasEachNodeDosageTreeDiagram(data) {\n      const res = await GetGasEachNodeDosageTreeDiagram(data)\n      return res.Data\n    },\n    async radioChange(data) {\n      const i = data.GasType\n      const IsTotalNode = data.val === '全部'\n      const params = {\n        ...this.componentsConfig,\n        GasType: i,\n        IsTotalNode,\n        NodeName: data.val\n      }\n      if (data.val === '全部') {\n        delete params.NodeName\n      }\n      const rightResData = await this.getGasTimePeriodDosageBarDiagram(params)\n      const xAxisData = (rightResData.List ?? []).map((item) => item.Key)\n      const seriesData = (rightResData.List ?? []).map((item) => item.Value)\n      this.componentsData[i - 1].barData.xAxis.data = xAxisData\n      this.componentsData[i - 1].barData.series[0].data = seriesData\n      this.componentsData[i - 1].baseData.Total = rightResData.Total\n    },\n    async gasFlow(data) {\n      this.loading = true\n      this.$refs.gasFlowDialog.handleOpen()\n      const i = data.GasType\n      const IsTotalNode = data.val === '全部'\n      const params = {\n        ...this.componentsConfig,\n        GasType: i,\n        IsTotalNode,\n        NodeName: data.val,\n        IsCube: true\n      }\n      if (data.val === '全部') {\n        delete params.NodeName\n      }\n      const rightResData = await this.getGasTimePeriodDosageBarDiagram(params)\n      const xAxisData = (rightResData.List ?? []).map((item) => item.Key)\n      const seriesData = (rightResData.List ?? []).map((item) => item.Value)\n      this.componentsDialogData[i - 1].barData.xAxis.data = xAxisData\n      this.componentsDialogData[i - 1].barData.series[0].data = seriesData\n      this.dialogObj = this.componentsDialogData[i - 1]\n      this.loading = false\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.containerBox {\n  .el-col {\n    margin-bottom: 16px;\n  }\n}\n</style>\n"]}]}