import request from '@/utils/request'

// 得到流程模板 (Auth)
export function FlowSchemesGet(data) {
  return request({
    method: 'get',
    url: '/Platform/FlowSchemes/Get',
    params: data
  })
}

// 添加流程模板 (Auth)
export function FlowSchemesAdd(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowSchemes/Add',
    data
  })
}

// 更新流程模板 (Auth)
export function FlowSchemesUpdate(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowSchemes/Update',
    data
  })
}

// 删除流程模板
export function FlowSchemesDelete(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowSchemes/Delete',
    data
  })
}
// 流程模板列表
export function FlowSchemesLoad(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowSchemes/Load',
    data
  })
}

