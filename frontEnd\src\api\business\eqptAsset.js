import request from '@/utils/request'

export function SaveEquipmentAssetEntity(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/SaveEquipmentAssetEntity',
    data
  })
}
export function SaveEquipmentAssetEntityPJ(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/V2/SaveEquipmentAssetEntity',
    data
  })
}
export function SaveVisitors(data) {
  return request({
    method: 'post',
    url: '/DF/Visitor/SaveVisitors',
    data
  })
}

export function DeleteEquipmentAssetEntity(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/DeleteEquipmentAssetEntity',
    data
  })
}

export function GetEquipmentAssetEntity(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetEquipmentAssetEntity',
    data
  })
}

export function GetEquipmentAssetPageList(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetEquipmentAssetPageList',
    data
  })
}

export function GetEquipmentAssetPageListPJ(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/V2/GetEquipmentAssetPageList',
    data
  })
}
// 数字车间
export function GetEquipmentAssetPageListPJByData(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/V2/GetEquipmentAssetPageListByData',
    data
  })
}

export function GetEquipmentAssetPageListSZCJ(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetEquipmentAssetPageList/szcj',
    data
  })
}

export function ExportEquipmentAssetsList(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/ExportEquipmentAssetsList',
    data
  })
}
// v1 版本 导出设备资产列表
export function ExportEquipmentList(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/ExportEquipmentList',
    data
  })
}
// v2 版本 导出设备资产列表
export function ExportEquipmentListPJ(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/v2/ExportEquipmentList',
    data
  })
}
// 数字车间 v2版本导出
export function ExportEquipmentListPJByData(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/v2/ExportEquipmentListByData',
    data
  })
}



export function GetParkArea(data) {
  return request({
    method: 'post',
    url: '/DF/DFPark/GetParkArea',
    data
  })
}

export function GetTreeAddress(data) {
  return request({
    method: 'post',
    url: '/DF/DFPark/GetTreeAddress',
    data
  })
}

// 获取图表数据
export function GetDataCharts(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetDataCharts',
    data
  })
}

// 获取最新数据
export function GetEQTLastData(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetEQTLastData',
    data
  })
}

export function RefreshEQTLastData(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/RefreshEQTLastData',
    data
  })
}

// 获取历史数据
export function GetHistoryList(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetHistoryList',
    data
  })
}

// 导出历史数据
export function ExportHistoryData(data) {
  return request({
    method: 'get',
    url: '/DF/EQPTAsset/ExportHistoryData',
    params: data,
    responseType: 'blob'
  })
}

// 下载模板
export function AssetImportTemplate(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/AssetImportTemplate',
    data
  })
}

export function AssetEquipmentImport(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/AssetEquipmentImport',
    data
  })
}
// v2 下载模板
export function AssetImportTemplatePJ(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/V2/AssetImportTemplate',
    data
  })
}

export function AssetEquipmentImportPJ(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/V2/AssetEquipmentImport',
    data
  })
}

export function GetDictionaryDetailListByParentId(parentId) {
  return request({
    url: '/Platform/Dictionary/GetDictionaryDetailListByParentId',
    method: 'post',
    params: {
      parentId
    }
  })
}

// 设备数采分析
// 获取设备数采分析设备运行状况
export function GetStatusAnalyseEqt(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetStatusAnalyseEqt',
    data
  })
}
// 获取设备异常情况排行
export function GetErrorRank(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetErrorRank',
    data
  })
}
// 获取设备数采异常信息
export function GetTopEqtError(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetTopEqtError',
    data
  })
}

// 获取设备数采分析开机时间分析
export function GetStartAnalyseEqt(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetStartAnalyseEqt',
    data
  })
}

// 获取设备数采分析负载率分析
export function GetLoadAnalyseEqt(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetLoadAnalyseEqt',
    data
  })
}
// // 获取本月耗材设备使用效率排行
export function GetConsumptionAnalyseEqt(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetConsumptionAnalyseEqt',
    data
  })
}

// 获取设备故障趋势
export function GetDeviceAnalyseWorkOrderCount(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetDeviceAnalyseWorkOrderCount',
    data
  })
}

// 获取工单处理情况
export function GetWorkOrderHandlingCount(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetWorkOrderHandlingCount',
    data
  })
}
// 获取工单类型故障次数
export function GetWorkOrderErrorTypeList(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetWorkOrderErrorTypeList',
    data
  })
}
// 获取设备完好率
export function GetDeviceServiceabilityRate(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetDeviceServiceabilityRate',
    data
  })
}
// 获取AGV运行状态
export function GetAGVAnalyseEqt(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetAGVAnalyseEqt',
    data
  })
}

// 获取生产用量趋势
export function GetProduceTrend(data) {
  return request({
    method: 'post',
    url: '/DF/EQPTAsset/GetProduceTrend',
    data
  })
}

//设备数采基础信息
export function GetBaseEqtData(data) {
  return request({
    url: '/DF/EQPTAsset/GetBaseEqtData',
    method: 'post',
    data
  })
}
//获取设备数采生产分析信息
export function GetProEqtData(data) {
  return request({
    url: '/DF/EQPTAsset/GetProEqtData',
    method: 'post',
    data
  })
}
//获取设备数采运行信息
export function GetRunEqtData(data) {
  return request({
    url: '/DF/EQPTAsset/GetRunEqtData',
    method: 'post',
    data
  })
}
//获取设备数采能耗信息
export function GetEnergyEqtData(data) {
  return request({
    url: '/DF/EQPTAsset/GetEnergyEqtData',
    method: 'post',
    data
  })
}
//获取设备数采能耗趋势信息
export function GetEnergyTrendEqtData(data) {
  return request({
    url: '/DF/EQPTAsset/GetEnergyTrendEqtData',
    method: 'post',
    data
  })
}
//获取故障时间统计
export function GetWorkOrderErrorList(data) {
  return request({
    url: '/DF/EQPTAsset/GetWorkOrderErrorList',
    method: 'post',
    data
  })
}
//获取历史数据
export function GetAnaHistoryCharts(data) {
  return request({
    url: '/DF/EQPTAsset/GetAnaHistoryCharts',
    method: 'post',
    data
  })
}

// 设备监听 设备状态及故障
export function GetDeviceStatus(data) {
  return request({
    url: '/DF/EQPTAsset/v2/GetDeviceStatus',
    method: 'post',
    data
  })
}
// 设备监听 设备状态及故障
export function GetDeviceStatusDetails(data) {
  return request({
    url: '/DF/EQPTAsset/v2/GetDeviceStatusDetails',
    method: 'post',
    data
  })
}
// 设备监听列表
export function GetDeviceList(data) {
  return request({
    url: '/DF/EQPTAsset/v2/GetDeviceList',
    method: 'post',
    data
  })
}

// 位置树
export function GetPostionTreeList(data) {
  return request({
    url: '/DF/EQPTAsset/v2/GetPostionTreeList',
    method: 'post',
    data
  })
}
// 获取设备维保分析
export function GetMaintenanceStatistics(data) {
  return request({
    url: '/DF/EQPTAsset/GetMaintenanceStatistics',
    method: 'post',
    data
  })
}
// 获取维保记录列表
export function GetMaintenanceList(data) {
  return request({
    url: '/DF/EQPTAsset/GetMaintenanceList',
    method: 'post',
    data
  })
}
// 下一次维保信息
export function GetMaintenanceDate(data) {
  return request({
    url: '/DF/EQPTAsset/GetMaintenanceDate',
    method: 'post',
    data
  })
}
// 获取设备数采分析开机时间
export function GetStartAnalyseEqtDetails(data) {
  return request({
    url: '/DF/EQPTAsset/GetStartAnalyseEqtDetails',
    method: 'post',
    data
  })
}
export function GetEqtElectricData(data) {
  return request({
    url: 'DF/EQPTAsset/GetEqtElectricData',
    method: 'post',
    data
  })
}
export function ExportEquipCardInfo(data) {
  return request({
    url: '/DF/EQPTAsset/v2/ExportEquipCardInfo',
    method: 'post',
    data
  })
}

