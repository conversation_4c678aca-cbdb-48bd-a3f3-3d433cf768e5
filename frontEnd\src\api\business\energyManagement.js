import request from '@/utils/request'
// 能耗管理模块

/** ************  能耗统计分析 ****************/
// 获取能耗统计分析列表
export function GetAnalyseList(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetAnalyseList',
    data
  })
}
// 获取用量统计能耗分析列表 (Auth)
export function GetStatisticAnalyseList(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetStatisticAnalyseList',
    data
  })
}
// top5点表用量 (Auth)
export function GetTopAnalyseList(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetTopAnalyseList',
    data
  })
}

/** ************  监测设备档案 ****************/
// 获取单个数据字典列表数据
export function GetDictionaryDetailListByCode(data) {
  return request({
    method: 'post',
    url: '/Platform/Dictionary/GetDictionaryDetailListByCode',
    data
  })
}
// 获取多个数据字典列表数据
export function GetDictionaryDetailListByCodes(data) {
  return request({
    method: 'post',
    url: '/Platform/Dictionary/GetDictionaryDetailListByCodes',
    data
  })
}
// 获取数据字典树型列表数据
export function GetDictionaryTreeDetailListByCode(data) {
  return request({
    method: 'post',
    url: '/Platform/Dictionary/GetDictionaryTreeDetailListByCode',
    data
  })
}
// 新增/编辑设备
export function EditEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/EditEquipment',
    data
  })
}
// 获取设备列表
export function GetEquipmentList(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetEquipmentList',
    data
  })
}
export function GetEquipmentListSZCJ(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetEquipmentList/szcj',
    data
  })
}
// 获取设备详情
export function GetEqtEntity(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetEqtEntity',
    data
  })
}
// 删除设备列表
export function DeleteEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/DeleteEquipment',
    data
  })
}
// 导出设备列表
export function ExportEnergyEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/ExportEnergyEquipment',
    data
  })
}
export function ExportEnergyEquipmentSZCJ(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/ExportEnergyEquipment/szcj',
    data
  })
}
// 获取园区地址
export function GetParkArea(data) {
  return request({
    method: 'post',
    url: '/DF/DFPark/GetParkArea',
    data
  })
}
// 获取园区安装地址
export function GetTreeAddress(data) {
  return request({
    method: 'post',
    url: '/DF/DFPark/GetTreeAddress',
    data
  })
}
// 下载模板
export function EnergyImportTemplate(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/EnergyImportTemplate',
    data
  })
}
// 导入
export function EnergyEquipmentImport(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/EnergyEquipmentImport',
    data
  })
}

/** ************  能耗配额管理 ****************/
// 新增/编辑设备配额
export function EditQuota(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/EditQuota',
    data
  })
}
// 获取配额列表
export function GetQuotaList(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetQuotaList',
    data
  })
}
// 获取配额详情
export function GetQuotaEntity(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetQuotaEntity',
    data
  })
}
// 删除配额
export function DeleteQuota(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/DeleteQuota',
    data
  })
}
// 删除全部配额
export function DeleteAllQuota(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/DeleteAllQuota',
    data
  })
}

/** ************  能耗监测数据 ****************/
// 能耗监测数据列表
export function GetDataList(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetDataList',
    data
  })
}
// 导出能耗监测数据列表
export function ExportData(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/ExportData',
    data
  })
}
// 获取设备采集的年份数据
export function GetDataYearList(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetDataYearList',
    data
  })
}
// 获取设备采集的年份的月份数据
export function GetYearData(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetYearData',
    data
  })
}

/** ************  能耗告警明细 ****************/
// 获取报警列表
export function GetWarningList(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetWarningList',
    data
  })
}
export function GetWarningListSZCJ(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetWarningList/szcj',
    data
  })
}
// 获取告警类型
export function GetWarningTypeList(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetWarningTypeList',
    data
  })
}
// 导出告警数据
export function ExportWarningList(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/ExportWarningList',
    data
  })
}
export function ExportWarningListSZCJ(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/ExportWarningList/szcj',
    data
  })
}
// 设备配额
export function EditEnergyQuotaTotalSettings(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/EditEnergyQuotaTotalSettings',
    data
  })
}
// 设备配额
export function GetEnergyQuotaTotalSettingEntity(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetEnergyQuotaTotalSettingEntity',
    data
  })
}

// 关闭告警
export function UpdateWarningStatus(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/UpdateWarningStatus',
    data
  })
}
// 节点列表
export function GetNodes(data) {
  return request({
    method: 'post',
    url: '/df/PaoJiangEnergyNode/GetNodes',
    data
  })
}
// 删除节点列表
export function DeleteEnergyNode(data) {
  return request({
    method: 'post',
    url: '/df/PaoJiangEnergyNode/DeleteEnergyNode',
    data
  })
}
// 获取设备列表
export function GetDevices(data) {
  return request({
    method: 'post',
    url: '/df/PaoJiangEnergyNode/GetDevices',
    data
  })
}
// 新增设备列表
export function SaveEnergyNode(data) {
  return request({
    method: 'post',
    url: '/df/PaoJiangEnergyNode/SaveEnergyNode',
    data
  })
}
// 能耗用能分析
export function GetEnergyAnalyseAreaUseTotal(data) {
  return request({
    method: 'post',
    url: '/DF/Energy/GetEnergyAnalyseAreaUseTotal',
    data
  })
}

/** ************  能耗用量分析 ****************/
// 获取能耗费用折线图统计
export function GetCostAnalyseStatistic(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyCost/GetCostAnalyseStatistic',
    data
  })
}
// 获取账单费用
export function GetCostBill(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyCost/GetCostBill',
    data
  })
}
// 获取节点费用
export function GetNodeBill(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyCost/GetNodeBill',
    data
  })
}

/** ************  能耗用量分析 ****************/
// 共计耗能统计折线图
export function GetElectricAnalyseStatistic(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetElectricAnalyseStatistic',
    data
  })
}
// 光伏电统计
export function GetPhotovoltaicElectricProportion(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetPhotovoltaicElectricProportion',
    data
  })
}
// 火电统计
export function GetThermalPowerProportion(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetThermalPowerProportion',
    data
  })
}

// 设备用电
export function GetEquipmentElectricity(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetEquipmentElectricity',
    data
  })
}
// 设备占生产占比
export function ProductionEquipmentElectricityProportion(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/ProductionEquipmentElectricityProportion',
    data
  })
}
// 设备占总用电比
export function ProductionEquipmentTotalProportion(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/ProductionEquipmentTotalProportion',
    data
  })
}
// 电能损耗
export function GetElectricEnergyLoss(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetElectricEnergyLoss',
    data
  })
}
// 各建筑用电
export function GetBuildingElectricity(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetBuildingElectricity',
    data
  })
}

// 各类型节点用电占比
export function GetTypeElectricProportion(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetTypeElectricProportion',
    data
  })
}

// 获取各节点用能占比
export function GetNodeElectricProportion(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetNodeElectricProportion',
    data
  })
}

// 获取对应单节点时间内用能情况
export function GetNodePowerUsage(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetNodePowerUsage',
    data
  })
}
export function GetNodesPowerUsage(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetNodesPowerUsage',
    data
  })
}
//获取用电趋势
export function GetElectricTrend(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetElectricTrend',
    data
  })
}
//用电情况
export function GetElectricUsage(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetElectricUsage',
    data
  })
}
//光伏情况
export function GetPVUsage(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetPVUsage',
    data
  })
}
//获取重钢工厂用电
export function GetSteelFactoryUsage(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyAnalyse/GetSteelFactoryUsage',
    data
  })
}

// 费用结算单
// 获取结算单内容
export function GetSettlementDoc(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyCost/GetSettlementDoc',
    data
  })
}
//结账单计算
export function CalculateSettlementDoc(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyCost/CalculateSettlementDoc',
    data
  })
}

//导出结账单
export function ExportSettlementDoc(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyCost/ExportSettlementDoc',
    data
  })
}

// 阈值设置
// 获取能耗分析阈值设置列表
export function GetPageList(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyThreshold/GetPageList',
    data
  })
}
// 获取二级列表 可选点表类型
export function GetDeviceDropList(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyThreshold/GetDeviceDropList',
    data
  })
}

// 保存阈值设置
export function SaveThresholdSetting(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyThreshold/SaveThresholdSetting',
    data
  })
}

// 删除阈值设置
export function DelThresholdSetting(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyThreshold/DelThresholdSetting',
    data
  })
}

// 获取告警类型下拉选择框
export function GetWarningTypeDropList(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyThreshold/GetWarningTypeDropList',
    data
  })
}
// 获取比对项下拉选择框
export function GetContrastDropList(data) {
  return request({
    method: 'post',
    url: '/DF/PEnergyThreshold/GetContrastDropList',
    data
  })
}

