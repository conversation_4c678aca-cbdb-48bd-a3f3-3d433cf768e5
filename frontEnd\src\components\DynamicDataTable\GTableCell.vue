<template>
  <span class="cell-span">
    <template v-if="editable && editMode && column.Is_Edit">
      <template v-if="!column.Dropdown_Name">
        <el-input-number
          v-if="type === 'number'"
          ref="control"
          v-model="row[column.Code]"
          class="cell-control"
          controls-position="right"
          size="mini"
          @change="valueChanged"
          @blur="blurHandler"
        />
        <el-input
          v-if="type === 'text'"
          ref="control"
          v-model="row[column.Code]"
          class="cell-control"
          :type="type"
          size="mini"
          placeholder=""
          @change="valueChanged"
          @blur="blurHandler"
        />
        <el-time-select
          v-if="type === 'time'"
          ref="control"
          v-model="row[column.Code]"
          class="cell-control"
          size="mini"
          :picker-options="{
            start: '00:00',
            step: '00:30',
            end: '23:59'
          }"
          placeholder="选择时间"
          @change="valueChanged"
          @blur="blurHandler"
        />
        <el-date-picker
          v-if="type === 'date'"
          ref="control"
          v-model="row[column.Code]"
          class="cell-control"
          :type="'date'"
          size="mini"
          placeholder="选择日期"
          value-format="yyyy-M-d"
          @change="valueChanged"
          @blur="blurHandler"
        />
        <el-date-picker
          v-if="type === 'daterange'"
          v-model="row[column.Code]"
          style="width:96%"
          type="daterange"
          value-format="yyyy-M-d"
          format="yyyy-M-d"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="valueChanged"
          @blur="blurHandler"
        />
      </template>
      <template v-else>
        <el-select
          ref="control"
          v-model="row[column.Code]"
          class="cell-control"
          placeholder="请选择"
          @change="valueChanged"
          @blur="blurHandler"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </template>
    </template>
    <template v-else>
      <FormatedCellText
        :text="row[column.Code]"
        :tool-tip-text="row['StatusToolTip'] ? row['StatusToolTip'] : row[column.Code]"
        :show-tooltip="showTooltip"
        :type="type"
        :is-format="column.Is_Formatter"
        :formatter="column.Formatter"
        :range="column.Range"
        :cus-style="column.Style"
      />
    </template>
  </span>
</template>
<script>
import { formatDate } from 'element-ui/src/utils/date-util'
// 辅助方法：将array转为object
function toObject(array) {
  const ret = {}
  array.forEach(e => {
    const index = e.indexOf(':')
    const property = e.substring(0, index).trim()
    const value = e.substring(index + 1).trim()
    ret[property] = value
  })
  return ret
}
export const css2json = css => {
  let open, close
  while (
    (open = css.indexOf('/*')) !== -1 &&
    (close = css.indexOf('*/')) !== -1
  ) {
    css = css.substring(0, open) + css.substring(close + 2)
  }
  const json = {}

  while (css.length > 0) {
    let declarations = css
      .split(';')
      .map(e => e.trim())
      .filter(e => e.length > 0) // 移除所有""空值

    declarations = toObject(declarations)
    return declarations
  }

  // 返回JSON形式的结果串
  return json
}
export const FormatedCellText = {
  props: {
    isFormatter: {
      type: Boolean,
      default: false
    },
    formatter: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: '字符型'
    },
    text: {
      type: String | Boolean | Number,
      default: ''
    },
    cusStyle: {
      type: String | undefined,
      default: ''
    },
    range: {
      type: String | undefined,
      default: ''
    },
    showTooltip: {
      type: Boolean,
      default: false
    },
    toolTipText: {
      type: String | Boolean | Number,
      default: ''
    }
  },
  render(h) {
    let vnode
    let innerText = ''
    let style = {}
    const range = parseJsonRange(this.range)
    if (this.cusStyle) {
      try {
        style = css2json(this.cusStyle)
      } catch (err) {
        console.error(err)
      }
    }
    switch (this.type) {
      case 'boolean':
        vnode = h(
          'el-tag',
          {
            attrs: {
              effect: 'dark',
              size: 'mini',
              type: this.text ? 'success' : 'danger'
            },
            style: {
              borderRadius: '12px',
              padding: '0 10px'
            }
          },
          this.text ? range[0]?.label || '是' : range[1]?.label || '否'
        )
        break
      case 'date':
        if (!this.formatter) {
          innerText = this.text?.toString() ?? ''
        } else {
          innerText = formatDate(this.text, this.formatter)
        }
        vnode = h('span', { style }, innerText)
        break
      case 'daterange':
        if (Object.prototype.toString.call(this.text) === '[object Array]') {
          innerText = this.text.join('~')
        } else {
          innerText = this.text?.toString() || ''
        }
        if (!this.formatter || !this.text) {
          innerText = innerText?.toString() ?? ''
        } else {
          innerText =
            formatDate(innerText.split('~')[0], this.formatter) +
            '~' +
            formatDate(innerText.split('~')[1], this.formatter)
        }
        vnode = h('span', { style }, innerText)
        break
      case 'text':
      default:
        if (!this.formatter) {
          innerText = this.text?.toString() ?? ''
        } else {
          innerText = this.text?.toString() ?? ''
        }
        vnode = h('span', { style }, innerText)
        break
    }
    if (this.showTooltip) {
      vnode = h(
        'el-tooltip',
        {
          props: {
            disabled: !this.showTooltip,
            effect: 'dark'
          }
        },
        [vnode, h('span', { slot: 'content' }, this.toolTipText?.toString())]
      )
    }

    return vnode
  }
}

export const parseJsonRange = range => {
  if (!range) return []
  try {
    const r = JSON.parse(range)
    return r
  } catch (err) {
    console.error(err)
  }
  return []
}
export default {
  name: 'GTableCell',
  components: {
    FormatedCellText
  },
  props: {
    editable: {
      type: Boolean,
      default: false
    },
    rowIndex: {
      type: Number,
      default: 0
    },
    row: {
      type: Object,
      default: () => {}
    },
    column: {
      type: Object,
      default: () => {}
    },
    options: {
      type: Array,
      default: () => []
    },
    editMode: {
      type: String,
      default: ''
    },
    cellEditorBlurSaveModel: {
      type: Boolean,
      default: true
    },
    showTooltip: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      type: '',
      oValue: ''
    }
  },
  created() {
    this.type = this.column.Type
    this.oValue = this.row[this.column.Code]
  },
  methods: {
    setEditMode(mode) {
      this.$emit('changeRowEditMode', { index: this.rowIndex, mode })
      this.$nextTick(() => {
        this.$refs.control?.focus()
      })
    },
    valueChanged(val) {
      this.$emit('inlineEdited', {
        index: this.rowIndex,
        row: this.row,
        key: this.column.Code,
        value: val
      })
    },
    blurHandler() {
      console.log('blur....')
      if (this.cellEditorBlurSaveModel) {
        // this.setEditMode(null)
      }
    }
  }
}
</script>
<style scoped>
.editme {
  display: inline-block;
  margin-left: 6px;
}
.cell-control {
  width: 96%;
}
.edited {
  display: flex;
  flex-direction: row;
}
</style>
