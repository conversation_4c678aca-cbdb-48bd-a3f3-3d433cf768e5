import request from '@/utils/request'
//查询门禁列表
export function GetList(data) {
    return request({
        method: 'post',
        url: '/DF/EntranceAuthGroup/GetList',
        data
    })
}
//详情
export function GetAuthGroupDetail(data) {
    return request({
        method: 'get',
        url: '/DF/EntranceAuthGroup/GetAuthGroupDetail',
        params: data
    })
}
//保存门禁权限组
export function SaveAuthGroup(data) {
    return request({
        method: 'post',
        url: '/DF/EntranceAuthGroup/SaveAuthGroup',
        data
    })
}
//删除权限组
export function DelAuthGroup(data) {
    return request({
        method: 'post',
        url: '/DF/EntranceAuthGroup/DelAuthGroup',
        data
    })
}
//获取权限组设备列表
export function GetEquipList(data) {
    return request({
        method: 'post',
        url: '/DF/EntranceAuthGroup/GetEquipList',
        data
    })
}
//获取部门人员
export function GetDeptUserList(data) {
    return request({
        method: 'post',
        url: '/DF/EntranceAuthGroup/GetDeptUserList',
        data
    })
}
//同步人员
export function SyncAuthGroup(data) {
    return request({
        method: 'post',
        url: '/DF/EntranceAuthGroup/SyncAuthGroup',
        data
    })
}
//导出
export function ExportData(data) {
    return request({
        method: 'get',
        url: '/DF/EntranceAuthGroup/ExportData',
        params: data,
        responseType: 'blob'
    })
}
