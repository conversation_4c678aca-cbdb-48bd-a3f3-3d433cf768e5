import request from "@/utils/request";
//  安全类设备信息
export function GetDeviceInfo(params) {
    return request({
        method: "get",
        url: "/DF/SafetySignage/GetDeviceInfo",
        params,
    });
}
//  安全类设备信息
export function GetDeviceOnLineRate(params) {
    return request({
        method: "get",
        url: "/DF/SafetySignage/GetDeviceOnLineRate",
        params,
    });
}
//  总设备在线离线率
export function GetDeviceStatusRate(params) {
    return request({
        method: "get",
        url: "/DF/SafetySignage/GetDeviceStatusRate",
        params,
    });
}
//  未处理告警数据
export function GetUntreatedAlarm(params) {
    return request({
        method: "get",
        url: "/DF/SafetySignage/GetUntreatedAlarm",
        params,
    });
}
//  行为分析今日、本月、累计告警次数
export function GetDateAlarmCount(params) {
    return request({
        method: "get",
        url: "/DF/SafetySignage/GetDateAlarmCount",
        params,
    });
}
//  行为分析告警类型告警次数
export function GetAlarmTypeCount(params) {
    return request({
        method: "get",
        url: "/DF/SafetySignage/GetAlarmTypeCount",
        params,
    });
}
//  危化品告警
export function GetWarningCountTotal(params) {
    return request({
        method: "get",
        url: "/DF/SafetySignage/GetWarningCountTotal",
        params,
    });
}
//  电子巡更
export function GetPatrolStatistics(params) {
    return request({
        method: "get",
        url: "/DF/SafetySignage/GetPatrolStatistics",
        params,
    });
}
//  无人值守-配电房列表
export function GetSwitchRoomList(params) {
    return request({
        method: "get",
        url: "/DF/SafetySignage/GetSwitchRoomList",
        params,
    });
}
//  无人值守数据
export function GetUnOperation(params) {
    return request({
        method: "get",
        url: "/DF/SafetySignage/GetUnOperation",
        params,
    });
}
//  人工巡检
export function GetManualInspection(params) {
    return request({
        method: "get",
        url: "/DF/SafetySignage/GetManualInspection",
        params,
    });
}
//  设备报警情况-告警趋势
export function GetAlarmTrend(data) {
    return request({
        method: "post",
        url: "/DF/SafetySignage/GetAlarmTrend",
        data,
    });
}
//  设备报警情况-告警总数
export function GetAlarmTotal(data) {
    return request({
        method: "post",
        url: "/DF/SafetySignage/GetAlarmTotal",
        data,
    });
}
//  巡检问题详情-柱状图
export function GetPersonnelTotalStatistics(data) {
    return request({
        method: "post",
        url: "/DF/SafetySignage/GetPersonnelTotalStatistics",
        data,
    });
}
//  人员列表
export function GetPersonnelList(data) {
    return request({
        method: "post",
        url: "/DF/SafetySignage/GetPersonnelList",
        data,
    });
}
//  巡检问题详情-饼图
export function GetPersonnelStatistics(data) {
    return request({
        method: "post",
        url: "/DF/SafetySignage/GetPersonnelStatistics",
        data,
    });
}
//  巡检发现问题列表
export function GetIssues(data) {
    return request({
        method: "post",
        url: "/DF/SafetySignage/GetIssues",
        data,
    });
}

export function GetJumpUrl(data) {
  return request({
      method: "post",
      url: "/DF/SafetySignage/GetJumpUrl",
      data,
  });
}





