# DynamicDataTable （动态表格组件）
动态表格封装自 `el-table`，使用 `el-pagination` 对可能包含分页的表格进行扩展。

## 基础用法
```html
<DynamicDataTable
  :config="tbConfig"
  :columns="columns"
  :data="data" 
/>
```
```javascript
export default {
  components:{
    DynamicDataTable
  },
  data(){
    return {
      tbConfig:{},
      columns:[],
      data:[]
    }
  }
}
```
`config` 属性对表格进行基础设置，`columns`数组配置表格的列，`data`数组向表格注入数据。

## DynamicDataTable 属性
`DynamicDataTable` 接受两类属性，一种是自定义属性名、通过组件内部运算应用或不应用到 `el-tbale`、`el-table-column` 的属性；另外一种是直接灌入到内部 `el-table` 组件上的同名属性。
## 自定义属性
属性|类型|说明|默认值
-|-|-|-
config|Object| 动态表格的一般配置 |默认空对象 `{}` 。可用配置在下方 **config 配置列表** 中列出
total|Number|开启分页的表格，所有数据记录的总数 | 0
page|Number|开启分页的表格，当前页码|1
columns| Object[] | 列配置对象数组 | 默认空数组。列可用属性在下方 **column 对象配置列表** 中列出
query|Object | 开启表头首行搜索的表格，默认传入的搜索条件。符合 {列名:值} 的 key-value 形式 | `{}`
expends|String[] |支持展开行的表格，默认展开的行。被灌入到内部`el-table`组件的`expand-row-keys`属性，用法相同| `[]`
cellEditorBlurSaveModel | Object | 可行内单元格编辑的单元格，编辑组件失焦时存储修改数据的对象，符合`列名:值` 的键值对形式 | {}


## 接受的 el-table 上可用属性
属性|说明
--------|-------
data|下同 `el-table` 中的介绍
border|-
stripe|-

## config 配置列表
键|说明|默认值
-|-|-

