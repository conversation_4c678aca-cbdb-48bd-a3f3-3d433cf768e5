{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\store\\modules\\user.js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\store\\modules\\user.js", "mtime": 1754554650836}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["login", "logout", "getInfo", "getConfigure", "getToken", "setToken", "removeToken", "router", "resetRouter", "md5", "CryptoJS", "login<PERSON>ey", "GetEntity", "state", "token", "name", "avatar", "introduction", "roles", "userId", "Last_Working_Object_Id", "CurReferenceId", "localStorage", "getItem", "CurPlatform", "Language", "account", "mobile", "email", "Last_Product_Id", "HomePageUrl", "PreferenceSetting", "mutations", "SET_TOKEN", "setItem", "location", "reload", "history", "_startLocation", "includes", "window", "href", "origin", "SET_INTRODUCTION", "SET_NAME", "SET_AVATAR", "SET_ACCOUNT", "SET_MOBILE", "SET_EMAIL", "SET_ROLES", "SET_USERID", "SET_LAST_WORKING_OBJECT_ID", "SET_CURREFERENCEID", "SET_CURPLATFORM", "SET_LANGUAGE", "SET_LAST_PRODUCT_ID", "SET_HOMEPAGE_URL", "Home_Page_Url", "SET_PREFERENCE_SETTING", "preference", "actions", "getPreferenceSetting", "_ref", "code", "commit", "Promise", "resolve", "reject", "Code", "then", "response", "Data", "eval", "toLowerCase", "IsSucceed", "dataJson", "catch", "error", "sso<PERSON><PERSON>in", "_ref2", "query", "workingobjID", "_ref3", "userInfo", "pwd", "tenantID", "trim", "userpwd", "btoa", "toString", "Token", "_ref4", "dispatch", "Display_Name", "Id", "Picture", "Mobile", "Email", "Login_Account", "setInfo", "_ref5", "stateInfo", "UserAccount", "UserId", "UserName", "setActionInfo", "_ref6", "_ref7", "root", "resetToken", "_ref8", "console", "log", "changeRoles", "_ref9", "role", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$dispatch", "accessRoutes", "wrap", "_callee$", "_context", "prev", "next", "sent", "addRoutes", "stop", "_ref10", "namespaced"], "sources": ["D:/project/platform_framework_hlj/hljbimdigitalfactory/frontEnd/src/store/modules/user.js"], "sourcesContent": ["import { login, logout, getInfo, getConfigure } from '@/api/user'\nimport { getToken, setToken, removeToken } from '@/utils/auth'\nimport router, { resetRouter } from '@/router'\nimport md5 from 'md5'\nimport CryptoJS from 'crypto-js'\nimport { loginKey } from '@/utils/constants'\nimport { GetEntity } from '@/api/sys/company-info'\n\nconst state = {\n  token: getToken(),\n  name: '',\n  avatar: '',\n  introduction: '',\n  roles: [],\n  userId: '',\n  Last_Working_Object_Id: '',\n  CurReferenceId: localStorage.getItem('CurReferenceId'),\n  CurPlatform: localStorage.getItem('Platform'),\n  Language: localStorage.getItem('Language'),\n  account: '',\n  mobile: localStorage.getItem('Mobile'),\n  email: '',\n  Last_Product_Id: localStorage.getItem('Last_Product_Id'),\n  HomePageUrl: localStorage.getItem('HomePageUrl'),\n  PreferenceSetting: {}\n}\n\nconst mutations = {\n  SET_TOKEN: (state, token) => {\n    state.token = token\n    if (token === '') {\n      localStorage.setItem('Token', '')\n      location.reload()\n      // if (router.history._startLocation === '/produce/qualityScreenHome') {\n      //   // window.history.pushState(null, '', '/login?autoLogin=true&tenantID=scyth_test&account=czy&pwd=123&redirect=produce/qualityScreenHome')\n      //   window.history.pushState(null, '', '/login?autoLogin=true&tenantID=hceg&account=zjdp&pwd=1decda52b02e5640266cfa1bcd9f00f1&redirect=produce/qualityScreenHome')\n      // } else {\n      //   window.history.pushState(null, '', '/login')\n      // }\n      if (router.history._startLocation.includes('isAuthLogin=true')) {\n        window.location.href = localStorage.getItem('autoLoginUrl')\n      } else {\n        window.location.href = window.location.origin\n      }\n    }\n  },\n  SET_INTRODUCTION: (state, introduction) => {\n    state.introduction = introduction\n  },\n  SET_NAME: (state, name) => {\n    state.name = name\n  },\n  SET_AVATAR: (state, avatar) => {\n    state.avatar = avatar\n  },\n  SET_ACCOUNT: (state, account) => {\n    state.account = account\n  },\n  SET_MOBILE: (state, mobile) => {\n    state.mobile = mobile\n  },\n  SET_EMAIL: (state, email) => {\n    state.email = email\n  },\n  SET_ROLES: (state, roles) => {\n    state.roles = roles\n  },\n  SET_USERID: (state, userId) => {\n    state.userId = userId\n  },\n  SET_LAST_WORKING_OBJECT_ID: (state, Last_Working_Object_Id) => {\n    state.Last_Working_Object_Id = Last_Working_Object_Id\n  },\n  SET_CURREFERENCEID: (state, CurReferenceId) => {\n    state.CurReferenceId = CurReferenceId\n  },\n  SET_CURPLATFORM: (state, CurPlatform) => {\n    state.CurPlatform = CurPlatform\n  },\n  SET_LANGUAGE: (state, Language) => {\n    state.Language = Language\n  },\n  SET_LAST_PRODUCT_ID: (state, Last_Product_Id) => {\n    state.Last_Product_Id = Last_Product_Id\n  },\n  SET_HOMEPAGE_URL: (state, Home_Page_Url) => {\n    state.HomePageUrl = Home_Page_Url\n  },\n  SET_PREFERENCE_SETTING: (state, preference) => {\n    state.PreferenceSetting = preference\n  }\n}\n\nconst actions = {\n  getPreferenceSetting({ commit, state }, code) {\n    return new Promise((resolve, reject) => {\n      getConfigure({ Code: code }).then(response => {\n        let { Data } = response\n        if (code === 'Is_Integration') {\n          if (Data === '' || !Data) {\n            Data = false\n          } else {\n            // eslint-disable-next-line no-eval\n            Data = eval(Data.toLowerCase())\n          }\n        }\n        if (response.IsSucceed) {\n          let dataJson = {}\n          if (state.PreferenceSetting) {\n            dataJson = state.PreferenceSetting\n          }\n          dataJson[code] = Data\n          commit('SET_PREFERENCE_SETTING', dataJson)\n          localStorage.setItem(code, Data)\n        }\n        resolve(Data)\n      }).catch(error => {\n        reject(error)\n      })\n    })\n  },\n  // 第三方登录、东航\n  ssoLogin({ commit }, query) {\n    const { token, workingobjID, CurReferenceId, CurPlatform } = query\n    commit('SET_TOKEN', token)\n    commit('SET_CURREFERENCEID', CurReferenceId || '')\n    localStorage.setItem('CurReferenceId', CurReferenceId || '')\n    localStorage.setItem('CurPlatform', CurPlatform || '')\n    commit('SET_LAST_WORKING_OBJECT_ID', workingobjID)\n    commit('SET_CURPLATFORM', CurPlatform)\n    localStorage.setItem('Last_Working_Object_Id', workingobjID)\n\n    setToken(token)\n  },\n  // user login\n  login({ commit }, userInfo) {\n    const { account, pwd, tenantID } = userInfo\n    // const userpwd = CryptoJS.AES.encrypt(pwd, loginKey, {\n    //   iv: '****************',\n    //   mode: CryptoJS.mode.CBC,\n    //   padding: CryptoJS.pad.Pkcs7\n    // })\n    return new Promise((resolve, reject) => {\n      login({ account: account.trim(), pwd: md5(pwd), userpwd: btoa(pwd).toString(), tenantID }).then(response => {\n        const { Data } = response\n        if (response.IsSucceed) {\n          commit('SET_TOKEN', Data.Token)\n          commit('SET_CURPLATFORM', Data.CurPlatform)\n          commit('SET_CURREFERENCEID', Data.CurReferenceId)\n          localStorage.setItem('CurReferenceId', Data.CurReferenceId)\n          localStorage.setItem('CurPlatform', Data.CurPlatform)\n          localStorage.setItem('Last_Product_Id', Data.Last_Product_Id)\n          setToken(Data.Token)\n        }\n        resolve(response)\n      }).catch(error => {\n        reject(error)\n      })\n    })\n  },\n\n  // get user info\n  getInfo({ commit, state, dispatch }) {\n    return new Promise((resolve, reject) => {\n      getInfo().then(response => {\n        if (response.IsSucceed) {\n          const { Data } = response\n          if (!Data) {\n            reject('身份验证过期，请重新登陆！')\n          }\n          const { Display_Name, Id, Picture, Last_Working_Object_Id, Mobile, Email, Login_Account } = Data\n          commit('SET_ROLES', [Id])\n          commit('SET_NAME', Display_Name)\n          if (Picture) {\n            commit('SET_AVATAR', Picture)\n          } else {\n            dispatch('getCompany')\n          }\n          commit('SET_USERID', Id)\n          commit('SET_ACCOUNT', Login_Account)\n          commit('SET_MOBILE', Mobile)\n          commit('SET_EMAIL', Email)\n          // localStorage.setItem('Mobile', Mobile)\n          commit('SET_LAST_WORKING_OBJECT_ID', Last_Working_Object_Id)\n          // localStorage.setItem('Last_Working_Object_Id', Last_Working_Object_Id)\n          commit('SET_LAST_PRODUCT_ID', Data.Last_Product_Id)\n          // localStorage.setItem('Last_Product_Id', Data.Last_Product_Id)\n\n          // getConfigure({ code: 'Language' }).then(res => {\n          //   if (res.Data) {\n          //     commit('SET_LANGUAGE', res.Data)\n          //     localStorage.setItem('Language', res.Data)\n          //   } else {\n          //     commit('SET_LANGUAGE', 'zh-cn')\n          //     localStorage.setItem('Language', 'zh-cn')\n          //   }\n          //   // console.log(localStorage.GetItem('Language'))\n          // })\n          resolve(Data)\n        } else {\n          reject()\n        }\n      }).catch(error => {\n        reject(error)\n      })\n    })\n  },\n\n  setInfo({ commit }, stateInfo) {\n    const { CurReferenceId, CurPlatform, Token, UserAccount, UserId, Mobile, UserName, Last_Working_Object_Id } = stateInfo\n    commit('SET_TOKEN', Token)\n    commit('SET_CURPLATFORM', CurPlatform)\n    commit('SET_CURREFERENCEID', CurReferenceId)\n    commit('SET_NAME', UserName)\n    commit('SET_USERID', UserId)\n    commit('SET_ROLES', [UserId])\n    commit('SET_ACCOUNT', UserAccount)\n    commit('SET_MOBILE', Mobile)\n    localStorage.setItem('CurReferenceId', CurReferenceId)\n    localStorage.setItem('CurPlatform', CurPlatform)\n    // localStorage.setItem('Last_Working_Object_Id', Last_Working_Object_Id)\n    localStorage.setItem('Token', Token)\n    setToken(Token)\n  },\n\n  setActionInfo({ commit }, stateInfo) {\n    const { CurReferenceId, CurPlatform } = stateInfo\n    commit('SET_CURPLATFORM', CurPlatform)\n    commit('SET_CURREFERENCEID', CurReferenceId)\n    localStorage.setItem('CurReferenceId', CurReferenceId)\n    localStorage.setItem('CurPlatform', CurPlatform)\n  },\n\n  // user logout\n  logout({ commit, state, dispatch }) {\n    return new Promise((resolve, reject) => {\n      logout().then(() => {\n        commit('SET_TOKEN', '')\n        commit('SET_ROLES', [])\n        removeToken()\n        resetRouter()\n        // reset visited views and cached views\n        // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485\n        dispatch('tagsView/delAllViews', null, { root: true })\n        resolve()\n      }).catch(error => {\n        reject(error)\n      })\n    })\n  },\n  // remove token\n  resetToken({ commit }) {\n    return new Promise(resolve => {\n      commit('SET_TOKEN', '')\n      commit('SET_ROLES', [])\n      console.log(2)\n      removeToken()\n      resolve()\n    })\n  },\n\n  // dynamically modify permissions\n  async changeRoles({ commit, dispatch }, role) {\n    const token = role + '-token'\n    commit('SET_TOKEN', token)\n    setToken(token)\n\n    const { roles } = await dispatch('getInfo')\n\n    resetRouter()\n\n    // generate accessible routes map based on roles\n    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })\n    // dynamically add accessible routes\n    router.addRoutes(accessRoutes)\n\n    // reset visited views and cached views\n    dispatch('tagsView/delAllViews', null, { root: true })\n  },\n\n  setToken({ commit }, token) {\n    commit('SET_TOKEN', token)\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n"], "mappings": ";;;;;;;;;;;;AAAA,SAASA,KAAK,IAALA,MAAK,EAAEC,MAAM,IAANA,OAAM,EAAEC,OAAO,IAAPA,QAAO,EAAEC,YAAY,QAAQ,YAAY;AACjE,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AAC9D,OAAOC,MAAM,IAAIC,WAAW,QAAQ,UAAU;AAC9C,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAOC,QAAQ,MAAM,WAAW;AAChC,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,SAAS,QAAQ,wBAAwB;AAElD,IAAMC,KAAK,GAAG;EACZC,KAAK,EAAEV,QAAQ,CAAC,CAAC;EACjBW,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE,EAAE;EACVC,YAAY,EAAE,EAAE;EAChBC,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVC,sBAAsB,EAAE,EAAE;EAC1BC,cAAc,EAAEC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACtDC,WAAW,EAAEF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;EAC7CE,QAAQ,EAAEH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;EAC1CG,OAAO,EAAE,EAAE;EACXC,MAAM,EAAEL,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EACtCK,KAAK,EAAE,EAAE;EACTC,eAAe,EAAEP,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;EACxDO,WAAW,EAAER,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EAChDQ,iBAAiB,EAAE,CAAC;AACtB,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,SAAS,EAAE,SAAXA,SAASA,CAAGpB,KAAK,EAAEC,KAAK,EAAK;IAC3BD,KAAK,CAACC,KAAK,GAAGA,KAAK;IACnB,IAAIA,KAAK,KAAK,EAAE,EAAE;MAChBQ,YAAY,CAACY,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MACjCC,QAAQ,CAACC,MAAM,CAAC,CAAC;MACjB;MACA;MACA;MACA;MACA;MACA;MACA,IAAI7B,MAAM,CAAC8B,OAAO,CAACC,cAAc,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAC9DC,MAAM,CAACL,QAAQ,CAACM,IAAI,GAAGnB,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MAC7D,CAAC,MAAM;QACLiB,MAAM,CAACL,QAAQ,CAACM,IAAI,GAAGD,MAAM,CAACL,QAAQ,CAACO,MAAM;MAC/C;IACF;EACF,CAAC;EACDC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAG9B,KAAK,EAAEI,YAAY,EAAK;IACzCJ,KAAK,CAACI,YAAY,GAAGA,YAAY;EACnC,CAAC;EACD2B,QAAQ,EAAE,SAAVA,QAAQA,CAAG/B,KAAK,EAAEE,IAAI,EAAK;IACzBF,KAAK,CAACE,IAAI,GAAGA,IAAI;EACnB,CAAC;EACD8B,UAAU,EAAE,SAAZA,UAAUA,CAAGhC,KAAK,EAAEG,MAAM,EAAK;IAC7BH,KAAK,CAACG,MAAM,GAAGA,MAAM;EACvB,CAAC;EACD8B,WAAW,EAAE,SAAbA,WAAWA,CAAGjC,KAAK,EAAEa,OAAO,EAAK;IAC/Bb,KAAK,CAACa,OAAO,GAAGA,OAAO;EACzB,CAAC;EACDqB,UAAU,EAAE,SAAZA,UAAUA,CAAGlC,KAAK,EAAEc,MAAM,EAAK;IAC7Bd,KAAK,CAACc,MAAM,GAAGA,MAAM;EACvB,CAAC;EACDqB,SAAS,EAAE,SAAXA,SAASA,CAAGnC,KAAK,EAAEe,KAAK,EAAK;IAC3Bf,KAAK,CAACe,KAAK,GAAGA,KAAK;EACrB,CAAC;EACDqB,SAAS,EAAE,SAAXA,SAASA,CAAGpC,KAAK,EAAEK,KAAK,EAAK;IAC3BL,KAAK,CAACK,KAAK,GAAGA,KAAK;EACrB,CAAC;EACDgC,UAAU,EAAE,SAAZA,UAAUA,CAAGrC,KAAK,EAAEM,MAAM,EAAK;IAC7BN,KAAK,CAACM,MAAM,GAAGA,MAAM;EACvB,CAAC;EACDgC,0BAA0B,EAAE,SAA5BA,0BAA0BA,CAAGtC,KAAK,EAAEO,sBAAsB,EAAK;IAC7DP,KAAK,CAACO,sBAAsB,GAAGA,sBAAsB;EACvD,CAAC;EACDgC,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAGvC,KAAK,EAAEQ,cAAc,EAAK;IAC7CR,KAAK,CAACQ,cAAc,GAAGA,cAAc;EACvC,CAAC;EACDgC,eAAe,EAAE,SAAjBA,eAAeA,CAAGxC,KAAK,EAAEW,WAAW,EAAK;IACvCX,KAAK,CAACW,WAAW,GAAGA,WAAW;EACjC,CAAC;EACD8B,YAAY,EAAE,SAAdA,YAAYA,CAAGzC,KAAK,EAAEY,QAAQ,EAAK;IACjCZ,KAAK,CAACY,QAAQ,GAAGA,QAAQ;EAC3B,CAAC;EACD8B,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAG1C,KAAK,EAAEgB,eAAe,EAAK;IAC/ChB,KAAK,CAACgB,eAAe,GAAGA,eAAe;EACzC,CAAC;EACD2B,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAG3C,KAAK,EAAE4C,aAAa,EAAK;IAC1C5C,KAAK,CAACiB,WAAW,GAAG2B,aAAa;EACnC,CAAC;EACDC,sBAAsB,EAAE,SAAxBA,sBAAsBA,CAAG7C,KAAK,EAAE8C,UAAU,EAAK;IAC7C9C,KAAK,CAACkB,iBAAiB,GAAG4B,UAAU;EACtC;AACF,CAAC;AAED,IAAMC,OAAO,GAAG;EACdC,oBAAoB,WAApBA,oBAAoBA,CAAAC,IAAA,EAAoBC,IAAI,EAAE;IAAA,IAAvBC,MAAM,GAAAF,IAAA,CAANE,MAAM;MAAEnD,KAAK,GAAAiD,IAAA,CAALjD,KAAK;IAClC,OAAO,IAAIoD,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtChE,YAAY,CAAC;QAAEiE,IAAI,EAAEL;MAAK,CAAC,CAAC,CAACM,IAAI,CAAC,UAAAC,QAAQ,EAAI;QAC5C,IAAMC,IAAI,GAAKD,QAAQ,CAAjBC,IAAI;QACV,IAAIR,IAAI,KAAK,gBAAgB,EAAE;UAC7B,IAAIQ,IAAI,KAAK,EAAE,IAAI,CAACA,IAAI,EAAE;YACxBA,IAAI,GAAG,KAAK;UACd,CAAC,MAAM;YACL;YACAA,IAAI,GAAGC,IAAI,CAACD,IAAI,CAACE,WAAW,CAAC,CAAC,CAAC;UACjC;QACF;QACA,IAAIH,QAAQ,CAACI,SAAS,EAAE;UACtB,IAAIC,QAAQ,GAAG,CAAC,CAAC;UACjB,IAAI9D,KAAK,CAACkB,iBAAiB,EAAE;YAC3B4C,QAAQ,GAAG9D,KAAK,CAACkB,iBAAiB;UACpC;UACA4C,QAAQ,CAACZ,IAAI,CAAC,GAAGQ,IAAI;UACrBP,MAAM,CAAC,wBAAwB,EAAEW,QAAQ,CAAC;UAC1CrD,YAAY,CAACY,OAAO,CAAC6B,IAAI,EAAEQ,IAAI,CAAC;QAClC;QACAL,OAAO,CAACK,IAAI,CAAC;MACf,CAAC,CAAC,CAACK,KAAK,CAAC,UAAAC,KAAK,EAAI;QAChBV,MAAM,CAACU,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;EACAC,QAAQ,WAARA,QAAQA,CAAAC,KAAA,EAAaC,KAAK,EAAE;IAAA,IAAjBhB,MAAM,GAAAe,KAAA,CAANf,MAAM;IACf,IAAQlD,KAAK,GAAgDkE,KAAK,CAA1DlE,KAAK;MAAEmE,YAAY,GAAkCD,KAAK,CAAnDC,YAAY;MAAE5D,cAAc,GAAkB2D,KAAK,CAArC3D,cAAc;MAAEG,WAAW,GAAKwD,KAAK,CAArBxD,WAAW;IACxDwC,MAAM,CAAC,WAAW,EAAElD,KAAK,CAAC;IAC1BkD,MAAM,CAAC,oBAAoB,EAAE3C,cAAc,IAAI,EAAE,CAAC;IAClDC,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEb,cAAc,IAAI,EAAE,CAAC;IAC5DC,YAAY,CAACY,OAAO,CAAC,aAAa,EAAEV,WAAW,IAAI,EAAE,CAAC;IACtDwC,MAAM,CAAC,4BAA4B,EAAEiB,YAAY,CAAC;IAClDjB,MAAM,CAAC,iBAAiB,EAAExC,WAAW,CAAC;IACtCF,YAAY,CAACY,OAAO,CAAC,wBAAwB,EAAE+C,YAAY,CAAC;IAE5D5E,QAAQ,CAACS,KAAK,CAAC;EACjB,CAAC;EACD;EACAd,KAAK,WAALA,KAAKA,CAAAkF,KAAA,EAAaC,QAAQ,EAAE;IAAA,IAApBnB,MAAM,GAAAkB,KAAA,CAANlB,MAAM;IACZ,IAAQtC,OAAO,GAAoByD,QAAQ,CAAnCzD,OAAO;MAAE0D,GAAG,GAAeD,QAAQ,CAA1BC,GAAG;MAAEC,QAAQ,GAAKF,QAAQ,CAArBE,QAAQ;IAC9B;IACA;IACA;IACA;IACA;IACA,OAAO,IAAIpB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtCnE,MAAK,CAAC;QAAE0B,OAAO,EAAEA,OAAO,CAAC4D,IAAI,CAAC,CAAC;QAAEF,GAAG,EAAE3E,GAAG,CAAC2E,GAAG,CAAC;QAAEG,OAAO,EAAEC,IAAI,CAACJ,GAAG,CAAC,CAACK,QAAQ,CAAC,CAAC;QAAEJ,QAAQ,EAARA;MAAS,CAAC,CAAC,CAAChB,IAAI,CAAC,UAAAC,QAAQ,EAAI;QAC1G,IAAQC,IAAI,GAAKD,QAAQ,CAAjBC,IAAI;QACZ,IAAID,QAAQ,CAACI,SAAS,EAAE;UACtBV,MAAM,CAAC,WAAW,EAAEO,IAAI,CAACmB,KAAK,CAAC;UAC/B1B,MAAM,CAAC,iBAAiB,EAAEO,IAAI,CAAC/C,WAAW,CAAC;UAC3CwC,MAAM,CAAC,oBAAoB,EAAEO,IAAI,CAAClD,cAAc,CAAC;UACjDC,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEqC,IAAI,CAAClD,cAAc,CAAC;UAC3DC,YAAY,CAACY,OAAO,CAAC,aAAa,EAAEqC,IAAI,CAAC/C,WAAW,CAAC;UACrDF,YAAY,CAACY,OAAO,CAAC,iBAAiB,EAAEqC,IAAI,CAAC1C,eAAe,CAAC;UAC7DxB,QAAQ,CAACkE,IAAI,CAACmB,KAAK,CAAC;QACtB;QACAxB,OAAO,CAACI,QAAQ,CAAC;MACnB,CAAC,CAAC,CAACM,KAAK,CAAC,UAAAC,KAAK,EAAI;QAChBV,MAAM,CAACU,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAED;EACA3E,OAAO,WAAPA,OAAOA,CAAAyF,KAAA,EAA8B;IAAA,IAA3B3B,MAAM,GAAA2B,KAAA,CAAN3B,MAAM;MAAEnD,KAAK,GAAA8E,KAAA,CAAL9E,KAAK;MAAE+E,QAAQ,GAAAD,KAAA,CAARC,QAAQ;IAC/B,OAAO,IAAI3B,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtCjE,QAAO,CAAC,CAAC,CAACmE,IAAI,CAAC,UAAAC,QAAQ,EAAI;QACzB,IAAIA,QAAQ,CAACI,SAAS,EAAE;UACtB,IAAQH,IAAI,GAAKD,QAAQ,CAAjBC,IAAI;UACZ,IAAI,CAACA,IAAI,EAAE;YACTJ,MAAM,CAAC,eAAe,CAAC;UACzB;UACA,IAAQ0B,YAAY,GAAwEtB,IAAI,CAAxFsB,YAAY;YAAEC,EAAE,GAAoEvB,IAAI,CAA1EuB,EAAE;YAAEC,OAAO,GAA2DxB,IAAI,CAAtEwB,OAAO;YAAE3E,sBAAsB,GAAmCmD,IAAI,CAA7DnD,sBAAsB;YAAE4E,MAAM,GAA2BzB,IAAI,CAArCyB,MAAM;YAAEC,KAAK,GAAoB1B,IAAI,CAA7B0B,KAAK;YAAEC,aAAa,GAAK3B,IAAI,CAAtB2B,aAAa;UACvFlC,MAAM,CAAC,WAAW,EAAE,CAAC8B,EAAE,CAAC,CAAC;UACzB9B,MAAM,CAAC,UAAU,EAAE6B,YAAY,CAAC;UAChC,IAAIE,OAAO,EAAE;YACX/B,MAAM,CAAC,YAAY,EAAE+B,OAAO,CAAC;UAC/B,CAAC,MAAM;YACLH,QAAQ,CAAC,YAAY,CAAC;UACxB;UACA5B,MAAM,CAAC,YAAY,EAAE8B,EAAE,CAAC;UACxB9B,MAAM,CAAC,aAAa,EAAEkC,aAAa,CAAC;UACpClC,MAAM,CAAC,YAAY,EAAEgC,MAAM,CAAC;UAC5BhC,MAAM,CAAC,WAAW,EAAEiC,KAAK,CAAC;UAC1B;UACAjC,MAAM,CAAC,4BAA4B,EAAE5C,sBAAsB,CAAC;UAC5D;UACA4C,MAAM,CAAC,qBAAqB,EAAEO,IAAI,CAAC1C,eAAe,CAAC;UACnD;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAqC,OAAO,CAACK,IAAI,CAAC;QACf,CAAC,MAAM;UACLJ,MAAM,CAAC,CAAC;QACV;MACF,CAAC,CAAC,CAACS,KAAK,CAAC,UAAAC,KAAK,EAAI;QAChBV,MAAM,CAACU,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDsB,OAAO,WAAPA,OAAOA,CAAAC,KAAA,EAAaC,SAAS,EAAE;IAAA,IAArBrC,MAAM,GAAAoC,KAAA,CAANpC,MAAM;IACd,IAAQ3C,cAAc,GAAwFgF,SAAS,CAA/GhF,cAAc;MAAEG,WAAW,GAA2E6E,SAAS,CAA/F7E,WAAW;MAAEkE,KAAK,GAAoEW,SAAS,CAAlFX,KAAK;MAAEY,WAAW,GAAuDD,SAAS,CAA3EC,WAAW;MAAEC,MAAM,GAA+CF,SAAS,CAA9DE,MAAM;MAAEP,MAAM,GAAuCK,SAAS,CAAtDL,MAAM;MAAEQ,QAAQ,GAA6BH,SAAS,CAA9CG,QAAQ;MAAEpF,sBAAsB,GAAKiF,SAAS,CAApCjF,sBAAsB;IACzG4C,MAAM,CAAC,WAAW,EAAE0B,KAAK,CAAC;IAC1B1B,MAAM,CAAC,iBAAiB,EAAExC,WAAW,CAAC;IACtCwC,MAAM,CAAC,oBAAoB,EAAE3C,cAAc,CAAC;IAC5C2C,MAAM,CAAC,UAAU,EAAEwC,QAAQ,CAAC;IAC5BxC,MAAM,CAAC,YAAY,EAAEuC,MAAM,CAAC;IAC5BvC,MAAM,CAAC,WAAW,EAAE,CAACuC,MAAM,CAAC,CAAC;IAC7BvC,MAAM,CAAC,aAAa,EAAEsC,WAAW,CAAC;IAClCtC,MAAM,CAAC,YAAY,EAAEgC,MAAM,CAAC;IAC5B1E,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEb,cAAc,CAAC;IACtDC,YAAY,CAACY,OAAO,CAAC,aAAa,EAAEV,WAAW,CAAC;IAChD;IACAF,YAAY,CAACY,OAAO,CAAC,OAAO,EAAEwD,KAAK,CAAC;IACpCrF,QAAQ,CAACqF,KAAK,CAAC;EACjB,CAAC;EAEDe,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAaL,SAAS,EAAE;IAAA,IAArBrC,MAAM,GAAA0C,KAAA,CAAN1C,MAAM;IACpB,IAAQ3C,cAAc,GAAkBgF,SAAS,CAAzChF,cAAc;MAAEG,WAAW,GAAK6E,SAAS,CAAzB7E,WAAW;IACnCwC,MAAM,CAAC,iBAAiB,EAAExC,WAAW,CAAC;IACtCwC,MAAM,CAAC,oBAAoB,EAAE3C,cAAc,CAAC;IAC5CC,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAEb,cAAc,CAAC;IACtDC,YAAY,CAACY,OAAO,CAAC,aAAa,EAAEV,WAAW,CAAC;EAClD,CAAC;EAED;EACAvB,MAAM,WAANA,MAAMA,CAAA0G,KAAA,EAA8B;IAAA,IAA3B3C,MAAM,GAAA2C,KAAA,CAAN3C,MAAM;MAAEnD,KAAK,GAAA8F,KAAA,CAAL9F,KAAK;MAAE+E,QAAQ,GAAAe,KAAA,CAARf,QAAQ;IAC9B,OAAO,IAAI3B,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtClE,OAAM,CAAC,CAAC,CAACoE,IAAI,CAAC,YAAM;QAClBL,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvBA,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvB1D,WAAW,CAAC,CAAC;QACbE,WAAW,CAAC,CAAC;QACb;QACA;QACAoF,QAAQ,CAAC,sBAAsB,EAAE,IAAI,EAAE;UAAEgB,IAAI,EAAE;QAAK,CAAC,CAAC;QACtD1C,OAAO,CAAC,CAAC;MACX,CAAC,CAAC,CAACU,KAAK,CAAC,UAAAC,KAAK,EAAI;QAChBV,MAAM,CAACU,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;EACAgC,UAAU,WAAVA,UAAUA,CAAAC,KAAA,EAAa;IAAA,IAAV9C,MAAM,GAAA8C,KAAA,CAAN9C,MAAM;IACjB,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BF,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;MACvBA,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;MACvB+C,OAAO,CAACC,GAAG,CAAC,CAAC,CAAC;MACd1G,WAAW,CAAC,CAAC;MACb4D,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ,CAAC;EAED;EACM+C,WAAW,WAAXA,WAAWA,CAAAC,KAAA,EAAuBC,IAAI,EAAE;IAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAvD,MAAA,EAAA4B,QAAA,EAAA9E,KAAA,EAAA0G,eAAA,EAAAtG,KAAA,EAAAuG,YAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAA1B9D,MAAM,GAAAkD,KAAA,CAANlD,MAAM,EAAE4B,QAAQ,GAAAsB,KAAA,CAARtB,QAAQ;YAC5B9E,KAAK,GAAGqG,IAAI,GAAG,QAAQ;YAC7BnD,MAAM,CAAC,WAAW,EAAElD,KAAK,CAAC;YAC1BT,QAAQ,CAACS,KAAK,CAAC;YAAA8G,QAAA,CAAAE,IAAA;YAAA,OAESlC,QAAQ,CAAC,SAAS,CAAC;UAAA;YAAA4B,eAAA,GAAAI,QAAA,CAAAG,IAAA;YAAnC7G,KAAK,GAAAsG,eAAA,CAALtG,KAAK;YAEbV,WAAW,CAAC,CAAC;;YAEb;YAAAoH,QAAA,CAAAE,IAAA;YAAA,OAC2BlC,QAAQ,CAAC,2BAA2B,EAAE1E,KAAK,EAAE;cAAE0F,IAAI,EAAE;YAAK,CAAC,CAAC;UAAA;YAAjFa,YAAY,GAAAG,QAAA,CAAAG,IAAA;YAClB;YACAxH,MAAM,CAACyH,SAAS,CAACP,YAAY,CAAC;;YAE9B;YACA7B,QAAQ,CAAC,sBAAsB,EAAE,IAAI,EAAE;cAAEgB,IAAI,EAAE;YAAK,CAAC,CAAC;UAAA;UAAA;YAAA,OAAAgB,QAAA,CAAAK,IAAA;QAAA;MAAA,GAAAV,OAAA;IAAA;EACxD,CAAC;EAEDlH,QAAQ,WAARA,QAAQA,CAAA6H,MAAA,EAAapH,KAAK,EAAE;IAAA,IAAjBkD,MAAM,GAAAkE,MAAA,CAANlE,MAAM;IACfA,MAAM,CAAC,WAAW,EAAElD,KAAK,CAAC;EAC5B;AACF,CAAC;AAED,eAAe;EACbqH,UAAU,EAAE,IAAI;EAChBtH,KAAK,EAALA,KAAK;EACLmB,SAAS,EAATA,SAAS;EACT4B,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}