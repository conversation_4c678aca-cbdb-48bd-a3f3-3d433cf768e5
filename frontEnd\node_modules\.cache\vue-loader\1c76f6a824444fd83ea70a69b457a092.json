{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\realVideo\\Screen.vue?vue&type=template&id=19275a03&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\realVideo\\Screen.vue", "mtime": 1754618172895}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1724304688265}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}