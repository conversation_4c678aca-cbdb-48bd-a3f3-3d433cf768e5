<template>
  <div style="height:100%;">
    <tree-detail
      ref="tree"
      show-checkbox
      :expanded-key="expandedKey"
      :loading="treeLoading"
      :tree-data="treeData"
      :can-node-click="false"
      icon="icon-user"
      node-key="Id"
      check-strictly
      :default-checked-keys="defaultCheckedKeys"
      @getCheckedNodes="getCheckedNodes"
      @check="check"
    />
  </div>
</template>

<script>
import { GetGroupTree } from '@/api/sys/user-group'
import treeDetail from '@/components/TreeDetail/wrapperd'
import { disableDirectory } from '@/utils/tree'
export default {
  name: 'UserGroup',
  components: {
    treeDetail
  },
  props: {
    group: {
      type: Array,
      default() {
        return []
      }
    },
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      treeData: [],
      treeLoading: false,
      defaultCheckedKeys: [],
      expandedKey: ''
    }
  },
  mounted() {
    this.fetchTreeData()
  },
  methods: {
    fetchTreeData() {
      GetGroupTree().then((res) => {
        if (res.IsSucceed) {
          disableDirectory(res.Data)
          this.treeData = res.Data
          this.treeLoading = false
          this.$nextTick(_ => {
            this.defaultCheckedKeys = this.group.map(v => v.Id)
          })
        }
      })
    },
    getCheckedData() {
      this.$nextTick(_ => {
        this.$refs.tree.getCheckedNodes()
      })
    },
    getCheckedNodes(checkedNodes) {
      this.$emit('update:group', checkedNodes)
      this.$emit('handleUpdate', 3)
    },
    check({ dataArray, data }) {
      // if (dataArray.checkedKeys.length > 1) {
      //   this.$refs.tree.setCheckedKeys([data.Id])
      // }
    }
  }
}
</script>

<style scoped>

</style>
