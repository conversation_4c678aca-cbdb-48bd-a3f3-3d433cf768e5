<template>
  <div class="c-upload-container">
    <el-upload
      ref="upload"
      drag
      :limit="limit"
      :accept="accept"
      :file-list="fileList"
      :before-upload="importData"
      :auto-upload="autoUpload"
      :on-change="handleChange"
      action="string"
    >
      <svg-icon icon-class="upload-icon" class-name="cs--icon-upload" />
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em>
        <div slot="tip" class="el-upload__tip">支持格式： {{ accept }}，{{ tip }}</div>
      </div>
    </el-upload>
  </div>
</template>

<script>
export default {
  props: {
    autoUpload: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: 1
    },
    accept: {
      type: String,
      default: '.xls, .xlsx'
    },
    tip: {
      type: String,
      default: '最大5M'
    },
    fileList: {
      type: Array,
      default: () => ([])
    },
    beforeUpload: Function, // eslint-disable-line
    onChange:Function,// eslint-disable-line
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    importData(file) {
      // const fileFormData = new FormData()
      // fileFormData.append('files', file)
      // 执行上传excel
      this.beforeUpload && this.beforeUpload(file)
      // const k = document.getElementsByClassName('el-upload-list__item-name')
      // k.forEach((element, idx) => {
      //   console.log('element', element)
      //   element.appendChild('<div>ddsd</div>')
      // })
      return false
    },
    handleSubmit() {
      this.$refs.upload.submit()
    },
    handleChange(file, fileList) {
      console.log(file, fileList)
      this.onChange && this.onChange(file, fileList)
    }
  }
}
</script>

<style scoped lang="scss">

.drop{
  border: 1px dashed #55A8FD;
  width: 100%;
  margin: 0 auto;
  font-size: 14px;
  border-radius: 5px;
  text-align: center;
  position: relative;

}
.c-upload-container{
  ::v-deep{
    .el-upload,.el-upload-dragger{
      width: 100%;
    }
    .el-upload-list__item{
      background: #fff;
      height: 50px;
      line-height: 50px;
    }
    .el-icon-close{
      top: 18px;
      right: 17px;
    }
  }
}

.cs--icon-upload{
  font-size: 96px;
  margin-top: 16px;
}
.el-upload__tip{
  font-size: 12px;
  color: rgba(34, 40, 52, 0.24);
}
</style>
