<template>
  <div style="height:100%;">
    <tree-detail
      ref="tree"
      :expand-on-click-node="false"
      show-checkbox
      check-strictly
      icon="icon-users"
      :loading="treeLoading"
      :tree-data="treeData"
      :default-checked-keys="defaultCheckedKeys"
      @getCheckedNodes="getCheckedNodes"
    />
  </div>
</template>

<script>
import TreeDetail from '@/components/TreeDetail/wrapperd'
import { GetDepartmentTree } from '@/api/sys'

export default {
  components: {
    TreeDetail
  },
  props: {
    departments: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      expandedKey: '',
      treeLoading: false,
      treeData: [],
      defaultCheckedKeys: []
    }
  },
  mounted() {
    this.fetchTreeData()
  },
  methods: {
    fetchTreeData() {
      this.treeLoading = true
      GetDepartmentTree().then(res => {
        this.treeData = res.Data
        this.treeLoading = false
        this.$nextTick(_ => {
          this.defaultCheckedKeys = this.departments.map(v => v.Id)
        })
      })
    },
    getCheckedData() {
      this.$nextTick(_ => {
        this.$refs.tree.getCheckedNodes()
      })
    },
    getCheckedNodes(checkedNodes) {
      this.$emit('update:departments', checkedNodes)
      this.$emit('handleUpdate', 2)
    }
  }
}
</script>

<style scoped>

</style>
