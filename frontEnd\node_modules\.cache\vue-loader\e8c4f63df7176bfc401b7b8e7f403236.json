{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\index.vue?vue&type=style&index=0&id=374a1912&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\index.vue", "mtime": 1754614843771}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZWxlQm94IHsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis/eleNew", "sourcesContent": ["<template>\n  <div class=\"eleBox\">\n    <el-row :gutter=\"16\">\n      <el-col :span=\"Is_Photovoltaic ? 8 : 8\">\n        <electricity-usage />\n      </el-col>\n      <el-col v-show=\"Is_Photovoltaic\" :span=\"8\">\n        <photovoltaic />\n      </el-col>\n      <el-col :span=\"Is_Photovoltaic ? 8 : 16\">\n        <generation :is-photovoltaic=\"Is_Photovoltaic\" />\n      </el-col>\n      <el-col :span=\"16\">\n        <useEleKJ />\n      </el-col>\n      <el-col :span=\"4\">\n        <ZGFactory />\n      </el-col>\n      <el-col :span=\"4\">\n        <electricityLoss />\n      </el-col>\n      <el-col :span=\"16\">\n        <useEleGX />\n      </el-col>\n      <el-col :span=\"8\">\n        <equipmentUsage />\n      </el-col>\n    </el-row>\n    <!-- <elePic /> -->\n  </div>\n</template>\n\n<script>\nimport electricityUsage from './components/electricityUsage'\nimport photovoltaic from './components/photovoltaic'\nimport generation from './components/generation'\nimport useEleKJ from './components/useEleKJ'\nimport ZGFactory from './components/ZGFactory'\nimport electricityLoss from './components/electricityLoss'\nimport useEleGX from './components/useEleGX'\nimport equipmentUsage from './components/equipmentUsage'\nimport elePic from './components/elePic'\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\n\nexport default {\n  components: {\n    electricityUsage,\n    photovoltaic,\n    generation,\n    useEleKJ,\n    ZGFactory,\n    electricityLoss,\n    useEleGX,\n    equipmentUsage,\n    elePic\n  },\n  data() {\n    return {\n      Is_Photovoltaic: false\n    }\n  },\n  created() {\n\n  },\n  mounted() {\n    GetPreferenceSettingValue({ Code: 'Is_Photovoltaic' }).then((res) => {\n      if (res.IsSucceed) {\n        this.Is_Photovoltaic = res.Data === 'true'\n      }\n    })\n  },\n  methods: {\n\n  }\n}\n</script>\n<style scoped lang='scss'>\n.eleBox {\n}\n</style>\n"]}]}