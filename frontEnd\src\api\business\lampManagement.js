import request from '@/utils/request'
import qs from 'qs'

//  人员管理
// #region
// 获取用户列表
export function GetPageList(data) {
  return request({
    method: 'post',
    url: '/DF/AndonUser/GetPageList',
    data
  })
}

// 保存用户信息
export function SaveUserInfo(data) {
  return request({
    method: 'post',
    url: '/DF/AndonUser/SaveUserInfo',
    data
  })
}

// 删除用户信息
export function DelUser(data) {
  return request({
    method: 'post',
    url: '/DF/AndonUser/DelUser',
    data
  })
}

// 下载人员导入模板
export function DownloadTemplate(data) {
  return request({
    method: 'Get',
    url: '/DF/AndonUser/DownloadTemplate',
    params: data,
    responseType: 'blob'
  })
}
// 导入
export function ImportDataStream(data) {
  return request({
    method: 'post',
    url: '/DF/AndonUser/ImportDataStream',
    data
  })
}
// #endregion

// 安灯汇总
// #region
// 获取标题统计信息
export function GetStatistics(data) {
  return request({
    method: 'Get',
    url: '/DF/AndonWorkorder/GetStatistics',
    params: data
  })
}

// 获取工单列表
export function GetWorkorderPageList(data) {
  return request({
    method: 'post',
    url: '/DF/AndonWorkorder/GetPageList',
    data
  })
}
// #endregion

// 安灯统计分析
// #region
// 查询不满意统计信息
export function GetUnsatisfiedList(data) {
  return request({
    method: 'get',
    url: '/DF/AndonWorkorder/GetUnsatisfiedList',
    params: data
  })
}

// 获取所有单据类别数量
export function GetWorkorderPrecentage(data) {
  return request({
    method: 'get',
    url: '/DF/AndonWorkorder/GetWorkorderPrecentage',
    params: data
  })
}

// 获取工程师满意度排名
export function GetEngineerSatisfied(data) {
  return request({
    method: 'get',
    url: '/DF/AndonWorkorder/GetEngineerSatisfied',
    params: data
  })
}

// 获取问题满意度排名
export function GetProblemSatisfied(data) {
  return request({
    method: 'get',
    url: '/DF/AndonWorkorder/GetProblemSatisfied',
    params: data
  })
}

// 获取问题满意度排名
export function GetYearSatisfied(data) {
  return request({
    method: 'get',
    url: '/DF/AndonWorkorder/GetYearSatisfied',
    params: data
  })
}

// 获取投诉类型统计分析
export function GetComplaintPrecentage(data) {
  return request({
    method: 'get',
    url: '/DF/AndonWorkorder/GetComplaintPrecentage',
    params: data
  })
}

// 获取单据统计分析
export function GetUserList(data) {
  return request({
    method: 'get',
    url: '/DF/AndonWorkorder/GetUserList',
    params: data
  })
}

// #endregion

// 操作日志
// #region

// 获取操作日志列表信息
export function GetOperateLogPageList(data) {
  return request({
    method: 'post',
    url: '/DF/AndonOperateLog/GetPageList',
    data
  })
}

// 导出操作日志记录
export function ExportExcelAsync(data) {
  return request({
    method: 'post',
    url: '/DF/AndonOperateLog/ExportExcel',
    data,
    responseType: 'blob'
  })
}

// 清空操作日志记录
export function ClearOpearteLog(data) {
  return request({
    method: 'get',
    url: '/DF/AndonOperateLog/ClearOpearteLog',
    params: data
  })
}

// #endregion

// 安灯配置
// #region
// 获取配置页面信息
export function GetConfigInfo(data) {
  return request({
    method: 'post',
    url: '/DF/AndonConfig/GetConfigInfo',
    data
  })
}
// 保存安灯配置
export function SaveConfig(data) {
  return request({
    method: 'post',
    url: '/DF/AndonConfig/SaveConfig',
    data
  })
}

// 保存用户信息
export function SaveConfigUserInfo(data) {
  return request({
    method: 'post',
    url: '/DF/AndonConfig/SaveUserInfo',
    data
  })
}

// 删除用户
export function DelConfigUser(data) {
  return request({
    method: 'post',
    url: '/DF/AndonConfig/DelUser',
    data
  })
}

