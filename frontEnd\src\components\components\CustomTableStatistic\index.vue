
<template>
  <div class="custom-table-statistic">
    <div v-for="(item, index) in customTableStatisticObj.statisticList" :key="index">
      <span class="title">{{ item.title }}：</span>
      <span class="value">{{ item.value }}</span>
      <span class="unit">{{ item.unit }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomTableStatistic',
  props: {
    customTableStatisticConfig: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {
    customTableStatisticObj() {
      return this.customTableStatisticConfig
    }
  },
  created() {
    console.log('customTableStatisticConfig', this.customTableStatisticConfig)
  },

  methods: {}
}
</script>

<style lang="scss" scoped>
  .custom-table-statistic {
    display: flex;
    justify-content: flex-start;
    & > div {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-right: 30px;
      span.title {
        color: #666666;
      }
      span.value {
        color: rgb(41, 141, 255);
      }
    }
    & > div:last-child {
      margin-right: 0;
    }
  }
</style>
