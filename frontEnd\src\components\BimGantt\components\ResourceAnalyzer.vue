<template>
  <div class="task-detail">
    <el-link
      :underline="false"
      icon="el-icon-close"
      class="closeme"
      @click="closeMe"
    />
    <el-tabs v-model="tabName" type="border-card">
      <el-tab-pane label="人工资源分析" name="analzer">
        <div class="a-panel">
          <div class="board">
            <div class="item">
              <el-image style="width: 184px; height: 104px" :src="boards.p" />
              <div class="content">
                <h3>计划总工日</h3>
                <p><strong>{{Number(planNum||0).toFixed(2)}}</strong></p>
              </div>
            </div>
            <div class="item">
              <el-image style="width: 184px; height: 104px" :src="boards.b" />
              <div class="content">
                <h3>实际总工日</h3>
                <p><strong>{{Number(actNum||0).toFixed(2)}}</strong></p>
              </div>
            </div>
            <div class="item">
              <el-image style="width: 184px; height: 104px" :src="boards.y" />
              <div class="content">
                <h3>剩余总工日</h3>
                <p><strong>{{Number(subNum||0).toFixed(2)}}</strong></p>
              </div>
            </div>
            <div class="item">
              <el-image style="width: 184px; height: 104px" :src="boards.o" />
              <div class="content">
                <h3>高峰期预计工日</h3>
                <p><strong>{{Number(highNum||0).toFixed(2)}}</strong></p>
              </div>
            </div>
          </div>

          <div class="chart-wrap">
            <span>单位：工日</span>
            <div id="analyzer-charts" />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import bgp from '@/assets/gantt/gantt-chartbg-1.png'
import bgb from '@/assets/gantt/gantt-chartbg-2.png'
import bgy from '@/assets/gantt/gantt-chartbg-3.png'
import bgo from '@/assets/gantt/gantt-chartbg-4.png'

import * as echarts from 'echarts'
import { GetPlanResouseReport } from '@/api/plan/index'

const chartParts = [
  {
    legend: '计划人工',
    type: 'bar',
    itemStyle: {
      color: '#2962FF'
    },
    prop:'PlanManDay'
  },
  {
    legend: '实际人工',
    type: 'bar',
    itemStyle: {
      color: '#11C6FF'
    },
    prop:'ActualManDay'
  },
  {
    legend: '计划累计',
    type: 'line',
    yAxisIndex: 1,
    lineStyle: {
      color: '#FF75B1'
    },
    itemStyle: {
      color: '#FF75B1'
    },
    prop:'SumPlanManDay'
  },
  {
    legend: '实际累计',
    type: 'line',
    yAxisIndex: 1,
    lineStyle: {
      color: '#F5C15A'
    },
    itemStyle: {
      color: '#F5C15A'
    },
    prop:'SumActualManDay'
  }
]
export default {
  name: 'ResourceAnalyzer',
  props: {
    plan: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tabName: 'analzer',
      chart: null,
      option: {
        tooltip: {
          trigger: 'axis',
          appendToBody: true,
          backgroundColor: '#434F71',
          borderColor: '#434F71',
          textStyle: {
            color: '#FFF'
          },
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        grid: {
          right: 140,
          left: 50,
          top: 18,
          bottom: 64
        },
        legend: {
          left: 'right',
          orient: 'vertical',
          top: 32,
          align: 'right',
          data: chartParts.map(p => p.legend)
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            splitLine:{
              show:true,
            },
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '',
            min: 0,
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: '',
            min: 0,
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: []
      },
      boards: {
        p: bgp,
        b: bgb,
        y: bgy,
        o: bgo
      },
      report: [],
      planNum:0,
      actNum:0,
      subNum:0,
      highNum:0
    }
  },
  computed: {
    computedMonths(){
      return this.report.map(r=>{
        return `${r.Year}-${r.Month}`
      })
    },
    dataSeries() {
      return chartParts.map(p => {
        let data = []
        data = this.report.map(r=>r[p.prop])
        const d = {
          ...p,
          name: p['legend'],
          data: data
        }
        delete d['legend']
        delete d['prop']
        return d
      })
    }
  },
  created() {

  },
  mounted() {
    this.chart = echarts.init(document.getElementById('analyzer-charts'))
    // const option = {
    //   ...this.option,
    //   series: this.dataSeries
    // }
    // console.log(option)
    // this.chart.setOption(option)
    if (this.plan && this.plan.Id) {
      this.getReport().then(()=>{
        this.option.xAxis[0].data = this.computedMonths
        const option = {
          ...this.option,
          series: this.dataSeries
        }
        console.log(option)
        this.chart.setOption(option)
      })
    }
  },
  methods: {
    closeMe() {
      this.$emit('drawerCancel')
    },
    getReport() {
      return GetPlanResouseReport(this.plan.Id, 'month').then(res => {
        if (res.IsSucceed) {
          this.report = res.Data
          if(res.Data != null && res.Data.length > 0){
            this.planNum = res.Data[res.Data.length - 1].SumPlanManDay
            this.actNum = res.Data[res.Data.length - 1].SumActualManDay
            this.subNum = this.planNum - this.actNum
            this.highNum = res.Data[res.Data.length - 1].HighManDay
          }

        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.a-panel {
  height: 100%;
  display: flex;
  flex-direction: row;
  .board {
    width: 400px;
    flex-shrink: 0;
  }
  .chart-wrap {
    flex: 1;
  }
}
.board {
  overflow: hidden;
  padding-top: 16px;
  .item {
    position: relative;
    margin-top: -16px;
    float: left;
  }
  .content {
    position: absolute;
    z-index: 1;
    color: #fff;
    top: 18px;
    left: 26px;
    h3 {
      padding: 0;
      margin: 0;
      font-size: 1.2em;
      font-weight: bold;
      margin-bottom: 8px;
      margin-top: 4px;
    }
    p {
      color: #ffffff66;
    }
    strong {
      font-size: 1.2em;
      font-weight: bold;
      color: #fff;
    }
  }
}
.chart-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  span {
    position: absolute;
    right: 14px;
    top: 2px;
    z-index: 1;
    color: #999;
  }
  #analyzer-charts {
    flex: auto;
  }
}
</style>
