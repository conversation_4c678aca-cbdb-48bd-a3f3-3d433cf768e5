<template>
  <div>
    <template v-for="(item, idx) in obj.children">
      <el-table
        ref="mtable"
        :key="idx"
        :show-header="false"
        :class="{ left: true, leaf: !item.children }"
        :title="item.title"
        :data="[{}]"
        :cell-class-name="customCellCls"
      >
        <el-table-column
          label=""
          prop="title"
          :width="item.width"
          :show-overflow-tooltip="false"
        >
          <div :class="{ leafcell: !item.children }">
            {{ item.title }}
          </div>
        </el-table-column>
        <el-table-column v-if="item.children && item.children.length>0" label="" prop="title">
          <Cascaded :obj="item" />
        </el-table-column>
      </el-table>
    </template>
  </div>
</template>
<script>
export default {
  name: 'Cascaded',
  props: {
    obj: {
      type: Object,
      default: () => ({})
    },
    lineHeight: {
      type: Number,
      default: 68
    }
  },
  mounted() {},
  methods: {
    customCellCls({ row, column, rowIndex, columnIndex }) {
      return 'aaa'
    },
    leafHeightStyle(isLeaf) {
      if (isLeaf) {
        return {
          height: `${this.lineHeight}px`
        }
      } else {
        return {}
      }
    }
  }
}
</script>
