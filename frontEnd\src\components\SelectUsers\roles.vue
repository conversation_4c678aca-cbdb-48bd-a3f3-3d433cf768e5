<template>
  <div style="height:100%;">
    <tree-detail
      ref="tree"
      show-checkbox
      :expanded-key="expandedKey"
      :loading="treeLoading"
      :tree-data="treeData"
      :can-node-click="false"
      icon="icon-user"
      :default-checked-keys="defaultCheckedKeys"
      @getCheckedNodes="getCheckedNodes"
    />
  </div>
</template>

<script>
import TreeDetail from '@/components/TreeDetail/wrapperd'
import { GetRoleTree } from '@/api/sys/role'

export default {
  components: {
    TreeDetail
  },
  props: {
    roles: {
      type: Array,
      default() {
        return []
      }
    },
    show: {
      type: <PERSON>olean,
      default: false
    }
  },
  data() {
    return {
      expandedKey: '',
      treeLoading: false,
      treeData: [],
      checkedNodes: [],
      defaultCheckedKeys: []
    }
  },
  mounted() {
    this.fetchTreeData()
  },
  methods: {
    fetchTreeData() {
      this.treeLoading = true
      GetRoleTree().then(res => {
        this.treeData = res.Data
        this.treeLoading = false
        this.$nextTick(_ => {
          this.defaultCheckedKeys = this.roles.map(v => v.Id)
        })
      })
    },
    getCheckedData() {
      this.$refs.tree.getCheckedNodes(true)
    },
    getCheckedNodes(checkedNodes) {
      this.checkedNodes = checkedNodes.filter(v => !v.Is_Directory)
      // const ids = this.checkedNodes.map(item => item.Id)
      this.$emit('update:roles', this.checkedNodes)
      this.$emit('handleUpdate', 0)
    }
  }
}
</script>

<style scoped>

</style>
