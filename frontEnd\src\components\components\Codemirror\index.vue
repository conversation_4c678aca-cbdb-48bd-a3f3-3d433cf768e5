<template>
  <div class="pagebody">
    <codemirror ref="mycode" :value="code" :options="cmOptions" class="code"></codemirror>
    <el-button class="download" @click="download">下载文档</el-button>
  </div>
</template>
<script>
import { codemirror } from 'vue-codemirror'
import "codemirror/theme/ambiance.css"; // 这里引入的是主题样式，根据设置的theme的主题引入，一定要引入！！
require("codemirror/mode/javascript/javascript"); // 这里引入的模式的js，根据设置的mode引入，一定要引入！！
import { saveAs } from 'file-saver';
export default {
  name: 'MyCodeMirror',
  data() {
    return {
      code:'123',
      extension:'.vue',
      cmOptions: {
        // autorefresh: true,
        tabSize: 8,
        // theme: 'ayu-mirage',
        line: true,
        lineNumbers: true,
        // 软换行
        lineWrapping: true,
        // viewportMargin: Infinity, //处理高度自适应时搭配使用
        highlightDifferences: true,
        // autofocus: false,
        // indentUnit: 2,
        // smartIndent: true,
        // readOnly: true, // 只读
        // showCursorWhenSelecting: true,
        // firstLineNumber: 1
        lineNumbers: true, // 显示行号
        mode: 'application/json', // 语法model
        theme: "ambiance",
        lint: true, // 开启语法检查
        autoCloseBrackets: true,
        autoCloseTags: true,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers'],
      }
    }
  },
  components: {
    codemirror
  },
  mounted(){
    this.editor = this.$refs.mycode.codemirror;
    // 设置codemirror高度为450
    this.editor.setSize("auto", 'calc(100vh - 10px)');
  },
  methods: {
    initCode(val,extension) {
      this.code = val
      this.extension=extension||'.vue'
    },
    download(){
      let strData = new Blob([this.code], { type: 'text/plain;charset=utf-8' }); 
      saveAs(strData, `自动生成文件${this.extension}`);
    }
  }

}

</script>
  
  
<style scoped>
.information-box>>>.CodeMirror {
  font-family: monospace;
  direction: ltr;
}
.pagebody{
  position: relative;
}
.download{
  position: absolute;
  top:10px;
  right:30px;
}
</style>
 