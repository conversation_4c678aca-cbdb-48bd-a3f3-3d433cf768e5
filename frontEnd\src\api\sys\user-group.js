import request from '@/utils/request'

// 获取群组列表
export function GetGroupList() {
  return request({
    url: '/Platform/UserGroup/GetGroupList',
    method: 'post'
  })
}

// 获取群组树
export function GetGroupTree(data) {
  return request({
    url: '/Platform/UserGroup/GetGroupTree',
    method: 'post',
    data
  })
}

// 获取群组实体
export function GetGroupEntity(data) {
  return request({
    url: '/Platform/UserGroup/GetGroupEntity',
    method: 'post',
    data
  })
}

// 添加/更新组
export function SaveGroup(data) {
  return request({
    url: '/Platform/UserGroup/SaveGroup',
    method: 'post',
    data
  })
}

// 删除群组
export function DeleteGroup(data) {
  return request({
    url: '/Platform/UserGroup/DeleteGroup',
    method: 'post',
    data
  })
}

// 添加用户到指定的组
export function SaveGroupUser(data) {
  return request({
    url: '/Platform/UserGroup/SaveGroupUser',
    method: 'post',
    data
  })
}

// 删除群组里的用户
export function DeleteGroupUser(data) {
  return request({
    url: '/Platform/UserGroup/DeleteGroupUser',
    method: 'post',
    data
  })
}

// 获取用户群组用户列表
export function GetGroupUser(data) {
  return request({
    method: 'post',
    url: '/Platform/UserGroup/GetGroupUser',
    data
  })
}

// 获取用户组角色
export function GetGroupRole(data) {
  return request({
    method: 'post',
    url: '/Platform/UserGroup/GetGroupRole',
    data
  })
}

export function GetRoleListCanAdd(data) {
  return request({
    method: 'post',
    url: 'sys/UserGroup/GetRoleListCanAdd',
    data
  })
}

// 删除用户组角色
export function DeleteGroupRole(data) {
  return request({
    method: 'post',
    url: '/Platform/UserGroup/DeleteGroupRole',
    data
  })
}

// 保存用户组
export function SaveGroupRole(data) {
  return request({
    method: 'post',
    url: '/Platform/UserGroup/SaveGroupRole',
    data
  })
}

// 保存用户授权
export function SaveGroupObject(data) {
  return request({
    method: 'post',
    url: '/Platform/UserGroup/SaveGroupObject',
    data
  })
}

// 获取群组数据权限树
export function GetWorkingObjTreeListByGroupId(data) {
  return request({
    method: 'post',
    url: '/Platform/UserGroup/GetWorkingObjTreeListByGroupId',
    data
  })
}

// 获取用户组对应角色的人员
export function GetGroupUserByRole(data) {
  return request({
    method: 'post',
    url: '/Platform/UserGroup/GetGroupUserByRole',
    data
  })
}

// 群组人员和已选列表
export function GetShuttleUserList(data) {
  return request({
    method: 'post',
    url: '/Platform/UserGroup/GetShuttleUserList',
    data
  })
}

// 保存用户组对应角色下的人员
export function SaveUserRoles(data) {
  return request({
    method: 'post',
    url: '/Platform/UserGroup/SaveUserRoles',
    data
  })
}

// 删除用户组对应角色下的人员
export function DeleteUserRole(data) {
  return request({
    method: 'post',
    url: '/Platform/UserGroup/DeleteUserRole',
    data
  })
}
