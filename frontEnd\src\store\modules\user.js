import { login, logout, getInfo, getConfigure } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import router, { resetRouter } from '@/router'
import md5 from 'md5'
import CryptoJS from 'crypto-js'
import { loginKey } from '@/utils/constants'
import { GetEntity } from '@/api/sys/company-info'

const state = {
  token: getToken(),
  name: '',
  avatar: '',
  introduction: '',
  roles: [],
  userId: '',
  Last_Working_Object_Id: '',
  CurReferenceId: localStorage.getItem('CurReferenceId'),
  CurPlatform: localStorage.getItem('Platform'),
  Language: localStorage.getItem('Language'),
  account: '',
  mobile: localStorage.getItem('Mobile'),
  email: '',
  Last_Product_Id: localStorage.getItem('Last_Product_Id'),
  HomePageUrl: localStorage.getItem('HomePageUrl'),
  PreferenceSetting: {}
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
    if (token === '') {
      localStorage.setItem('Token', '')
      location.reload()
      // if (router.history._startLocation === '/produce/qualityScreenHome') {
      //   // window.history.pushState(null, '', '/login?autoLogin=true&tenantID=scyth_test&account=czy&pwd=123&redirect=produce/qualityScreenHome')
      //   window.history.pushState(null, '', '/login?autoLogin=true&tenantID=hceg&account=zjdp&pwd=1decda52b02e5640266cfa1bcd9f00f1&redirect=produce/qualityScreenHome')
      // } else {
      //   window.history.pushState(null, '', '/login')
      // }
      if (router.history._startLocation.includes('isAuthLogin=true')) {
        window.location.href = localStorage.getItem('autoLoginUrl')
      } else {
        window.location.href = window.location.origin
      }
    }
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ACCOUNT: (state, account) => {
    state.account = account
  },
  SET_MOBILE: (state, mobile) => {
    state.mobile = mobile
  },
  SET_EMAIL: (state, email) => {
    state.email = email
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_USERID: (state, userId) => {
    state.userId = userId
  },
  SET_LAST_WORKING_OBJECT_ID: (state, Last_Working_Object_Id) => {
    state.Last_Working_Object_Id = Last_Working_Object_Id
  },
  SET_CURREFERENCEID: (state, CurReferenceId) => {
    state.CurReferenceId = CurReferenceId
  },
  SET_CURPLATFORM: (state, CurPlatform) => {
    state.CurPlatform = CurPlatform
  },
  SET_LANGUAGE: (state, Language) => {
    state.Language = Language
  },
  SET_LAST_PRODUCT_ID: (state, Last_Product_Id) => {
    state.Last_Product_Id = Last_Product_Id
  },
  SET_HOMEPAGE_URL: (state, Home_Page_Url) => {
    state.HomePageUrl = Home_Page_Url
  },
  SET_PREFERENCE_SETTING: (state, preference) => {
    state.PreferenceSetting = preference
  }
}

const actions = {
  getPreferenceSetting({ commit, state }, code) {
    return new Promise((resolve, reject) => {
      getConfigure({ Code: code }).then(response => {
        let { Data } = response
        if (code === 'Is_Integration') {
          if (Data === '' || !Data) {
            Data = false
          } else {
            // eslint-disable-next-line no-eval
            Data = eval(Data.toLowerCase())
          }
        }
        if (response.IsSucceed) {
          let dataJson = {}
          if (state.PreferenceSetting) {
            dataJson = state.PreferenceSetting
          }
          dataJson[code] = Data
          commit('SET_PREFERENCE_SETTING', dataJson)
          localStorage.setItem(code, Data)
        }
        resolve(Data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  // 第三方登录、东航
  ssoLogin({ commit }, query) {
    const { token, workingobjID, CurReferenceId, CurPlatform } = query
    commit('SET_TOKEN', token)
    commit('SET_CURREFERENCEID', CurReferenceId || '')
    localStorage.setItem('CurReferenceId', CurReferenceId || '')
    localStorage.setItem('CurPlatform', CurPlatform || '')
    commit('SET_LAST_WORKING_OBJECT_ID', workingobjID)
    commit('SET_CURPLATFORM', CurPlatform)
    localStorage.setItem('Last_Working_Object_Id', workingobjID)

    setToken(token)
  },
  // user login
  login({ commit }, userInfo) {
    const { account, pwd, tenantID } = userInfo
    // const userpwd = CryptoJS.AES.encrypt(pwd, loginKey, {
    //   iv: '****************',
    //   mode: CryptoJS.mode.CBC,
    //   padding: CryptoJS.pad.Pkcs7
    // })
    return new Promise((resolve, reject) => {
      login({ account: account.trim(), pwd: md5(pwd), userpwd: btoa(pwd).toString(), tenantID }).then(response => {
        const { Data } = response
        if (response.IsSucceed) {
          commit('SET_TOKEN', Data.Token)
          commit('SET_CURPLATFORM', Data.CurPlatform)
          commit('SET_CURREFERENCEID', Data.CurReferenceId)
          localStorage.setItem('CurReferenceId', Data.CurReferenceId)
          localStorage.setItem('CurPlatform', Data.CurPlatform)
          localStorage.setItem('Last_Product_Id', Data.Last_Product_Id)
          setToken(Data.Token)
        }
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  getInfo({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        if (response.IsSucceed) {
          const { Data } = response
          if (!Data) {
            reject('身份验证过期，请重新登陆！')
          }
          const { Display_Name, Id, Picture, Last_Working_Object_Id, Mobile, Email, Login_Account } = Data
          commit('SET_ROLES', [Id])
          commit('SET_NAME', Display_Name)
          if (Picture) {
            commit('SET_AVATAR', Picture)
          } else {
            dispatch('getCompany')
          }
          commit('SET_USERID', Id)
          commit('SET_ACCOUNT', Login_Account)
          commit('SET_MOBILE', Mobile)
          commit('SET_EMAIL', Email)
          // localStorage.setItem('Mobile', Mobile)
          commit('SET_LAST_WORKING_OBJECT_ID', Last_Working_Object_Id)
          // localStorage.setItem('Last_Working_Object_Id', Last_Working_Object_Id)
          commit('SET_LAST_PRODUCT_ID', Data.Last_Product_Id)
          // localStorage.setItem('Last_Product_Id', Data.Last_Product_Id)

          // getConfigure({ code: 'Language' }).then(res => {
          //   if (res.Data) {
          //     commit('SET_LANGUAGE', res.Data)
          //     localStorage.setItem('Language', res.Data)
          //   } else {
          //     commit('SET_LANGUAGE', 'zh-cn')
          //     localStorage.setItem('Language', 'zh-cn')
          //   }
          //   // console.log(localStorage.GetItem('Language'))
          // })
          resolve(Data)
        } else {
          reject()
        }
      }).catch(error => {
        reject(error)
      })
    })
  },

  setInfo({ commit }, stateInfo) {
    const { CurReferenceId, CurPlatform, Token, UserAccount, UserId, Mobile, UserName, Last_Working_Object_Id } = stateInfo
    commit('SET_TOKEN', Token)
    commit('SET_CURPLATFORM', CurPlatform)
    commit('SET_CURREFERENCEID', CurReferenceId)
    commit('SET_NAME', UserName)
    commit('SET_USERID', UserId)
    commit('SET_ROLES', [UserId])
    commit('SET_ACCOUNT', UserAccount)
    commit('SET_MOBILE', Mobile)
    localStorage.setItem('CurReferenceId', CurReferenceId)
    localStorage.setItem('CurPlatform', CurPlatform)
    // localStorage.setItem('Last_Working_Object_Id', Last_Working_Object_Id)
    localStorage.setItem('Token', Token)
    setToken(Token)
  },

  setActionInfo({ commit }, stateInfo) {
    const { CurReferenceId, CurPlatform } = stateInfo
    commit('SET_CURPLATFORM', CurPlatform)
    commit('SET_CURREFERENCEID', CurReferenceId)
    localStorage.setItem('CurReferenceId', CurReferenceId)
    localStorage.setItem('CurPlatform', CurPlatform)
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout().then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        removeToken()
        resetRouter()
        // reset visited views and cached views
        // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
        dispatch('tagsView/delAllViews', null, { root: true })
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      console.log(2)
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({ commit, dispatch }, role) {
    const token = role + '-token'
    commit('SET_TOKEN', token)
    setToken(token)

    const { roles } = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, { root: true })
  },

  setToken({ commit }, token) {
    commit('SET_TOKEN', token)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
