import request from '@/utils/request'
//获取工单总览统计
export function GetWorkorderStatistics(data) {
    return request({
        method: 'post',
        url: '/DF/WorkOrderAnalyse/GetWorkorderStatistics',
        data
    })
}
//获取维保日历
export function GetOverView(params) {
    return request({
        method: 'get',
        url: '/DF/WorkOrderAnalyse/GetOverView',
        params
    })
}
//获取处理人员完成排名
export function GetProcessedRank(data) {
    return request({
        method: 'post',
        url: '/DF/WorkOrderAnalyse/GetProcessedRank',
        data
    })
}
//获取工单响应超时统计
export function GetTimeoutStatistics(data) {
    return request({
        url: '/DF/WorkOrderAnalyse/GetTimeoutStatistics',
        method: 'post',
        data
    })
}
//获取异常维保项排行 - 维保
export function GetAbnormalMaintenanceRank(data) {
    return request({
        url: '/DF/WorkOrderAnalyse/GetAbnormalMaintenanceRank',
        method: 'post',
        data
    })
}
//获取各车间工单情况
export function GetWorkShopCase(data) {
    return request({
        url: '/DF/WorkOrderAnalyse/GetWorkShopCase',
        method: 'post',
        data
    })
}
//获取各车间趋势
export function GetWorkOrderTrend(data) {
    return request({
        url: '/DF/WorkOrderAnalyse/GetWorkOrderTrend',
        method: 'post',
        data
    })
}
//待办维保
export function GetWorkOrderManageList(data) {
    return request({
        url: '/PFI/Plm_WorkOrder_Setup/GetWorkOrderManageList',
        method: 'post',
        data
    })
}
//跳转
export function GetJumpUrl() {
    return request({
        url: '/DF/WorkOrderAnalyse/GetJumpUrl',
        method: 'get',
    })
}