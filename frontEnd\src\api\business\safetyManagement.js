import request from "@/utils/request";
//  设备管理
export function GetEquipmentList(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreEquipment/GetEquipmentList",
    data,
  });
}
export function GetEquipmentListSZCJ(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreEquipment/GetEquipmentList/szcj",
    data,
  });
}
//  设备详情
export function MonitoreEquipmentInfo(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreEquipment/MonitoreEquipmentInfo",
    data,
  });
}
//  新增/编辑设备
export function SubEquipment(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreEquipment/SubEquipment",
    data,
  });
}
//  删除设备
export function DelEquipment(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreEquipment/DelEquipment",
    data,
  });
}
//  查看视频
export function LookVideo(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreEquipment/LookVideo",
    data,
  });
}
//  下载模板
export function MonitoreImportTemplate(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreEquipment/MonitoreImportTemplate",
    data,
  });
}
//  批量导入
export function MonitoreEquipmentImport(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreEquipment/MonitoreEquipmentImport",
    data,
  });
}
//  批量导出
export function ExportMonitoreEquipment(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreEquipment/ExportMonitoreEquipment",
    data,
  });
}
// v2 安防监控设备批量导出
export function ExportEquipmentList(data) {
  return request({
    method: 'post',
    url: "/DF/MonitoreEquipment/v2/ExportEquipmentList",
    data
  })
}
//  实时视频
export function RealTimeVideo(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreEquipment/RealTimeVideo",
    data,
  });
}
//  告警列表
export function GetWarningList(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreWarning/GetWarningList",
    data,
  });
}
export function GetWarningListSZCJ(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreWarning/GetWarningList/szcj",
    data,
  });
}
//  修改告警状态
export function UpdateStatus(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreWarning/UpdateStatus",
    data,
  });
}
//  告警详情
export function WaringManageInfo(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreWarning/WaringManageInfo",
    data,
  });
}
//  告警信息导出
export function ExportWaringManage(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreWarning/ExportWaringManage",
    data,
  });
}
// v2 安防告警信息导出
export function ExportWaringList(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreWarning/v2/ExportWaringList",
    data,
  });
}
//  关闭告警
export function UpdateWarningStatus(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreWarning/UpdateWarningStatus",
    data,
  });
}
//  海康登录地址
export function HikSsoJumpUrl(data) {
  return request({
    method: "post",
    url: "/DF/SsoJump/HikSsoJumpUrl",
    data,
  });
}
//  危化品单点登录地址
export function HazchemSsoJumpUrl(data) {
  return request({
    method: "post",
    url: "/DF/SsoJump/HazchemSsoJumpUrl",
    data,
  });
}
//  环境单点登录地址
export function EnvironmentSsoJumpUrl(data) {
  return request({
    method: "post",
    url: "/DF/SsoJump/EnvironmentSsoJumpUrl",
    data,
  });
}
//  获取设备树
export function GetEquipmentTree(data) {
  return request({
    method: "post",
    url: "/DF/MonitoreEquipment/GetEquipmentTree",
    data,
  });
}
