<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="164" height="112" viewBox="0 0 164 112">
  <defs>
    <style>
      .cls-1, .cls-4 {
        fill: #fff;
      }

      .cls-2 {
        fill: url(#linear-gradient);
      }

      .cls-3 {
        clip-path: url(#clip-path);
      }

      .cls-4 {
        opacity: 0.16;
      }

      .cls-5 {
        filter: url(#矩形_2724);
      }
    </style>
    <linearGradient id="linear-gradient" y1="0.032" x2="1" y2="0.971" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#4ea1ff"/>
      <stop offset="1" stop-color="#298dff"/>
    </linearGradient>
    <filter id="矩形_2724" x="0" y="0" width="164" height="112" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="4" result="blur"/>
      <feFlood flood-color="#298dff" flood-opacity="0.239"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <clipPath id="clip-path">
      <rect id="矩形_2724-2" data-name="矩形 2724-2" class="cls-1" width="140" height="88" rx="4" transform="translate(583.789 338.221)"/>
    </clipPath>
  </defs>
  <g id="zhijiancishu" transform="translate(-439 -491)">
    <g class="cls-5" transform="matrix(1, 0, 0, 1, 439, 491)">
      <rect id="矩形_2724-2-2" data-name="矩形 2724" class="cls-2" width="140" height="88" rx="4" transform="translate(12 9)"/>
    </g>
    <g id="蒙版组_7" data-name="蒙版组 7" class="cls-3" transform="translate(-132.789 161.779)">
      <rect id="矩形_2738" data-name="矩形 2738" class="cls-4" width="49" height="49" transform="translate(723.5 384.221) rotate(45)"/>
      <rect id="矩形_2739" data-name="矩形 2739" class="cls-4" width="49" height="49" transform="translate(723.5 344.221) rotate(45)"/>
      <rect id="矩形_2740" data-name="矩形 2740" class="cls-4" width="16.971" height="16.971" transform="translate(682.5 396.871) rotate(45)"/>
      <rect id="矩形_2741" data-name="矩形 2741" class="cls-4" width="49" height="49" transform="translate(723.5 364.221) rotate(45)"/>
    </g>
  </g>
</svg>
