<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60" height="60" viewBox="0 0 60 60">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        fill: rgba(41,141,255,0.08);
      }

      .cls-3 {
        clip-path: url(#clip-path);
      }

      .cls-4 {
        fill: #298dff;
      }
    </style>
    <clipPath id="clip-path">
      <rect id="矩形_2555" data-name="矩形 2555" class="cls-1" width="32" height="32"/>
    </clipPath>
  </defs>
  <g id="组_8042" data-name="组 8042" transform="translate(-306 -135)">
    <rect id="矩形_2996" data-name="矩形 2996" class="cls-2" width="60" height="60" rx="20" transform="translate(306 135)"/>
    <g id="file-task-filled" transform="translate(320 149)">
      <g id="file-task-filled-2" data-name="file-task-filled" class="cls-3">
        <path id="路径_4825" data-name="路径 4825" class="cls-4" d="M27,4H24V3a1,1,0,0,0-1-1H9A1,1,0,0,0,8,3V4H5A1,1,0,0,0,4,5V29a1,1,0,0,0,1,1H27a1,1,0,0,0,1-1V5A1,1,0,0,0,27,4ZM10,4H22V6H10ZM23,22H9V20H23Zm0-6H9V14H23Z"/>
      </g>
    </g>
  </g>
</svg>
