<template>
  <div style="height: 100%">
    <vxe-grid
      class="custom-vxe-grid"
      ref="xGrid"
      :loading="loading"
      :size="vxeConfigComputed.size"
      :border="vxeConfigComputed.border"
      :align="vxeConfigComputed.align"
      :show-footer="vxeConfigComputed.showFooter"
      :stripe="vxeConfigComputed.stripe"
      :resizable="false"
      :auto-resize="true"
      height="100%"
      :export-config="{}"
      :row-class-name="rowClassName"
      :cell-style="cellStyle"
      :columns="replaceData"
      :data="tableData"
      @cell-click="cellClickEvent"
    >
      <template #pager v-if="vxeConfigComputed.pageShow">
        <vxe-pager
          background
          :align="pageConfig.right"
          :size="pageConfig.size"
          :layouts="pageConfig.layouts"
          :current-page.sync="pageConfig.currentPage"
          :page-size.sync="pageConfig.pageSize"
          :page-sizes.sync="pageConfig.pageSizes"
          :total="pageConfig.total"
          @page-change="handlePageChange"
        >
        </vxe-pager>
      </template>
    </vxe-grid>
    <!-- :footer-method="footerMethod" -->
    <!-- :footer-cell-class-name="footerCellClassName" -->
    <!-- :footer-span-method="footerColspanMethod" -->
  </div>
</template>

<script>
export default {
  name: "DynamicVxeTable",
  props: {
    vxeConfig: {
      type: Object,
      default: () => {
        return {};
      },
    },
    columnsData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    dataConfig: {
      type: Object,
      default: () => {
        return {};
      },
    },
    pageConfig: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
        replaceData:[],
        loading: this.vxeConfig.loading ? this.vxeConfig.loading : true,
        url: this.vxeConfig.url ? this.vxeConfig.url : '',
    };
  },
  computed: {
    vxeConfigComputed() {
      let vxeConfigTemp = {};
      this.vxeConfig.size===undefined ? vxeConfigTemp.size = 'medium' : vxeConfigTemp.size = this.vxeConfig.size;
      this.vxeConfig.align===undefined ? vxeConfigTemp.align = 'center' : vxeConfigTemp.align = this.vxeConfig.align;
      this.vxeConfig.showFooter===undefined ? vxeConfigTemp.showFooter = true : vxeConfigTemp.showFooter = this.vxeConfig.showFooter;
      this.vxeConfig.border===undefined ? vxeConfigTemp.border = true : vxeConfigTemp.border = this.vxeConfig.border;
      this.vxeConfig.stripe===undefined ? vxeConfigTemp.stripe = true : vxeConfigTemp.stripe = this.vxeConfig.stripe;
      this.vxeConfig.pageShow===undefined ? vxeConfigTemp.pageShow = false : vxeConfigTemp.pageShow = this.vxeConfig.pageShow;
      return vxeConfigTemp;
    },
    pageConfig() {
      return Object.assign({
        align: 'right',
        size: 'small',
        layouts: [
          'Sizes',
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'FullJump',
          'Total',
        ]
      }, this.pageConfig);
    }
  },
  watch: {
    columnsData: {
      handler(newVal, oldVal) {
        this.columnsData = newVal;
      },
      immediate: true,
      deep: true
    },
    tableData: {
      handler(newVal, oldVal) {
        this.tableData = newVal;
        if(this.tableData.length==0) return;
        this.roloadTableData();
      },
      deep: true
    }
  },
  mounted() {
    this.replaceName(this.columnsData);
    this.roloadTableData();
  },
  methods: {
    roloadTableData() {
      this.$refs.xGrid.reloadData(this.tableData).then(() => {
        this.handleMerge();
        this.loading = false;
      });
    },
    // 动态改变表头字段key
    replaceName(data) {
      data.map((item) => {
        item["title"] = item["Display_Name"];
        item["field"] = item["Code"];
        item["children"] = item["Children"];
        item["align"] = item["Align"];
        delete item["Display_Name"];
        delete item["Code"];
        delete item["Children"];
        delete item["Align"];
        if (item.children) {
          this.replaceName(item.children);
        }
        this.replaceData = data;
        return data;
      });
    },
    // 改变分页
    handlePageChange({ currentPage, pageSize }) {
      this.pageConfig.currentPage = currentPage;
      this.pageConfig.pageSize = pageSize;
      let pageInfo = {Page: currentPage, pageSize: pageSize}
      this.$emit("changePageFn", pageInfo)
    },

    // 行样式
    rowClassName({ row, rowIndex }) {
      if (
        this.dataConfig.rowBgColor &&
        JSON.stringify(this.dataConfig.rowBgColor) != "{}"
      ) {
        let rowBgColor = this.dataConfig.rowBgColor;
        for (var key in rowBgColor) {
          if (rowBgColor[key].includes(rowIndex)) {
            return `row-${key}`;
          }
        }
      }
      if (this.url && this.url != "" && row.Click_Param!='' ) {
        return "row-pointer";
      }
    },

    // 列样式
    cellStyle({ row, column, rowIndex, _columnIndex }) {
      let cellStyleLeft = { textAlign: "left" };
      if (
        this.dataConfig.rowAlignLeft &&
        this.dataConfig.rowAlignLeft.includes(rowIndex)
      ) {
        return cellStyleLeft;
      }
    },

    // 单元格点击 用于跳转
    cellClickEvent({ row, column, _rowIndex }) {
      console.log(`单元格点击${column.title}-${_rowIndex}-${row.moduleName}`);
      if (this.url && this.url != "" && row.Click_Param!='') {
        window.open(`${this.url}?Click_Param=${row.Click_Param}`);
      }
    },

    // 单元格合并
    handleMerge() {
      const $xGrid = this.$refs.xGrid;
      const { fullData, visibleData } = $xGrid.getTableData();
      const { fullColumn } = $xGrid.getTableColumn();
      let fullColumnFilter = fullColumn.filter((item) => {
        return item.visible == true;
      });
      if(fullData.length==0) return;
      let mergeCells = [];
      // 列数据相同 自动合并
      const mergeColFields = this.dataConfig.mergeCol ? this.dataConfig.mergeCol : []; // 列field
      if (mergeColFields && mergeColFields.length > 0) {
        fullData.forEach((row, _rowIndex) => {
          fullColumnFilter.forEach((column, _colIndex) => {
            const cellValue = row[column.property];
            if (cellValue && mergeColFields.includes(column.property)) {
              const prevRow = visibleData[_rowIndex - 1];
              let nextRow = visibleData[_rowIndex + 1];
              if (prevRow && prevRow[column.property] === cellValue) {
                mergeCells.push({
                  row: _rowIndex,
                  col: _colIndex,
                  rowspan: 0,
                  colspan: 0,
                });
              } else {
                let countRowspan = 1;
                while (nextRow && nextRow[column.property] === cellValue) {
                  nextRow = visibleData[++countRowspan + _rowIndex];
                }
                if (countRowspan > 1) {
                  mergeCells.push({
                    row: _rowIndex,
                    col: _colIndex,
                    rowspan: countRowspan,
                    colspan: 1,
                  });
                }
              }
            }
          });
        });
      }
      // 根据行号合并整行
      const mergeRowFields = this.dataConfig.mergeRow ? this.dataConfig.mergeRow : [];
      if (mergeRowFields && mergeRowFields.length > 0) {
        fullData.forEach((row, _rowIndex) => {
          if (mergeRowFields.includes(_rowIndex)) {
            mergeCells.push({
              row: _rowIndex,
              col: 0,
              rowspan: 1,
              colspan: fullColumn.length,
            });
          }
        });
      }
      // 组合其他需要合并的行列数据
      mergeCells = mergeCells.concat(
        this.dataConfig.rowMerge ? this.dataConfig.rowMerge : [],
        this.dataConfig.colMerge ? this.dataConfig.colMerge : []
      );
      $xGrid.setMergeCells(mergeCells).then(() => {
        console.log("合并完毕");
      });
    },
  },
};
</script>

<style lang="scss">
.custom-vxe-grid {
  .vxe-table--render-default.border--full .vxe-table--header-wrapper {
    background-color: #f5f6f8;
  }
  .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
    padding: 8px 0;
  }
  .vxe-cell--title {
    color: rgba(34, 40, 52, 0.65);
  }
  .vxe-table--body-wrapper {
    height: 180px;
  }

  .vxe-body--row.row-2db7f5 {
    background-color: #2db7f5;
    color: #fff;
  }
  .vxe-body--row.row-187187 {
    background-color: #187;
    color: #fff;
  }
  .vxe-body--row.row-ce951a {
    background-color: #ce951a;
    color: #fff;
  }

  .row-ce951a {
    background-color: #ce951a;
    color: #fff;
  }

  .vxe-body--row.row-pointer {
    cursor: pointer;
  }

  /*滚动条整体部分*/
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  /*滚动条的轨道*/
  ::-webkit-scrollbar-track {
    background-color: #ffffff;
  }
  /*滚动条里面的小方块，能向上向下移动*/
  ::-webkit-scrollbar-thumb {
    background-color: #bfbfbf;
    border-radius: 5px;
    border: 1px solid #f1f1f1;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8;
  }
  ::-webkit-scrollbar-thumb:active {
    background-color: #787878;
  }
  /*边角，即两个滚动条的交汇处*/
  ::-webkit-scrollbar-corner {
    background-color: #ffffff;
  }
}
</style>
