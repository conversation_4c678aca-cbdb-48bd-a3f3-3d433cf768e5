<template>
  <div class="calendar-set gantt-calendar">
    <el-row :gutter="20">
      <el-col :span="10">
        <div class="custom-calendar-wrapper">
          <div class="month-year-controls">
            <div class="backward">
              <i
                title="上一年"
                class="el-icon-d-arrow-left"
                @click="prevYear"
              />
              <i title="上个月" class="el-icon-arrow-left" @click="prevMonth" />
            </div>
            <div class="forward">
              <i
                title="下个月"
                class="el-icon-arrow-right"
                @click="nextMonth"
              />
              <i
                title="下一年"
                class="el-icon-d-arrow-right"
                @click="nextYear"
              />
            </div>
          </div>
          <el-calendar ref="myCalendar" class="custom-calendar">
            <!-- 这里使用的是 2.5 slot 语法，对于新项目请使用 2.6 slot 语法-->
            <template slot="dateCell" slot-scope="{ date, data }">
              <div
                :class="`c-date ${exceptionType(data.day)}`"
                :date="data.day"
              >
                <div v-if="exceptionType(data.day) !== 'normal'" class="ctag">
                  {{
                    exceptionType(data.day) === 'workday'
                      ? '班'
                      : exceptionType(data.day) === 'holiday'
                        ? '休'
                        : ''
                  }}
                </div>
                {{ date.getDate() }}
              </div>
            </template>
          </el-calendar>
        </div>

        <p style="text-align:center;color:#CCC;">日历预览</p>
      </el-col>
      <el-col :span="14">
        <div class="weekend-set">
          <h3>工作时间</h3>
          <el-checkbox-group v-model="workWeek" @change="workWeekChange">
            <template v-for="w in weekdays">
              <el-checkbox
                :key="w.value"
                :disabled="!editMode"
                :label="w.value"
              >{{ w.label }}</el-checkbox>
            </template>
          </el-checkbox-group>
        </div>
        <div class="exception-set">
          <div class="header">
            <strong>例外名称</strong>
            <strong>例外日期</strong>
            <strong>设为</strong>
          </div>
          <div class="scroll">
            <div v-for="(ex, i) in exceptions" :key="i" class="s-row">
              <div>
                <el-input
                  v-if="editMode"
                  v-model="ex.name"
                  size="mini"
                  type="text"
                  style="width:90%"
                  placeholder="例外名称"
                  @change="exceptionChange(ex, 'name', $event)"
                />
                <span v-else>{{ ex.name }}</span>
              </div>
              <div>
                <el-date-picker
                  v-if="editMode"
                  v-model="ex.date"
                  type="daterange"
                  size="mini"
                  placeholder="例外时间"
                  style="width:90%"
                  value-format="yyyy-MM-dd"
                  @change="exceptionChange(ex, 'date', $event)"
                />
                <span v-else>
                  {{ ex.date.join(' ~ ') }}
                </span>
              </div>
              <div>
                <el-switch
                  v-model="ex.isWorkday"
                  :disabled="!editMode"
                  style="display: block"
                  :width="42"
                  active-color="#ff4949"
                  inactive-color="#13ce66"
                  active-text="班"
                  inactive-text="休"
                  @change="exceptionChange(ex, 'isWorkday', $event)"
                />
                <el-link
                  v-if="editMode"
                  :underline="false"
                  icon="el-icon-close"
                  @click="removeException(i)"
                />
              </div>
            </div>
          </div>
          <div>
            <el-link
              v-if="editMode"
              :underline="false"
              icon="el-icon-plus"
              type="primary"
              @click="addException"
            >新增例外</el-link>
          </div>
        </div>
      </el-col>
    </el-row>
    <div style="text-align:center;">
      <el-button @click="$emit('dialogCancel')">取消</el-button>
      <el-button type="primary" @click="saveCalendar">确定</el-button>
    </div>
  </div>
</template>
<script>
import * as moment from 'moment'
export default {
  name: 'CalendarSet',
  components: {},
  props: {
    origin: {
      type: Object,
      default: () => ({
        week_calendar: [1, 1, 1, 1, 1, 1, 1],
        out_calendar: [] // 例外对象数组 {name:'', start_date:'', end_date:'', type:1} type 1上班，0 休息
      })
    },
    editMode: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      weekdays: [
        { value: 1, label: '周一' },
        { value: 2, label: '周二' },
        { value: 3, label: '周三' },
        { value: 4, label: '周四' },
        { value: 5, label: '周五' },
        { value: 6, label: '周六' },
        { value: 0, label: '周日' }
      ],
      workWeek: [], // 每周工作日
      calendarRules: {
        weekend: [], // 周末
        holiday: [], // 周末之外的假期
        workday: [] // 周末规则之外的工作日
      },
      exceptions: [] // 例外日期
    }
  },
  watch: {
    exceptions(nv) {
      this.resetCalendarRules()
    }
  },
  created() {
    this.calcOrignRules()
    this.workWeek = this.weekdays
      .filter(d => this.calendarRules.weekend.indexOf(d.value) < 0)
      .map(d => d.value)
  },
  methods: {
    calcOrignRules() {
      // 从 orign 计算周末
      this.origin.week_calendar.forEach((w, i) => {
        if (Number(w) === 0) {
          this.calendarRules.weekend.push(i)
        }
      })
      // 从 orign 计算例外
      this.origin.out_calendar.forEach(o => {
        this.exceptions.push({
          name: o.name,
          date: [o.start_date, o.end_date],
          isWorkday: o.type == 1
        })
      })
    },
    exceptionType(dateStr) {
      let type = 'normal'
      if (this.calendarRules.workday.indexOf(dateStr) > -1) {
        type = 'workday'
      } else if (this.calendarRules.holiday.indexOf(dateStr) > -1) {
        type = 'holiday'
      } else {
        type =
          this.calendarRules.weekend.indexOf(new Date(dateStr).getDay()) > -1
            ? 'holiday'
            : 'normal'
      }
      // console.log(date.getDay())
      return type
    },
    exceptionChange(exception, k, v) {
      exception[k] = v
      this.resetCalendarRules()
    },
    resetCalendarRules() {
      this.calendarRules.weekend = this.weekdays
        .filter(d => this.workWeek.indexOf(d.value) < 0)
        .map(d => d.value)
      // 先清空例外日期
      this.calendarRules.workday = []
      this.calendarRules.holiday = []
      // 根据例外数组重新计算例外日期
      this.exceptions.forEach(ex => {
        if (ex.date) {
          const dates = []
          if (!ex.date[0] || !ex.date[1]) {
            dates.push(ex.date[0] ?? ex.date[1])
          } else {
            const deltaDays = moment(ex.date[1]).diff(ex.date[0], 'days')
            for (let i = 0; i <= deltaDays; i++) {
              const date = moment(ex.date[0])
                .add(i, 'days')
                .format('YYYY-MM-DD')
              dates.push(date)
            }
          }
          if (ex.isWorkday) {
            this.calendarRules.workday = this.calendarRules.workday.concat(
              dates
            )
          } else {
            this.calendarRules.holiday = this.calendarRules.holiday.concat(
              dates
            )
          }
        }
      })
      console.log(this.calendarRules)
    },
    prevYear() {
      const date = new Date(
        this.$refs.myCalendar.realSelectedDay ||
          this.$refs.myCalendar.formatedToday
      )
      const m = date.getMonth() + 1
      const y = date.getFullYear()
      console.log(`${y - 1}-${m < 10 ? '0' + m : m}-01`)
      this.$refs.myCalendar.pickDay(`${y - 1}-${m < 10 ? '0' + m : m}-01`)
    },
    prevMonth() {
      this.$refs.myCalendar.selectDate('prev-month')
    },
    nextYear() {
      const date = new Date(
        this.$refs.myCalendar.realSelectedDay ||
          this.$refs.myCalendar.formatedToday
      )
      const m = date.getMonth() + 1
      const y = date.getFullYear()
      this.$refs.myCalendar.pickDay(`${y + 1}-${m < 10 ? '0' + m : m}-01`)
    },
    nextMonth() {
      this.$refs.myCalendar.selectDate('next-month')
    },
    workWeekChange(val) {
      this.calendarRules.weekend = this.weekdays
        .filter(d => this.workWeek.indexOf(d.value) < 0)
        .map(d => d.value)
    },
    addException() {
      this.exceptions.push({
        name: '例外',
        date: '',
        isWorkday: true
      })
    },
    removeException(index) {
      this.exceptions.splice(index, 1)
      // console.log(this.exceptions)
    },
    saveCalendar() {
      // console.log(this.workWeek, this.exceptions)
      if (!this.editMode) {
        return this.$emit('dialogCancel')
      }
      const data = {}
      data.week_calendar = [0, 0, 0, 0, 0, 0, 0]
      this.workWeek.forEach(d => {
        data.week_calendar[d] = 1
      })
      data.out_calendar = []
      this.exceptions.forEach(ex => {
        data.out_calendar.push({
          name: ex.name,
          type: ex.isWorkday ? 1 : 0,
          start_date: ex.date[0],
          end_date: ex.date[1]
        })
      })
      // console.log(data, this.calendarRules)
      this.$emit('dialogFormSubmitSuccess', { type: 'setCalendar', data })
    }
  }
}
</script>
<style lang="scss" scoped>
.calendar-set {
  margin-top: -16px;
  .custom-calendar-wrapper > .custom-calendar {
    border: 1px solid #ddd;
  }
  .weekend-set {
    h3 {
      padding: 6px 0;
      margin: 0;
      font-weight: normal;
    }
    label {
      margin: 8px 12px 8px 0;
      font-weight: bold;
    }
  }
  .exception-set {
    margin-top: 16px;
    .scroll {
      margin: 12px 0;
      height: 252px;
      overflow-y: scroll;
    }
    .header,
    .s-row {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      margin-bottom: 12px;
      & > * {
        width: 100%;
      }
      & > *:first-child {
        width: 120px;
        flex-shrink: 0;
      }
      & > *:last-child {
        width: 80px;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
        > .el-link {
          position: absolute;
          right: 12px;
          top: 6px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.gantt-calendar {
  .s-row {
    .el-date-editor {
      .el-tooltip.item.el-input__inner {
        padding-left: 28px;
      }
    }
  }
  .custom-calendar-wrapper {
    position: relative;
    > .month-year-controls {
      position: absolute;
      z-index: 1;
      display: flex;
      justify-content: space-between;
      width: 100%;
      .backward,
      .forward {
        margin: 12px;
        > i {
          cursor: pointer;
          margin: 0 4px;
        }
        > i:hover {
          color: #298dff;
        }
      }
    }
    > .custom-calendar {
      .el-calendar__header {
        border-bottom: none;
        position: relative;
        justify-content: space-around;
      }
      .el-calendar__button-group {
        display: none;
      }
      .el-calendar__body {
        padding-bottom: 20px;
      }
      .el-calendar-table {
        td {
          border: none;
        }
        th {
          border-bottom: 1px solid #ddd;
        }
        .el-calendar-day {
          height: initial;
          padding: 0;
          border: 2px solid #fff;
          .c-date {
            padding: 14px;
            position: relative;
            text-align: center;
            white-space: nowrap;
            font-size: 13px;
            .ctag {
              position: absolute;
              right: 2px;
              top: 2px;
              transform: scale(0.75);
            }
            &.holiday {
              color: #3ecc93;
              background: #3ecc9311;
            }
            &.workday {
              background: #fb6b7f11;
              color: #fb6b7f;
            }
          }
        }
      }
    }
  }
  .el-switch {
    width: 40px;
    .el-switch__label.el-switch__label--left,
    .el-switch__label.el-switch__label--right {
      visibility: hidden;
      position: absolute;
      color: #fff;
    }
    .el-switch__label.el-switch__label--left.is-active {
      left: 21px;
      z-index: 10;
      top: 2px;
      color: #fff;
      font-size: 11px;
      visibility: initial;
    }
    .el-switch__label.el-switch__label--right.is-active {
      left: -4px;
      z-index: 10;
      top: 3px;
      color: #fff;
      font-size: 11px;
      visibility: initial;
    }
  }
}
</style>
