import * as moment from 'moment'
import * as BGT from './index'
export function calc(gantt, plan, tasks, links) {
  return new Promise((resolve, reject) => {
    var projectstartdate = plan.Plan_Start_Date
    var projectenddate = plan.Plan_End_Date

    /**
     * 最小开始日期
     */
    var startdatees = null
    /**
     * 最晚结束日期
     */
    var enddatels = null

    // 初始化部分数据,以免传入数据缺失
    for (var i = 0; i < tasks.length; i++) {
      if (startdatees == null) {
        startdatees = tasks[i].Plan_Start_Date
      } else if (startdatees > tasks[i].Plan_Start_Date) {
        startdatees = tasks[i].Plan_Start_Date
      }

      if (enddatels == null) {
        enddatels = tasks[i].Plan_End_Date
      } else if (enddatels < tasks[i].Plan_End_Date) {
        enddatels = tasks[i].Plan_End_Date
      }

      tasks[i]['iscriticalPath'] = 0
      tasks[i]['isCalcforward'] = false
      tasks[i]['isCalcbackward'] = false
    }

    if (projectstartdate == null && startdatees != null) {
      projectstartdate = startdatees
    }

    if (projectenddate == null && enddatels != null) {
      projectenddate = enddatels
    }

    /**
     * 状态日期
     */
    var statusDate = projectstartdate

    if (plan.Cur_Data_Date) {
      statusDate = plan.Cur_Data_Date
    }

    if (statusDate == null || statusDate == '') {
      return reject('状态日期必须不为空!')
    }
    // 需要检验当前的状态日期,是否在所有任务实际开始日期之后,如果是继续计算,否则需要返回
    var ret = tasks.filter(
      item =>
        item.type != 'project' &&
        item.Actual_Start_Date != '' &&
        item.Actual_Start_Date != null &&
        item.Actual_Start_Date > statusDate
    )
    if (ret != null && ret.length > 0) {
      // return reject('在状态日期之后有任务已经开工,不可以选择该状态日期!')
    }
    ret = tasks.filter(
      item =>
        item.type != 'project' &&
        item.Actual_End_Date != '' &&
        item.Actual_End_Date != null &&
        item.Actual_End_Date > statusDate
    )
    if (ret != null && ret.length > 0) {
      // return reject('在状态日期之后有任务已经完工,不可以选择该状态日期!')
    }
    // 需要循环所有links,确保没有任务可以组成圆形,如果有的话,则该任务存在问题,返回错误信息,否则则可以继续进行计算
    var haswbserror = checkwbslinks(tasks, links)
    if (haswbserror.status == 'error') {
      return reject(haswbserror.message)
    }
    var haserror = checklinks(tasks, links)
    if (haserror.status == 'error') {
      return reject(haserror.message)
    }
    // 开始计算
    calcTask(
      tasks,
      links,
      statusDate,
      projectstartdate,
      projectenddate,
      plan,
      gantt
    )

    // 计算结果回填 plan
    plan.Plan_Start_Date = tasks
      .map(t => t.Plan_Start_Date)
      .sort((a, b) => {
        return a <= b ? -1 : 1
      })
      .shift()
    plan.Plan_End_Date = tasks
      .map(t => t.Plan_End_Date)
      .sort((a, b) => {
        return a <= b ? -1 : 1
      })
      .pop()
    plan.Plan_Duration =
      Math.abs(
        gantt.calculateDuration({
          start_date: plan.Plan_Start_Date,
          end_date: plan.Plan_End_Date
        })
      ) + 1
    plan.Actual_Start_Date =
      tasks
        .filter(t => Boolean(t.Actual_Start_Date))
        .map(t => t.Actual_Start_Date)
        .sort((a, b) => {
          return a <= b ? -1 : 1
        })
        .shift() || null
    const act_end_dates = tasks
      .filter(t => Boolean(t.Actual_End_Date))
      .map(t => t.Actual_End_Date)
    plan.Actual_End_Date =
      act_end_dates
        .sort((a, b) => {
          return a <= b ? -1 : 1
        })
        .pop() || null
    const hasUnfinished = act_end_dates.length !== tasks.length

    if (hasUnfinished) {
      plan.Actual_End_Date = null
    }
    if (plan.Actual_Start_Date) {
      const a_end = plan.Actual_End_Date || plan.Cur_Data_Date
      plan.Actual_Duration =
        Math.abs(
          gantt.calculateDuration({
            start_date: plan.Actual_Start_Date,
            end_date: a_end
          })
        ) + 1
    } else {
      plan.Actual_Duration = 0
    }
    plan.Dynamic_Start_Date = plan.Actual_Start_Date
      ? moment(plan.Actual_Start_Date).format('YYYY-MM-DD[A]')
      : moment(plan.Plan_Start_Date).format('YYYY-MM-DD')
    plan.Dynamic_End_Date = plan.Actual_End_Date
      ? moment(plan.Actual_End_Date).format('YYYY-MM-DD[A]')
      : moment(plan.Plan_End_Date).format('YYYY-MM-DD')
    plan.Dynamic_Duration =
      Math.abs(
        gantt.calculateDuration({
          start_date: plan.Actual_Start_Date || plan.Plan_Start_Date,
          end_date: plan.Actual_End_Date || plan.Plan_End_Date
        })
      ) + 1
    // 总体计划进度
    let n_days = 0
    let task_days = 0
    tasks.forEach(t => {
      if (t.type === 'task') {
        task_days += t.Plan_Duration
        if (t.Actual_Start_Date) {
          n_days += t.Plan_Duration * t.Actual_Progress
        }
      }
    })
    // tasks.forEach(t => {
    //   if (t.type === 'task') {
    //     task_days += t.Plan_Duration
    //     n_days += t.Needed_Duration || 0
    //   }
    // })
    plan.Percent_Complete =
      task_days === 0 ? 0 : (n_days / task_days).toFixed(4)
    // console.log(n_days, task_days, plan.Percent_Complete)
    /**
     * resolve 数据
     */
    resolve(true)
  })
}

// 循环所有关联关系,判断是否有wbs任务参与其中
function checkwbslinks(datas, links) {
  for (var i = 0; i < links.length; i++) {
    var sourceid = links[i].source
    var sourcetask = datas.filter(item => item.id == sourceid)[0]
    var targetid = links[i].target
    var targettask = datas.filter(item => item.id == targetid)[0]
    try {
      if (sourcetask != null && targettask != null) {
        if (sourcetask.type == 'project' || targettask.type == 'project') {
          var message =
            '名称为' + sourcetask.text + '的wbs节点参与关联关系!请检查'
          return { status: 'error', message: message }
        }
      } else {
        return {
          status: 'error',
          message: '关联关系数据异常,请检查或者联系开发人员'
        }
      }
    } catch (e) {
      console.log('\r\n', e, '\r\n', e.stack)
    }
  }
  return { status: 'success', message: '' }
}

// 循环所有关联关系,判断是否有圆形关系
function checklinks(datas, links) {
  for (var i = 0; i < links.length; i++) {
    // var searchlinkids = links[i].source;
    var searchlinkids = new Array()
    searchlinkids.push(links[i].source)
    var retjson = checklinkserror(datas, links, searchlinkids, links[i])
    if (retjson.status == 'error') {
      return retjson
    } else {
      continue
    }
  }
  return { status: 'success', message: '' }
}

// 根据传入节点参数判断该回路是否有圆形回路
function checklinkserror(datas, links, searchlinkids, sourcelink) {
  var sourceid = sourcelink.source
  var targetid = sourcelink.target

  if (searchlinkids.indexOf(targetid) < 0) {
    var nextlinks = links.filter(item => item.source == targetid)
    if (nextlinks != null && nextlinks.length > 0) {
      searchlinkids.push(targetid)
      for (var i = 0; i < nextlinks.length; i++) {
        var retjson = checklinkserror(datas, links, searchlinkids, nextlinks[i])
        if (retjson.status == 'error') {
          return retjson
        } else {
          continue
        }
      }
      searchlinkids.splice(1, targetid)
    }
  } else {
    // 返回错误信息
    var targettask = datas.filter(item => item.id == targetid)[0]
    var message =
      '名称为' +
      targettask.text +
      '的任务存在错误的关联关系,造成圆形回路,请检查'
    var error = { status: 'error', message: message }
    return error
  }
  return { status: 'success', message: '' }
}

// 重新计算所有节点
function calcTask(
  tasks,
  links,
  calcDate,
  projectStartdate,
  projectenddate,
  plan,
  gantt
) {
  // var projectenddate = dateFtt("yyyy-MM-dd", clcEndDate(new Date(projectStartdate), projectduration));

  // 判断计划任务中是否有规划的任务比项目开始日期早的,以及实际已经开工的任务中有比项目开始日期早的,取最小日期
  // var taskslist = tasks.filter(item => item.type == "task");
  // for (var i = 0; i < taskslist.length; i++) {
  //    if (projectStartdate > taskslist[i].start_date_plan) {
  //        projectStartdate = taskslist[i].start_date_plan;
  //    }
  //    if (projectStartdate > taskslist[i].start_date_act) {
  //        projectStartdate = taskslist[i].start_date_act;
  //    }
  // }

  // 前推法
  var tasksdatalist = tasks.filter(
    item => item.type == 'task' || item.type == 'milestone'
  )

  for (var i = 0; i < tasksdatalist.length; i++) {
    calcTaskforward(
      tasks,
      links,
      calcDate,
      projectStartdate,
      tasksdatalist[i],
      gantt
    )
  }

  // 前推后能得到一个最新的最早完成时间,需要判断该最新的最早完成时间是否会大于项目规定的最晚结束日期
  for (var i = 0; i < tasksdatalist.length; i++) {
    if (projectenddate < tasksdatalist[i].Plan_End_Date) {
      projectenddate = tasksdatalist[i].Plan_End_Date
    }
    if (projectenddate < tasksdatalist[i].Actual_End_Date) {
      projectenddate = tasksdatalist[i].Actual_End_Date
    }
  }

  // 逆推法
  for (var i = 0; i < tasksdatalist.length; i++) {
    calcTaskbackward(
      tasks,
      links,
      calcDate,
      projectenddate,
      tasksdatalist[i],
      gantt
    )
  }
  // 前推,逆推后将再一次进行循环,计算出相应的自由时差和总时差
  // 循环所有任务,先计算本任务的总时差=最迟开始时间-最早开始时间=最迟结束时间-最早结束时间
  // 找出所有总时差为零或为负的活动，就是关键活动
  // 再循环寻找该任务的所有紧后任务,并找到紧后任务的最早开始时间最小值,计算自由时差 = 所有紧后工作中最早开始时间最小值－ 最早结束时间
  var haszeropath = false
  for (var i = 0; i < tasksdatalist.length; i++) {
    if (tasksdatalist[i].Actual_End_Date) {
      tasksdatalist[i].Free_Float = null
      tasksdatalist[i].Total_Float = null
    } else {
      tasksdatalist[i].Total_Float =
        (new Date(tasksdatalist[i].Laster_Start_Date) -
          new Date(tasksdatalist[i].Early_Start_Date)) /
        (1 * 24 * 60 * 60 * 1000)
      if (tasksdatalist[i].Total_Float <= 0) {
        haszeropath = true
      }
      if (tasksdatalist[i].Total_Float <= 0) {
        // 就是关键路径
        tasksdatalist[i]['iscriticalPath'] = 1
      }
      var targetLinks = links.filter(item => item.source == tasksdatalist[i].id)
      tasksdatalist[i].Free_Float = null
      if (targetLinks && targetLinks.length > 0) {
        var mintargetstartdate = null
        for (var j = 0; j < targetLinks.length; j++) {
          var targettasktemp = tasks.filter(
            item => item.id == targetLinks[j].target
          )[0]
          var freeFloatTmp = 0
          // 根据逻辑条件计算浮时,并取最小值
          if (targetLinks[j].type == '0') {
            // FS
            freeFloatTmp =
              (new Date(targettasktemp.Early_Start_Date) -
                new Date(tasksdatalist[i].Early_End_Date)) /
                (1 * 24 * 60 * 60 * 1000) -
              parseInt(targetLinks[j].lag || '0') -
              1
          } else if (targetLinks[j].type == '1') {
            // SS
            freeFloatTmp =
              (new Date(targettasktemp.Early_Start_Date) -
                new Date(tasksdatalist[i].Early_Start_Date)) /
                (1 * 24 * 60 * 60 * 1000) -
              parseInt(targetLinks[j].lag || '0')
          } else if (targetLinks[j].type == '2') {
            // FF
            freeFloatTmp =
              (new Date(targettasktemp.Early_End_Date) -
                new Date(tasksdatalist[i].Early_End_Date)) /
                (1 * 24 * 60 * 60 * 1000) -
              parseInt(targetLinks[j].lag || '0')
          } else if (targetLinks[j].type == '3') {
            // SF
            freeFloatTmp =
              (new Date(targettasktemp.Early_End_Date) -
                new Date(tasksdatalist[i].Early_Start_Date)) /
                (1 * 24 * 60 * 60 * 1000) -
              parseInt(targetLinks[j].lag || '0') +
              1
          } else {
            // FS
            freeFloatTmp =
              (new Date(targettasktemp.Early_Start_Date) -
                new Date(tasksdatalist[i].Early_End_Date)) /
                (1 * 24 * 60 * 60 * 1000) -
              parseInt(targetLinks[j].lag || '0') -
              1
          }
          if (
            tasksdatalist[i].Free_Float == null ||
            freeFloatTmp < tasksdatalist[i].Free_Float
          ) {
            tasksdatalist[i].Free_Float = freeFloatTmp
          }
          if (tasksdatalist[i].Free_Float < 0) tasksdatalist[i].Free_Float = 0
          // if (mintargetstartdate == null || mintargetstartdate > targettasktemp.earliestStartDate) {
          //    mintargetstartdate = targettasktemp.earliestStartDate;
          // }
        }
        // tasksdatalist[i].freeFloat = (new Date(mintargetstartdate) - new Date(tasksdatalist[i].earliestFinishDate)) / (1 * 24 * 60 * 60 * 1000) -1;
      } else {
        tasksdatalist[i].Free_Float =
          (new Date(projectenddate) -
            new Date(tasksdatalist[i].Early_End_Date)) /
          (1 * 24 * 60 * 60 * 1000)
        if (tasksdatalist[i].Free_Float < 0) tasksdatalist[i].Free_Float = 0
      }
    }
  }
  // 如果活动里没有等于0的路径,可以考虑取总时差最小的活动列表为关键路径
  console.log(haszeropath)
  if (!haszeropath) {
    var filterlist = tasks.filter(item => item.Total_Float >= 0)
    var minvalue = filterlist.reduce((prev, cur) => {
      console.log(prev, cur)
      return prev && prev.Total_Float < cur.Total_Float
        ? prev.Total_Float
        : cur.Total_Float
    })
    console.log(minvalue)
    var templist = tasks.filter(item => item.Total_Float == minvalue)
    for (var j = 0; j < templist.length; j++) {
      templist[j]['iscriticalPath'] = 1
    }
  }

  // 循环计算wbs的任务进度,所有父节点的实际任务进度,还需要计算其显示进度
  var ret = tasks.filter(item => item.type == 'project')
  if (ret != null) {
    for (var i = 0; i < ret.length; i++) {
      let earliestTask, latestTask
      // 搜索所有节点的父节点是该任务节点的
      var childtasks = sumTasksByParent(ret[i], tasks)
      var sumdays = 0
      var curdays = 0
      var sumdaysshow = 0
      var curdaysshow = 0
      var minstartdate = null
      var maxenddate = null
      var minstartdateact = null
      let actstart = null
      let actend = null
      // 循环判断
      for (var j = 0; j < childtasks.length; j++) {
        sumdaysshow += parseInt(childtasks[j].duration)
        sumdays += parseInt(childtasks[j].Plan_Duration)
        curdays +=
          childtasks[j].Actual_Progress * parseInt(childtasks[j].Plan_Duration)
        if (minstartdate == null || minstartdate > childtasks[j].start_date) {
          minstartdate = childtasks[j].start_date
          earliestTask = childtasks[j]
        }
        if (
          maxenddate == null ||
          maxenddate <
            moment(childtasks[j].Dynamic_End_Date.split('A')[0]).toDate()
        ) {
          // 里程碑不参与计算wbs
          // if (childtasks[j].type !== 'milestone') {
          //   maxenddate = moment(
          //     childtasks[j].Dynamic_End_Date.split('A')[0]
          //   ).toDate()
          //   latestTask = childtasks[j]
          // }

          // 里程碑参与计算wbs
          maxenddate = moment(
            childtasks[j].Dynamic_End_Date.split('A')[0]
          ).toDate()
          latestTask = childtasks[j]
        }
        if (childtasks[j].Actual_Start_Date != null) {
          if (
            minstartdateact == null ||
            minstartdateact > childtasks[j].Actual_Start_Date
          ) {
            minstartdateact = childtasks[j].Actual_Start_Date
          }
        }
        // 实际开始/结束
        if (childtasks[j].Actual_Start_Date) {
          if (!actstart || childtasks[j].Actual_Start_Date < actstart) {
            actstart = childtasks[j].Actual_Start_Date
          }
        }
        if (childtasks[j].Actual_End_Date) {
          // 所有子任务都完成时才有实际完成
          if (
            (!actend || childtasks[j].Actual_End_Date > actend) &&
            !childtasks.find(tk => Boolean(tk.Actual_End_Date))
          ) {
            actend = childtasks[j].Actual_End_Date
          }
        }
      }
      console.log(earliestTask, latestTask)
      if (minstartdate != null && maxenddate != null) {
        console.log(ret[i].text, minstartdate)
        ret[i].Plan_Start_Date = minstartdate
        ret[i].start_date = minstartdate
        ret[i].Actual_Start_Date = minstartdateact
        ret[i].Dynamic_Start_Date = earliestTask.Actual_Start_Date
          ? moment(minstartdate).format('YYYY-MM-DD[A]')
          : moment(minstartdate).format('YYYY-MM-DD')
        // 如果子任务是里程碑，把里程碑延长时间横道线的日期减少一天
        if (
          earliestTask.type === 'milestone' &&
          earliestTask.parent == ret[i].Code
        ) {
          let nd = gantt.calculateEndDate({
            start_date: earliestTask.start_date,
            duration: -1
          })
          ret[i].Dynamic_Start_Date = moment(nd).format('YYYY-MM-DD[A]')
        }
        ret[i].Dynamic_End_Date = latestTask.Actual_End_Date
          ? moment(maxenddate).format('YYYY-MM-DD[A]')
          : moment(maxenddate).format('YYYY-MM-DD')

        console.log(ret[i].Dynamic_Start_Date)
        ret[i].Plan_End_Date = maxenddate
        ret[i].Dynamic_Duration =
          Math.abs(
            gantt.calculateDuration({
              start_date: moment(
                ret[i].Dynamic_Start_Date.split('A')[0]
              ).toDate(),
              end_date: moment(ret[i].Dynamic_End_Date.split('A')[0]).toDate()
            })
          ) + 1
        // ret[i].duration = (new Date(maxstartdate) - new Date(minstartdate)) / (1 * 24 * 60 * 60 * 1000) + 1;//计划完成工期
        ret[i].duration =
          Math.abs(
            gantt.calculateDuration({
              start_date: new Date(maxenddate),
              end_date: new Date(minstartdate)
            })
          ) + 1 // 显示完成工期
        // 里程碑显示在日期开头，所以wbs 横道延长一天
        console.log(latestTask.type, maxenddate)
        if (latestTask.type === 'milestone') {
          ret[i].Dynamic_Duration -= 1

          latestTask.start_date = latestTask.end_date = ret[
            i
          ].end_date = moment(maxenddate)
            .add(1, 'days')
            .startOf('date')
            .toDate()
        }
        console.log(ret[i].text, ret[i].type, ret[i].duration, ret[i].end_date)
      }
      if (sumdays > 0) {
        if (calcDate >= minstartdate) {
          ret[i].progress = (curdays / sumdays).toFixed(4)
          // ret[i].progress = (
          //   ((new Date(calcDate) - new Date(ret[i].start_date)) /
          //     (1 * 24 * 60 * 60 * 1000) +
          //     1) /
          //   ((new Date(ret[i].Dynamic_End_Date.split('A')[0]) -
          //     new Date(ret[i].start_date)) /
          //     (1 * 24 * 60 * 60 * 1000) +
          //     1)
          // ).toFixed(4) // 显示完成百分比
        } else {
          ret[i].progress = 0
        }

        ret[i].Actual_Progress = (curdays / sumdays).toFixed(4)
        // console.log(curdays, sumdays, ret[i].Actual_Progress, ret[i].progress, maxenddate, actend, parseInt(ret[i].Actual_Progress))
        if (parseInt(ret[i].Actual_Progress) === 1) {
          ret[i].Actual_End_Date = maxenddate
          actend = maxenddate
        } else {
          ret[i].Actual_End_Date = null
        }
      }
      ret[i].Early_Start_Date = earliestTask?.Early_Start_Date
      ret[i].Early_End_Date = latestTask?.Early_End_Date
      const erynd = childtasks
        .map(t => t.Needed_Start_Date)
        .filter(d => Boolean(d))
        .map(d =>
          moment(d)
            .toDate()
            .getTime()
        )
        .sort((a, b) => a - b)
        .shift()
      const lstnd = childtasks
        .map(t => t.Needed_End_Date)
        .filter(d => Boolean(d))
        .map(d =>
          moment(d)
            .toDate()
            .getTime()
        )
        .sort((a, b) => a - b)
        .pop()
      ret[i].Needed_Start_Date = erynd ? moment(erynd).format('YYYY-MM-DD') : ''
      ret[i].Needed_End_Date = lstnd ? moment(lstnd).format('YYYY-MM-DD') : ''
      ret[i].Needed_Duration =
        ret[i].Needed_Start_Date && ret[i].Needed_End_Date
          ? Math.abs(
              gantt.calculateDuration({
                start_date: moment(ret[i].Needed_Start_Date).toDate(),
                end_date: moment(ret[i].Needed_End_Date).toDate()
              })
            ) + 1
          : 0
      // 赋值WBS实际时间
      if (actstart) {
        ret[i].Actual_Start_Date = actstart
      }
      if (actend) {
        ret[i].Actual_End_Date = actend
      }
      // console.log(ret[i].text, actstart, actend)
      // 赋值实际工期
      if (actend && actstart) {
        ret[i].Actual_Duration =
          Math.abs(
            gantt.calculateDuration({
              start_date: moment(actstart).toDate(),
              end_date: moment(actend).toDate()
            })
          ) + 1
      } else if (actstart && !actend) {
        ret[i].Actual_Duration =
          Math.abs(
            gantt.calculateDuration({
              start_date: moment(actstart).toDate(),
              end_date: moment(calcDate).toDate()
            })
          ) + 1
      } else {
        ret[i].Actual_Duration = 0
      }
      // ret[i].Actual_Duration = curdays
      // 赋值计划工期
      ret[i].Plan_Duration =
        ret[i].Plan_Start_Date && ret[i].Plan_End_Date
          ? Math.abs(
              gantt.calculateDuration({
                start_date: moment(ret[i].Plan_Start_Date).toDate(),
                end_date: moment(ret[i].Plan_End_Date).toDate()
              })
            ) + 1
          : 0
      // 赋值目标工期
      ret[i].Target_Duration =
        ret[i].Target_Start_Date && ret[i].Target_End_Date
          ? Math.abs(
              gantt.calculateDuration({
                start_date: moment(ret[i].Target_Start_Date).toDate(),
                end_date: moment(ret[i].Target_End_Date).toDate()
              })
            )
          : 0
      if (childtasks.length <= 0) {
        ret[i].start_date = moment(plan.Plan_Start_Date)
          .startOf('date')
          .toDate()
        ret[i].duration = 1
        ret[i].end_date = moment(ret[i].start_date)
          .add(1, 'days')
          .startOf('date')
          .toDate()
        ret[i].Actual_Start_Date = ''
        ret[i].Actual_End_Date = ''
        ret[i].Needed_Start_Date = ''
        ret[i].Needed_End_Date = ''
        ret[i].Dynamic_Start_Date = ''
        ret[i].Dynamic_End_Date = ''
        ret[i].progress = 0
        ret[i].Actual_Progress = 0
        ret[i].Dynamic_Duration = 0
        ret[i].Actual_Duration = 0
        ret[i].Needed_Duration = 0
        ret[i].Plan_Duration = 1
        console.log(ret[i])
      }
    }
  }
  plan.Plan_Start_Date = projectStartdate
  plan.Plan_End_Date = projectenddate
}

/**
 * 使用前推法重新计算单个节点
 * @param {*} tasks 作业数组
 * @param {*} links 连接关系
 * @param {*} calcDate 计算日期
 * @param {*} projectStartdateplan 项目计划开始
 * @param {*} calctask 当前进行计算的作业
 */
function calcTaskforward(
  tasks,
  links,
  calcDate,
  projectStartdateplan,
  calctask,
  gantt
) {
  // 只有没有进行过计算的才需要重新计算,已经计算过的就不需要再进行计算了
  if (calctask.isCalcforward == false) {
    // 先判断其是否有前置节点,如果有循环其前置节点,并将前置节点全部计算完毕再计算当前节点,如果没有则直接进行计算
    /**
     * 最早开始中间变量
     */
    var esdate = null
    /**
     * 最早结束
     */
    var efdate = null
    var sourcetask = null
    /**
     * 前置关系数组
     */
    var calcLinks = links.filter(item => item.target == calctask.id)
    if (calcLinks == null || calcLinks.length == 0) {
      // 没有前置节点,则可开始的最早日期为项目计划开始日期
      if (projectStartdateplan != null) {
        esdate = projectStartdateplan
      } else {
        esdate = calctask.Plan_Start_Date
      }
    } else {
      // 循环前置节点 即ES=max{紧前活动的EF}
      for (var i = 0; i < calcLinks.length; i++) {
        /**
         * 前置作业ID
         */
        var sourceid = calcLinks[i].source
        /**
         * 前置连接类型
         */
        var linktype = calcLinks[i].type
        /**
         * 延迟天数
         */
        var lagdays = 0
        if (calcLinks[i].lag != null) {
          lagdays = parseInt(calcLinks[i].lag)
        }
        /**
         * 前置作业对象
         */
        sourcetask = tasks.filter(item => item.id == sourceid)[0]
        // console.log(JSON.stringify(sourcetask))
        calcTaskforward(
          tasks,
          links,
          calcDate,
          projectStartdateplan,
          sourcetask,
          gantt
        )
        // 判断前置节点和当前节点的关系,不同的关系会造成不同的结果
        /**
         * 作业开始日期中间变量
         */
        var tempstartdate
        /**
         * 作业结束日期中间变量
         */
        var tempenddate
        // 实际完成的前置作业不参与区控
        if (!sourcetask.Actual_End_Date) {
          if (sourcetask.type != 'milestone') {
            // source 非里程碑
            if (linktype == '0') {
              // FS
              lagdays += 1
              tempstartdate = gantt.calculateEndDate({
                start_date: sourcetask.Needed_End_Date,
                duration: lagdays
              })
            } else if (linktype == '1') {
              // SS
              tempstartdate = gantt.calculateEndDate({
                start_date: sourcetask.Needed_Start_Date,
                duration: lagdays
              })
            } else if (linktype == '2') {
              // FF
              tempenddate = gantt.calculateEndDate({
                start_date: sourcetask.Needed_End_Date,
                duration: lagdays
              })
              // 计算出开始日期
              tempstartdate = gantt.calculateEndDate({
                start_date: tempenddate,
                duration: -1 * parseInt(calctask.duration) + 1
              })
            } else if (linktype == '3') {
              // SF
              lagdays -= 1
              tempenddate = gantt.calculateEndDate({
                start_date: sourcetask.Needed_Start_Date,
                duration: lagdays
              })
              // 计算出开始日期
              tempstartdate = gantt.calculateEndDate({
                start_date: tempenddate,
                duration: -1 * parseInt(calctask.duration) + 1
              })
            } else {
              // FS
              lagdays += 1
              tempstartdate = gantt.calculateEndDate({
                start_date: sourcetask.Needed_End_Date,
                duration: lagdays
              })
            }
            // 如果存在尚需，且早于实际开始，以尚需之后第二天为最早开始
            // if(sourcetask.Needed_End_Date!==null && sourcetask.Needed_End_Date!==undefined && sourcetask.Needed_End_Date!==''){
            //   if(sourcetask.Needed_End_Date<tempstartdate){
            //     tempstartdate = gantt.calculateEndDate({
            //       start_date: sourcetask.Needed_End_Date,
            //       duration: 1
            //     })
            //   }
            // }
          } else {
            // source 为里程碑
            if (linktype == '0') {
              // FS
              // lagdays += 1;
              tempstartdate = gantt.calculateEndDate({
                start_date: sourcetask.Early_Start_Date,
                duration: lagdays
              })
            } else if (linktype == '1') {
              // SS
              tempstartdate = gantt.calculateEndDate({
                start_date: sourcetask.Early_Start_Date,
                duration: lagdays
              })
            } else if (linktype == '2') {
              // FF
              lagdays -= 1
              tempenddate = gantt.calculateEndDate({
                start_date: sourcetask.Early_Start_Date,
                duration: lagdays
              })
              // 计算出开始日期
              tempstartdate = tempenddate
            } else if (linktype == '3') {
              // SF
              lagdays -= 1
              tempenddate = gantt.calculateEndDate({
                start_date: sourcetask.Early_Start_Date,
                duration: lagdays
              })
              // 计算出开始日期
              tempstartdate = tempenddate
            } else {
              // FS
              // lagdays += 1;
              tempstartdate = gantt.calculateEndDate({
                start_date: sourcetask.Early_End_Date,
                duration: lagdays
              })
            }
          }
          if (esdate == null || esdate < tempstartdate) {
            esdate = tempstartdate
          }
          if (efdate == null || efdate < tempenddate) {
            efdate = tempenddate
          }
        }
      }
    }
    // console.log(
    //   calctask.text,
    //   moment(esdate).format('YYYY-MM-DD'),
    //   moment(efdate).format('YYYY-MM-DD')
    // )
    // 有了前置任务计算得到的最早开始日期,就可以计算其他数据
    // 判断是否已经实际开始
    if (
      calctask.Actual_Start_Date != null &&
      calctask.Actual_Start_Date != ''
    ) {
      // 暂存最早开始
      let temp_start = esdate
      const temp_end = efdate
      // -- 已实际开始
      esdate = calctask.Actual_Start_Date
      // 如果有实际开始日期,则不需要考虑,当前的关联关系以及限制条件
      calctask.start_date = calctask.Actual_Start_Date // 显示开始日期

      /**
       * 计划结束中间变量
       */
      var enddateplan = gantt.calculateEndDate({
        start_date: calctask.Actual_Start_Date,
        duration:
          calctask.Plan_Duration - 1 < 0 ? 0 : calctask.Plan_Duration - 1
      })
      // calctask.Plan_End_Date = enddateplan
      calctask.Dynamic_Start_Date = moment(calctask.Actual_Start_Date).format(
        'YYYY-MM-DD[A]'
      )
      // 如果实际的结束日期也不为空
      // 有实际开始日期,就不会再有最早结束日期和最晚结束日期,或者最早结束和最晚结束都等于实际结束日期
      // 如果没有实际结束日期,需要计算一系列数据
      if (calctask.Actual_End_Date != '' && calctask.Actual_End_Date != null) {
        calctask.Needed_Duration = 0 // 尚需工期
        calctask.Needed_End_Date = '' // 尚需完成日期
        calctask.Actual_Progress = 1
        calctask.progress = 1
        // 显示完成工期
        calctask.duration =
          gantt.calculateDuration({
            start_date: calctask.Actual_Start_Date,
            end_date: calctask.Actual_End_Date
          }) + 1

        // 显示完成日期
        calctask.Dynamic_End_Date = moment(calctask.Actual_End_Date).format(
          'YYYY-MM-DD[A]'
        )
        // calctask.Dynamic_Duration = calctask.duration
        console.log(calctask.duration, calctask)
      } else {
        // 尚需工期
        calctask.Needed_Duration = Math.ceil(
          (1 - calctask.Actual_Progress) * calctask.Plan_Duration
        )
        // 尚需开始等于状态日期
        calctask.Needed_Start_Date = calcDate

        // 如果最早开始日期晚于状态日期
        if (temp_start > calcDate) {
          calctask.Needed_Start_Date = temp_start
        }
        // 尚需完成日期
        calctask.Needed_End_Date = gantt.calculateEndDate({
          start_date: calctask.Needed_Start_Date,
          duration: calctask.Needed_Duration - 1
        })
        // 如果最早完成大于尚需完成
        if (temp_end > calctask.Needed_End_Date) {
          calctask.Needed_End_Date = temp_end
          temp_start = calctask.Needed_Start_Date = gantt.calculateEndDate({
            start_date: calctask.Needed_End_Date,
            duration: -1 * calctask.Needed_Duration + 1
          })
        }
        // 如果实际开始大于尚需开始，绘制横道以尚需开始
        if (calctask.Actual_Start_Date > calctask.Needed_Start_Date) {
          calctask.start_date = calctask.Needed_Start_Date
        }
        /**
         * 计算已经用天数
         */
        var curdurationdays = gantt.calculateDuration({
          start_date: calctask.Actual_Start_Date,
          end_date: temp_start > calcDate ? temp_start : calcDate
        })
        // 尚需开始晚于计算日期
        if (temp_start > calcDate) {
          curdurationdays -= gantt.calculateDuration({
            start_date: calcDate,
            end_date: temp_start
          })
        }
        console.log(curdurationdays)
        curdurationdays = curdurationdays < 0 ? 0 : curdurationdays

        calctask.Actual_Duration = curdurationdays
        calctask.duration = curdurationdays + parseInt(calctask.Needed_Duration) // 计算显示工期
        calctask.Dynamic_End_Date = moment(calctask.Needed_End_Date).format(
          'YYYY-MM-DD'
        ) // 显示完成日期等于尚需完成日期
        calctask.end_date = gantt.calculateEndDate({
          start_date: calctask.Needed_End_Date,
          duration: 1
        })

        calctask.Dynamic_Duration = calctask.duration

        // 计算显示完成百分比
        // 比例绘制
        // calctask.progress = (
        //   (calctask.duration -
        //     calctask.Plan_Duration +
        //     calctask.Plan_Duration * calctask.Actual_Progress) /
        //   calctask.duration
        // ).toFixed(4)
        // 绘制到状态日期
        calctask.progress = (
          calctask.Actual_Duration / calctask.duration
        ).toFixed(4)
        console.log(
          calctask.Needed_Duration,
          calctask.duration,
          calctask.Plan_Duration,
          calctask.Actual_Progress,
          calctask.Actual_Duration,
          calctask.progress
        )
      }
    } else {
      // --- 未实际开始 ---
      // 判断任务的限制条件,限制条件优先与逻辑关系
      if (
        calctask.constraint_type == null ||
        calctask.constraint_type == '' ||
        calctask.constraint_type == 'asap'
      ) {
        // 越早越好
        // 用最早开始日期作为开始日期
        // **
      } else if (calctask.constraint_type == 'snet') {
        // 不得早于...开始
        if (esdate < calctask.constraint_date) {
          esdate = calctask.constraint_date
        }
      } else if (calctask.constraint_type == 'fnet') {
        // 不得早于...结束
        /**
         * 根据限制条件反算出不得早于哪天开始
         */
        var constraintstartdate = gantt.calculateEndDate({
          start_date: calctask.constraint_date,
          duration: -1 * parseInt(calctask.duration) + 1
        })
        if (esdate < constraintstartdate) {
          esdate = constraintstartdate
        }
      } else if (calctask.constraint_type == 'snlt') {
        // 不得晚于...开始
        // if (esdate > calctask.constraintDate) {
        //    esdate = calctask.constraintDate;
        // }
      } else if (calctask.constraint_type == 'fnlt') {
        // 不得晚于...结束
        // var constraintstartdate = addDate(calctask.constraintDate, ((-1) * parseInt(calctask.duration) + 1));
        // if (esdate > constraintstartdate) {
        //    esdate = constraintstartdate;
        // }
      } else if (calctask.constraint_type == '1alap') {
        // 越晚越好
        // 用最晚开始日期作为开始日期
      } else if (calctask.constraint_type == 'mso') {
        // 必须开始于
        esdate = calctask.constraint_date
      } else {
        // 越早越好
      }
      // 里程碑任务没有结束日期以及工期的概念
      /**
       * 与状态日期进行比较,如果早于状态日期,则最早开始日期从状态日期开始
       */
      var newesdate = gantt.calculateEndDate({
        start_date: calcDate,
        duration: 0
      })
      if (
        calctask.constraint_type !== 'mso' &&
        calctask.constraint_type !== 'snlt' &&
        newesdate > esdate
      ) {
        esdate = newesdate
      }

      calctask.start_date = esdate // 显示开始日期
      if (calctask.type != 'milestone') {
        /**
         * 计划结束中间变量
         */
        var enddateplan = gantt.calculateEndDate({
          start_date: calctask.start_date,
          duration: parseInt(calctask.Plan_Duration) - 1
        })
        calctask.duration = calctask.Plan_Duration // 计算显示工期
        calctask.end_date = gantt.calculateEndDate({
          start_date: enddateplan,
          duration: 1
        })
        calctask.Needed_Duration = calctask.Plan_Duration // 尚需工期
        calctask.Needed_End_Date = enddateplan // 尚需完成日期
        calctask.Needed_Start_Date = esdate
        calctask.Dynamic_End_Date = moment(enddateplan).format('YYYY-MM-DD') // 显示完成日期等于尚需完成日期
        calctask.Dynamic_Start_Date = moment(esdate).format('YYYY-MM-DD') //
        calctask.Dynamic_Duration =
          Math.abs(
            gantt.calculateDuration({
              start_date: esdate,
              end_date: enddateplan
            })
          ) + 1
        // 未开始的作业，计划变更
        calctask.Plan_Start_Date = esdate // 计划开始日期
        calctask.Plan_End_Date = enddateplan // 计算计划结束日期
      } else {
        calctask.Plan_Start_Date = esdate
        calctask.Plan_Duration = calctask.duration = calctask.Needed_Duration = calctask.Dynamic_Duration = 0
        calctask.Dynamic_Start_Date = moment(esdate).format('YYYY-MM-DD')
        calctask.Dynamic_End_Date = calctask.Dynamic_Start_Date
        calctask.Plan_End_Date = calctask.Plan_Start_Date // 计算计划结束日期
      }

      calctask.progress = 0 // 显示完成百分比

      // 里程碑工期为0天,显示时间
      // if (sourcetask && calctask.type === 'milestone') {
      //   console.log(esdate)
      //   calctask.Needed_Start_Date = gantt.calculateEndDate({
      //     start_date: esdate,
      //     duration: -1
      //   })
      //   calctask.Needed_End_Date = calctask.Plan_Start_Date = calctask.Plan_End_Date = calctask.Early_Start_Date =
      //     calctask.Needed_Start_Date

      //   calctask.Dynamic_Start_Date = calctask.Dynamic_End_Date = moment(calctask.Needed_Start_Date).format('YYYY-MM-DD')
      // }

      // console.log(
      //   calctask.Needed_Duration,
      //   calctask.duration,
      //   calctask.Plan_Duration,
      //   calctask.Actual_Progress,
      //   calctask.Actual_Duration,
      //   calctask.progress
      // )
    }
    calctask.Early_Start_Date = esdate // 最早开始日期
    // if (sourcetask && calctask.type === 'milestone') {
    //   calctask.Early_Start_Date = calctask.Needed_Start_Date
    // }

    if (calctask.type != 'milestone') {
      calctask.Early_End_Date = gantt.calculateEndDate({
        start_date: esdate,
        duration: calctask.duration - 1
      })
    } else {
      calctask.Early_End_Date = calctask.Early_Start_Date
    }

    console.log(calctask.Early_Start_Date)
    console.log(calctask.Early_End_Date)
    // 如果有目标，计算目标相关差值
    calctask.Start_Difference = 0
    calctask.End_Difference = 0
    calctask.Duration_Difference = 0
    if (calctask.Target_Start_Date) {
      // 存在目标开始
      calctask.Start_Difference = gantt.calculateDuration({
        start_date: moment(calctask.Dynamic_Start_Date.split('A')[0]).toDate(),
        end_date: moment(calctask.Target_Start_Date).toDate()
      })
    }
    if (calctask.Target_End_Date) {
      // 存在目标结束
      calctask.End_Difference = gantt.calculateDuration({
        start_date: moment(calctask.Dynamic_End_Date.split('A')[0]).toDate(),
        end_date: moment(calctask.Target_End_Date).toDate()
      })
    }
    if (
      calctask.Target_Duration !== '' &&
      calctask.Target_Duration !== null &&
      calctask.Target_Duration !== undefined
    ) {
      // 存在目标工期
      calctask.Duration_Difference = calctask.Target_Duration
        ? Number(calctask.Target_Duration) - Number(calctask.Dynamic_Duration)
        : 0
    }
    // 计算完毕后需要将isCalc设置为true;
    calctask.isCalcforward = true

    console.log(calctask.duration, calctask.Dynamic_Duration)
  }
}

/**
 * 使用逆推法重新计算单个节点
 * @param {*} tasks
 * @param {*} links
 * @param {*} calcDate 计算日期
 * @param {*} projectEnddate 项目结束日期
 * @param {*} calctask 当前计算的任务
 */
function calcTaskbackward(
  tasks,
  links,
  calcDate,
  projectEnddate,
  calctask,
  gantt
) {
  // 只有没有进行过计算的才需要重新计算,已经计算过的就不需要再进行计算了
  if (calctask.isCalcbackward == false) {
    // 先判断其是否有前置节点,如果有循环其前置节点,并将前置节点全部计算完毕再计算当前节点,如果没有则直接进行计算
    var lfdate = null
    var calcLinks = links.filter(item => item.source == calctask.id)
    if (calcLinks == null || calcLinks.length == 0) {
      // 没有后置节点,可以直接进行计算
      if (projectEnddate != null) {
        lfdate = projectEnddate
      } else {
        lfdate = calctask.Plan_End_Date
      }
    } else {
      // 循环后置节点 最晚结束时间（LF）：在不影响项目完成时间的条件下，一项活动可能完成的最迟时间。计算公式是：LF=min{紧后活动的LS}。
      for (var i = 0; i < calcLinks.length; i++) {
        var targetid = calcLinks[i].target
        var linktype = calcLinks[i].type
        var lagdays = 0
        if (calcLinks[i].lag != null) {
          lagdays = parseInt(calcLinks[i].lag)
        }
        var targettask = tasks.filter(item => item.id == targetid)[0]
        calcTaskbackward(
          tasks,
          links,
          calcDate,
          projectEnddate,
          targettask,
          gantt
        )
        // 判断后置节点和当前节点的关系,不同的关系会造成不同的结果
        var tempenddate
        if (calctask.type != 'milestone') {
          // 非里程碑
          if (linktype == '0') {
            // FS
            lagdays += 1
            tempenddate = gantt.calculateEndDate({
              start_date: targettask.Laster_Start_Date,
              duration: -1 * parseInt(lagdays)
            })
          } else if (linktype == '1') {
            // SS
            var tempstartdate = gantt.calculateEndDate({
              start_date: targettask.Laster_Start_Date,
              duration: -1 * parseInt(lagdays)
            })

            tempenddate = gantt.calculateEndDate({
              start_date: tempstartdate,
              duration: targettask.duration
            })
          } else if (linktype == '2') {
            // FF
            tempenddate = gantt.calculateEndDate({
              start_date: targettask.Laster_End_Date,
              duration: -1 * parseInt(lagdays)
            })
          } else if (linktype == '3') {
            // SF
            lagdays -= 1
            var tempstartdate = gantt.calculateEndDate({
              start_date: targettask.Laster_End_Date,
              duration: -1 * parseInt(lagdays)
            })

            tempenddate = gantt.calculateEndDate({
              start_date: tempstartdate,
              duration: targettask.duration
            })
          } else {
            // FS
            lagdays += 1
            tempenddate = gantt.calculateEndDate({
              start_date: targettask.Laster_Start_Date,
              duration: -1 * parseInt(lagdays)
            })
          }
        } else {
          // 里程碑
          if (linktype == '0') {
            // FS
            // lagdays += 1;
            tempenddate = gantt.calculateEndDate({
              start_date: targettask.Laster_Start_Date,
              duration: -1 * parseInt(lagdays)
            })
          } else if (linktype == '1') {
            // SS
            var tempstartdate = gantt.calculateEndDate({
              start_date: targettask.Laster_Start_Date,
              duration: -1 * parseInt(lagdays)
            })
            tempenddate = tempstartdate
          } else if (linktype == '2') {
            // FF
            lagdays -= 1
            tempenddate = gantt.calculateEndDate({
              start_date: targettask.Laster_End_Date,
              duration: -1 * parseInt(lagdays)
            })
          } else if (linktype == '3') {
            // SF
            lagdays -= 1
            var tempstartdate = gantt.calculateEndDate({
              start_date: targettask.Laster_End_Date,
              duration: -1 * parseInt(lagdays)
            })
            tempenddate = tempstartdate
          } else {
            // FS
            // lagdays += 1;
            tempenddate = gantt.calculateEndDate({
              start_date: targettask.Laster_Start_Date,
              duration: -1 * parseInt(lagdays)
            })
          }
        }

        if (lfdate == null || lfdate > tempenddate) {
          lfdate = tempenddate
        }
      }
    }
    // 有了后置任务计算得到的最晚结束日期,就可以计算其他数据
    // 判断任务的限制条件
    if (
      calctask.constraint_type == null ||
      calctask.constraint_type == '' ||
      calctask.constraint_type == 'asap'
    ) {
      // 越早越好
    } else if (calctask.constraint_type == 'snet') {
      // 不得早于...开始
      // var constraintenddate = dateFtt("yyyy-MM-dd", clcEndDate(new Date(calctask.constraintDate), calctask.duration));
      // if (lfdate < calctask.constraintDate) {
      //    lfdate = calctask.constraintDate;
      // }
    } else if (calctask.constraint_type == 'fnet') {
      // 不得早于...结束
      // if (lfdate < calctask.constraintDate) {
      //    lfdate = calctask.constraintDate;
      // }
    } else if (calctask.constraint_type == 'snlt') {
      // 不得晚于...开始
      var constraintenddate = gantt.calculateEndDate({
        start_date: calctask.constraint_date,
        duration: calctask.duration
      })

      if (lfdate > constraintenddate) {
        lfdate = constraintenddate
      }
    } else if (calctask.constraint_type == 'fnlt') {
      // 不得晚于...结束
      if (lfdate > calctask.constraint_date) {
        lfdate = calctask.constraint_date
      }
    } else if (calctask.constraint_type == '1') {
      // 越晚越好
      var lfstartdate = gantt.calculateEndDate({
        start_date: lfdate,
        duration: calctask.duration + 1
      })
      var newlfdate = gantt.calculateEndDate({
        start_date: calcDate,
        duration: 1
      })

      if (newlfdate > lfstartdate) {
        lfstartdate = newlfdate
      }

      calctask.start_date = lfstartdate // 显示开始日期
      calctask.Plan_Start_Date = lfstartdate // 计划开始日期
      if (calctask.type != 'milestone') {
        var enddateplan = gantt.calculateEndDate({
          start_date: calctask.Plan_Start_Date,
          duration: calctask.Plan_Duration
        })
        calctask.Plan_End_Date = enddateplan // 计算计划结束日期
        calctask.duration = calctask.Plan_Duration // 计算显示工期
        calctask.Needed_Duration = calctask.Plan_Duration // 尚需工期
        calctask.Needed_End_Date = calctask.Plan_End_Date // 尚需完成日期
        calctask.Dynamic_End_Date = moment(calctask.Plan_End_Date).format(
          'YYYY-MM-DD'
        ) // 显示完成日期等于尚需完成日期
      }
    } else {
      // 越早越好
    }

    // 对于计算浮动工时还有待考量
    // 里程碑任务没有结束日期以及工期的概念

    if (calctask.type != 'milestone') {
      calctask.Laster_End_Date = lfdate
      calctask.Laster_Start_Date = gantt.calculateEndDate({
        start_date: calctask.Laster_End_Date,
        duration: -1 * parseInt(calctask.duration) + 1
      })
    } else {
      calctask.Laster_End_Date = lfdate
      calctask.Laster_Start_Date = lfdate
    }
    // 计算完毕后需要将isCalcbackward设置为true;
    calctask.isCalcbackward = true
  }
}

// 在tasks搜索所有节点的父节点是wbs节点的任务清单
function sumTasksByParent(wbs, tasks) {
  var childtasks = tasks.filter(
    item =>
      item.parent == wbs.id && (item.type == 'task' || item.type == 'milestone')
  )
  var rettasks = childtasks
  var childwbss = tasks.filter(
    item => item.parent == wbs.id && item.type == 'project'
  )
  if (childwbss != null && childwbss.length > 0) {
    for (var i = 0; i < childwbss.length; i++) {
      var temptasks = sumTasksByParent(childwbss[i], tasks)
      rettasks = rettasks.concat(temptasks)
    }
  }
  return rettasks
}
