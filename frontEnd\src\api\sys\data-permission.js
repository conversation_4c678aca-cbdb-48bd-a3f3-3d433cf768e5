import request from '@/utils/request'
import qs from 'qs'

export function GetWorkingObjectTree(data) {
  return request({
    method: 'post',
    url: '/Platform/WorkingObject/GetWorkingObjectTree',
    data: qs.stringify(data)
  })
}

export function GetPermittedWorkingObjTreesIsAdmin(data) {
  return request({
    method: 'post',
    url: '/Platform/WorkingObject/GetPermittedWorkingObjTreesIsAdmin',
    data: qs.stringify(data)
  })
}

// 获取对应数据权限下的用户列表 (Auth)
export function GetUserListByObjId(data) {
  return request({
    method: 'post',
    url: '/Platform/WorkingObject/GetUserListByObjId',
    data
  })
}
// 添加/更新数据权限实体 (Auth)
export function SaveWorkingObject(data) {
  return request({
    method: 'post',
    url: '/Platform/WorkingObject/SaveWorkingObject',
    data
  })
}

export function GetWorkingObjectEntity(data) {
  return request({
    url: '/Platform/WorkingObject/GetWorkingObjectEntity',
    method: 'post',
    data
  })
}

//  获取数据权限关联的用户群组 (Auth)
export function GetUserGrouByWorkingObjId(data) {
  return request({
    url: '/Platform/WorkingObject/GetUserGrouByWorkingObjId',
    method: 'post',
    data
  })
}

// 获取数据权限列表
export function GetPermittedWorkingObjs(data) {
  return request({
    method: 'post',
    url: '/Platform/WorkingObject/GetPermittedWorkingObjs',
    data
  })
}

// 获取数据权限列表
export function GetAppPermittedWorkingObjs(data) {
  return request({
    method: 'post',
    url: '/Platform/WorkingObject/GetAppPermittedWorkingObjs',
    data
  })
}
