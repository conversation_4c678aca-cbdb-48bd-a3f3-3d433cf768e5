import OSS from 'ali-oss'
import { getTenantId } from '@/utils/tenant'
import { GetOssUrl } from '@/api/sys/index'

export default function oss(option, tokenInfo) {
  const { AccessKeyId, AccessKeySecret, bucket, regionId, SecurityToken } = tokenInfo
  const ossClient = new OSS({
    region: `oss-${regionId}`,
    secure: true,
    stsToken: SecurityToken,
    accessKeyId: AccessKeyId,
    accessKeySecret: AccessKeySecret,
    bucket: bucket
  })
  let filePoint = null
  const checkpoint = option.pointFile || null
  const date = new Date()
  const file = option.file
  const path = getTenantId() + '/' + date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate() + '/' + file.name
  ossClient.multipartUpload(path, option.file, {
    parallel: 6,
    partSize: 1024 * 1024 * 2,
    checkpoint: checkpoint,
    progress: function(p, checkpoint) {
      filePoint = checkpoint
      option.onProgress({ percent: Math.floor(p * 100) })
    }
  }).then(val => {
    if (val.res.statusCode === 200) {
      //const fileUrl = val.res.requestUrls[0]
      const fileUrl = val.res.requestUrls[0] && val.res.requestUrls[0].split('?')[0]
      GetOssUrl({ url: fileUrl }).then(res => {
        option.onSuccess({ Data: fileUrl + '*' + file.size + '*' + file.name.substr(file.name.lastIndexOf('.')) + '*' + file.name , encryptionUrl: res.Data})
        //params.onSuccess({ Data: fileurl + '*' + file.size + '*' + file.name.substr(file.name.lastIndexOf('.')) + '*' + file.name, encryptionUrl: res.Data })
      })
      //option.onSuccess({ Data: (val.res.requestUrls[0].lastIndexOf('?') === -1 ? fileUrl : fileUrl.substr(0, fileUrl.lastIndexOf('?'))) + '*' + file.size + '*' + file.name.substr(file.name.lastIndexOf('.')) + '*' + file.name })
    }
  }, err => {
    option.onError(err, filePoint)
  })
}
