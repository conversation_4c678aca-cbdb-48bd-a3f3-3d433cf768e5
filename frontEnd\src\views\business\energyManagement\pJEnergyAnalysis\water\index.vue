<template>
  <div class="containerBox">
    <el-row :gutter="20">
      <el-col class="chartBox" :span="24">
        <barChart />
      </el-col>

      <!-- 龙建科工 暂时注释 -->
      <!-- <el-col class="chartBox" :span="8">
        <barChart2 />
      </el-col>
      <el-col class="chartBox" :span="4">
        <pieChart />
      </el-col>
      <el-col class="chartBox" :span="10">
        <pieChart2 />
      </el-col>
      <el-col class="chartBox" :span="10">
        <pieChart3 />
      </el-col>
      <el-col class="chartBox" :span="4">
        <pieChart4 />
      </el-col>
      <el-col :span="24">
        <pictureCard />
      </el-col> -->
    </el-row>
  </div>
</template>

<script>
import barChart from './components/bar'
import barChart2 from './components/bar2'
import pieChart from './components/pie'
import pieChart2 from './components/pie2'
import pieChart3 from './components/pie3'
import pieChart4 from './components/pie4'
import pictureCard from './components/pic'

export default {
  components: {
    barChart,
    barChart2,
    pieChart,
    pieChart2,
    pieChart3,
    pieChart4,
    pictureCard
  },
  data() {
    return {

    }
  },
  mounted() {

  }
  // inject: ['DataType', 'StartTime', 'EndTime'],
  // computed: {
  //   parentData() {
  //     return {
  //       DateType: this.DateType(),
  //       StartTime: this.StartTime(),
  //       EndTime: this.EndTime(),
  //     }
  //   }
  // }
}
</script>

<style scoped lang="scss">
.containerBox {
  .chartBox {
    height: 308px;
    border-radius: 4px;
    margin-bottom: 16px;
  }
}
</style>
