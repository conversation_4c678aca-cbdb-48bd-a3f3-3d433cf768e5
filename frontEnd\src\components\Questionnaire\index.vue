<template>
  <div class="question-table">
    <el-table border :show-header="false" :data="[{}, {}]" class="outer">
      <el-table-column
        label=""
        prop=""
        class-name="bg-grey"
        :width="tbData.code === 'tbData-026' ? 131 : ''"
      >
        <template slot-scope="{ row, column, $index }">
          <div v-if="$index === 0" class="th">调查项目</div>
          <template v-else>
            <Cascaded ref="cascaded" :obj="{ children: formatted.list }" />
          </template>
        </template>
      </el-table-column>
      <el-table-column label="" prop="title">
        <template slot-scope="{ row, column, $index }">
          <el-table
            v-if="$index === 0"
            class="inner"
            :show-header="false"
            :data="[{}]"
          >
            <el-table-column
              v-for="r in formatted.radios"
              :key="r.label"
              label=""
              prop=""
            >
              <template>
                <div class="th bg-grey">{{ r.label }}<br />{{ r.points }}</div>
              </template>
            </el-table-column>
          </el-table>
          <template v-else>
            <el-table
              :class="['leaf', 'td-bottom-bordered', 'options']"
              :show-header="false"
              :data="listData"
              :span-method="colSpanHanlder"
            >
              <el-table-column
                v-for="(r, i) in formatted.radios"
                :key="r.title"
                class-name="columRadio"
              >
                <template slot-scope="{ row, column, $index }">
                  <el-radio
                    v-if="row.type === 'radio'"
                    v-model="row.value"
                    :label="i"
                    @change="pickRadio(row, i)"
                    >{{
                      typeof row.standard !== "undefined" ? row.standard[i] : ""
                    }}</el-radio
                  >
                  <el-input
                    v-if="row.type === 'text'"
                    v-model="row.value"
                    @change="gread(row)"
                    type="textarea"
                    resize="none"
                  />
                </template>
              </el-table-column>
            </el-table>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <el-table
      v-if="false"
      :show-header="false"
      :span-method="spanHandler"
      border
      :data="formatted.list"
    >
      <el-table-column
        v-for="ci in formatted.deep + formatted.radios.length + 1"
        :key="ci"
        label=""
        prop=""
      >
        <template slot-scope="{ row, column, $index }">
          <template v-if="$index === 0 && ci - 1 === 0">
            <div class="th">调查项目</div>
          </template>
          <template v-if="$index === 0 && ci >= formatted.deep">
            <div class="th">
              {{
                formatted.radios[ci - 2]
                  ? formatted.radios[ci - 2]["label"]
                  : "-"
              }}<br />
              <strong>{{
                formatted.radios[ci - 2]
                  ? formatted.radios[ci - 2]["points"]
                  : "-"
              }}</strong>
            </div>
          </template>
          <template v-if="$index !== 0 && ci === 1">
            {{ subDeep(formatted.list[5]) }} -- {{ column }}
          </template>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import Cascaded from "./Cascaded";
export default {
  name: "Questionnaire",
  components: {
    Cascaded,
  },
  props: {
    tbData: {
      type: Object,
      default: () => ({}),
    },
    lineHeight: {
      type: Number,
      default: 68,
    },
  },
  data() {
    return {
      obj: { Satisfaction: 0, Satisfaction_Score: 0, textarea: "" },
    };
  },
  computed: {
    formatted() {
      console.log(1, this.tbData);
      const data = JSON.parse(JSON.stringify(this.tbData));
      this.formatData(data.list, 0);
      data.deep = this.tbData.deep;
      //console.log(data);
      return data;
    },
    listData() {
      const list = this.flatternData(this.formatted.list ?? []);
      console.log(list);
      return list;
    },
  },
  watch: {
    tbData() {
      setTimeout(() => {
        this.adjTDHeight();
      }, 300);
    },
  },
  created() {},
  mounted() {
    setTimeout(() => {
      this.adjTDHeight();
    }, 300);
  },
  methods: {
    adjTDHeight() {
      const leaves = Array.from(this.$el.querySelectorAll(".leafcell")).filter(
        (e) => {
          return !this.hasParentHiddenColumn(e);
        }
      );
      const options = Array.from(
        this.$el.querySelectorAll(".options .el-table__row")
      );
      for (let i = 0; i < options.length; i++) {
        const leafH = leaves[i].parentNode.parentNode.scrollHeight;
        const optH = options[i].scrollHeight;
        const h = Math.max(optH, leafH);
        // console.dir(leaves[i].parentNode.parentNode)
        // console.dir(options[i])
        // console.log(leafH, optH, h)
        options[i].style.height = h + "px";
        leaves[i].parentNode.parentNode.style.height = h + "px";
      }
    },
    hasParentHiddenColumn(ele) {
      let has = false;
      const parent = ele.parentNode;
      if (!parent) return has;
      if (!parent.classList || !parent.classList.contains("hidden-columns")) {
        has = this.hasParentHiddenColumn(parent);
      } else {
        has = true;
      }

      return has;
    },
    flatternData(arr) {
      let list = [];
      arr.forEach((a) => {
        if (a.children) {
          const subList = this.flatternData(a.children);
          list = [...list, ...subList];
        } else {
          list.push(a);
        }
      });
      return list;
    },
    formatData(dataArr, lvl) {
      if (lvl > this.tbData.deep) this.tbData.deep = lvl;
      if (dataArr && dataArr.length > 0) {
        dataArr.forEach((d, xi) => {
          if (!d.path) d.path = xi;
          d.lvl = lvl;
          d.subDeep = this.subDeep(d);

          if (d.children && d.children.length > 0) {
            d.children.forEach((sd, si) => {
              //sd.parent = d;
              sd.path = d.path + "-" + si;
            });
            this.formatData(d.children, lvl + 1);
          }
        });
      }
    },
    spanHandler({ row, column, rowIndex, columnIndex }) {
      let { rowspan, colspan } = { rowspan: 1, colspan: 1 };
      if (rowIndex === 0 && columnIndex === 0) {
        colspan = this.formatted.deep + 1;
      }
      // if (rowIndex !== 0) {
      //   // colspan = this.formatted.deep + 1
      //   // this.formatted.list[columnIndex]
      //   rowspan = this.formatted.list[columnIndex].children?.length ?? 1
      // }
      return { rowspan, colspan };
    },
    subDeep(d) {
      let deep = 0;
      if (d.children) deep++;
      if (d.children && d.children.length > 0) {
        // deep += this.subDeep
        const deeps = d.children.map((sd) => this.subDeep(sd));
        deep += Math.max(...deeps);
      }
      return deep;
    },
    subCount(d) {
      const count = d.children?.length ?? 0;
      // if (d.children && d.children.length) count =
      return count;
    },
    outSpan({ row, column, rowIndex, columnIndex }) {
      let { rowspan, colspan } = { rowspan: 1, colspan: 1 };
      if (rowIndex === 1 && columnIndex === 0) {
        colspan = 2;
      }
      return { rowspan, colspan };
    },
    colSpanHanlder({ row, column, rowIndex, columnIndex }) {
      let { rowspan, colspan } = { rowspan: 1, colspan: 1 };
      if (row.type === "text") {
        colspan = this.formatted.radios.length;
      }
      return { rowspan, colspan };
    },
    pickRadio(row, radio) {
      // console.log(row, radio);
      if (row.title === "您对该项目实施的总体满意程度") {
        this.obj.Satisfaction = radio;
      }
      if (row.title === "您留下建议或意见，我们深表感谢！") {
        this.obj.textarea = row.value;
      }
      this.$emit("grade", this.obj);
    },
    gread(row) {
      if (row.title === "总体满意度分数") {
        this.obj.Satisfaction_Score = row.value;
      }
      this.$emit("grade", this.obj);
    },
    leafHeightStyle({ row, column, rowIndex, columnIndex, store }) {
      console.log(row);
      return { height: `${this.lineHeight}px` };
    },
  },
};
</script>
<style lang="scss">
.question-table {
  > .el-table.outer {
    td {
      text-align: center;
      padding: 0;
      .cell {
        padding: 0;
      }
    }
    .el-table.leaf {
      > .el-table__body-wrapper {
        > .el-table__body {
          > tbody {
            > tr {
              > td {
                padding: 8px;
                > .cell {
                  padding: 0 10px;
                }
              }
            }
          }
        }
      }
    }
  }
  td {
    .el-table {
      border: none;
    }
    .el-table::before,
    .el-table::after {
      // height: 0;
    }
    .el-table td {
      border-bottom: none;
      .leafcell {
        overflow: hidden;
        // text-overflow: ellipsis;
        width: 100%;
      }
      .el-radio__label {
        text-overflow: ellipsis;
        white-space: normal;
        line-height: 20px;
        vertical-align: middle;
        display: inline-block;
        text-align: left;
      }
    }
    td:last-child {
      border-right: none;
    }
    .el-table.td-bottom-bordered {
      td {
        border-bottom: 1px solid #dfe6ec;
      }
    }
  }
}
.bg-grey {
  background-color: #f5f6f8;
}
</style>
