import request from '@/utils/request'

// 查询设备类型
export function GetDictionaryDetailListByCode(data) {
  return request({
    method: 'post',
    url: '/Platform/Dictionary/GetDictionaryDetailListByCode',
    data
  })
}
/** ************  通行记录 ****************/
// 通行记录-列表
export function GetTrafficRecordList(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceTrafficRecord/GetTrafficRecordList',
    data
  })
}
// 通行记录-列表
export function EntranceTrafficRecordInfo(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceTrafficRecord/EntranceTrafficRecordInfo',
    data
  })
}
// 通行记录-导出
export function ExportEntranceTrafficRecord(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceTrafficRecord/ExportEntranceTrafficRecord',
    data
  })
}

/** ************  人员管理 ****************/
// 人员列表
export function GetPersonnelList(data) {
  return request({
    method: 'post',
    url: '/DF/EntrancePersonnel/GetPersonnelList',
    data
  })
}
// 新增/修改
export function SubPersonnel(data) {
  return request({
    method: 'post',
    url: '/DF/EntrancePersonnel/SubPersonnel',
    data
  })
}

// 人员详情
export function EntrancePersonnelInfo(data) {
  return request({
    method: 'post',
    url: '/DF/EntrancePersonnel/EntrancePersonnelInfo',
    data
  })
}
// 启用
export function UpdateStatus(data) {
  return request({
    method: 'post',
    url: '/DF/EntrancePersonnel/UpdateStatus',
    data
  })
}

// 删除
export function DelPersonnel(data) {
  return request({
    method: 'post',
    url: '/DF/EntrancePersonnel/DelPersonnel',
    data
  })
}

// 下载模板
export function PersonnelImportTemplate(data) {
  return request({
    method: 'post',
    url: '/DF/EntrancePersonnel/PersonnelImportTemplate',
    data
  })
}

// 批量导入
export function EntrancePersonnelImport(data) {
  return request({
    method: 'post',
    url: '/DF/EntrancePersonnel/EntrancePersonnelImport',
    data
  })
}

// 导出
export function ExportEntrancePersonnel(data) {
  return request({
    method: 'post',
    url: '/DF/EntrancePersonnel/ExportEntrancePersonnel',
    data
  })
}

// 岗位类型
export function GetRole(data) {
  return request({
    method: 'post',
    url: '/DF/Entrance_PullDown/GetRole',
    data
  })
}

// 所属部门
export function GetDepartment(data) {
  return request({
    method: 'post',
    url: '/DF/Entrance_PullDown/GetDepartment',
    data
  })
}

// 所属单位
export function GetCompany(data) {
  return request({
    method: 'post',
    url: '/DF/Entrance_PullDown/GetCompany',
    data
  })
}
// v2 api
//通行记录列表
export function GetTrafficRecordPageList(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceTrafficRecord/v2/GetTrafficRecordPageList',
    data
  })
}
//通行记录列表导出
export function ExportEntranceEquipmentList(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceTrafficRecord/v2/ExportEntranceEquipmentList',
    data
  })
}
//新增/编辑门禁设备
export function SubEntranceEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/v2/SubEntranceEquipment',
    data
  })
}
//获取门禁设备列表
export function GetEquipmentPageList(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/v2/GetEquipmentPageList',
    data
  })
}
//导入设备列表
export function ImportEntranceEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/v2/ImportEntranceEquipment',
    data
  })
}
//导出设备列表
export function MJSBExportEntranceEquipmentList(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/v2/ExportEntranceEquipmentList',
    data
  })
}
//删除门禁设备
export function DelEntranceEquipment(params) {
  return request({
    method: 'get',
    url: '/DF/EntranceEquipment/v2/DelEntranceEquipment',
    params
  })
}
//三方系统关联ID
export function GetGroupEquipmentPull() {
  return request({
    method: 'get',
    url: '/DF/EntranceEquipment/v2/GetGroupEquipmentPull'
  })
}
//导入模板下载
export function EntranceEquipmentImportTemplate() {
  return request({
    method: 'get',
    url: '/DF/EntranceEquipment/v2/EntranceEquipmentImportTemplate'
  })
}
//获取MES班组列表
export function GetMesTeams(params) {
  return request({
    method: 'post',
    url: '/DF/Personnel/v2/GetMesTeams',
    params
  })
}
//
export function GetCompanyList(params) {
  return request({
    method: 'post',
    url: '/Platform/Department/GetCompanyList',
    params
  })
}
