import request from '@/utils/request'
//今日、本月进出园人数统计
export function GetAccessNumber(params) {
  return request({
    method: 'get',
    url: '/DF/EntranceAnalyse/GetAccessNumber',
    params
  })
}
//进园人员类型分析统计
export function GetPersonnelType(params) {
  return request({
    method: 'get',
    url: '/DF/EntranceAnalyse/GetPersonnelType',
    params
  })
}
//门禁设备统计
export function GetDeviceType(params) {
  return request({
    method: 'get',
    url: '/DF/EntranceAnalyse/GetDeviceType',
    params
  })
}
//设备实时在线率
export function GetDeviceOnLine(params) {
  return request({
    method: 'get',
    url: '/DF/EntranceAnalyse/GetDeviceOnLine',
    params
  })
}
//园区人流量趋势
export function GetPeopleFlowTrend(params) {
  return request({
    method: 'get',
    url: '/DF/EntranceAnalyse/GetPeopleFlowTrend',
    params
  })
}
//最新出入记录
export function GetTrafficRecordInfo(params) {
  return request({
    method: 'get',
    url: '/DF/EntranceAnalyse/GetTrafficRecordInfo',
    params
  })
}
//进出园工种分布
export function GetWorkTypeAccess(params) {
  return request({
    method: 'get',
    url: '/DF/EntranceAnalyse/GetWorkTypeAccess',
    params
  })
}
//获取查看更多跳转模块地址
export function GetJumpUrl(params) {
  return request({
    method: 'get',
    url: '/DF/EntranceAnalyse/GetJumpUrl',
    params
  })
}