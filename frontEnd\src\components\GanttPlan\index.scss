.gantt-plan {
  &,
  > .gantthere {
    height: 100%;
    width: 100%;
  }
  .gantt_scale_line {
    .gantt_scale_cell {
      color: #666;
    }
  }
  .gantt_row_task {
    .gantt_cell {
      border-right: 1px solid #ddd;
      &:last-child {
        border-right: none;
      }
    }
  }
  .gantt_grid_head_wbs {
    border-right: 1px solid #ddd !important;
  }
  [data-column-name='wbs'] {
    text-align: left;
  }
  .gantt_task_bg {
    position: relative;
  }
  .divLine {
    position: absolute;
    z-index: 10;
    pointer-events: none;
  }
  .gantt_scale_line .gantt_scale_cell.basedate {
    background: #fb6b7f;
    color: #fff;
  }
  .gantt_marker_content {
    width: 69px;
  }
  #wbs2task {
    position: relative;
    span.tag {
      display: inline-block;
      background: #ccc;
      height: 12px;
      width: 32px;
      border-radius: 6px;
      margin-right: 24px;
      position: absolute;
      left: 10px;
      top: 22px;
      cursor: pointer;
      span {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 12px;
        position: absolute;
        top: -4px;
        z-index: 99;
        font-size: 10px;
        line-height: 20px;
        text-align: center;
        color: #fff;
      }
      &.task span {
        left: -4px;
        background: #999;
      }
      &.wbs span {
        right: -4px;
        background: #298dff;
      }
    }
  }
  &.hasbaseline {
    .gantt_task_line,
    .gantt_line_wrapper {
      margin-top: -10px;
    }
    .gantt_task_link .gantt_link_arrow {
      margin-top: -12px;
    }
    .baseline {
      position: absolute;
      border-radius: 2px;
      opacity: 0.6;
      margin-top: -6px;
      height: 16px;
      background: rgba(255, 194, 72, 0.6);
      border: 1px solid rgba(255, 194, 72, 0.8);
    }
  }
  .gantt_layout_cell_border_bottom,
  .gantt_layout_cell_border_top,
  .gantt_layout_cell_border_right,
  .gantt_layout_cell_border_left,
  .gantt_grid_head_cell,
  .gantt_grid_scale,
  .gantt_task .gantt_task_scale .gantt_scale_cell,
  .gantt_scale_line,
  .gantt_task .gantt_task_scale .gantt_scale_cell,
  .gantt_grid_scale,
  .gantt_task_scale,
  .gantt_grid_scale .gantt_grid_head_cell {
    border-color: #eee !important;
  }
  .gantt_task_scale {
    box-shadow: #eee 0px 0px 6px;
  }
  .gantdrawer {
    bottom: 0 !important;
    top: initial;
    left: initial;
    box-shadow: #ccc 0px 0px 12px;
    .el-drawer__body {
    }
    .task-detail {
      height: 100%;
      position: relative;
      .closeme {
        position: absolute;
        right: 20px;
        top: 14px;
        cursor: pointer;
        z-index: 1;
      }
      .el-tabs {
        height: 100%;
        display: flex;
        flex-direction: column;
        flex-flow: column;
        .el-tabs__header {
          border-bottom: none;
        }
        .el-tabs__content {
          flex: auto;
          .el-tab-pane {
            height: 100%;
            >.el-row {
              height: 100%;
              >.el-col {
                height: 100%;
              }
            }
          }
          .el-form-item.el-form-item--mini{
            margin-bottom: 12px;
            &:first-child{margin-top: 4px;}
            label{
              font-weight: normal;
            }
          }
        }
        &.el-tabs--border-card {
          box-shadow: none !important;
          border-left: none;
          border-right: none;
          border-bottom: none;
        }
        .el-tabs__item {
          outline: none;
          box-shadow: none !important;
          border-bottom-width: 2px;
          border-left: none;
          border-right: none;
          &:hover {
            color: rgba(34, 40, 52, 0.8);
          }
          &.is-active {
            color: rgba(34, 40, 52, 0.8);
            font-weight: bold;
            border-image: linear-gradient(
                90deg,
                rgba(255, 0, 0, 0) 0%,
                rgba(255, 0, 0, 0) 40%,
                #298dff 40%,
                #298dff 60%,
                rgba(255, 0, 0, 0) 60%,
                rgba(255, 0, 0, 0) 100%
              )
              30 30;
          }
        }
        .el-tabs__nav {
          margin-left: 0px;
        }
        .col-block {
          display: flex;
          flex-direction: row;
          height: 100%;
          .el-table::before{
            height: 0;
          }
          textarea.el-textarea__inner{
            height: 100%;
          }
          .rlist{
            flex: auto;
            margin-left: 16px;
          }
          .rfix{
            width: 60px;
            flex-shrink: 0;
            margin-left: 16px;
          }
          .head {
            writing-mode: vertical-lr;
            text-align: center;
            width: 28px;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #298dff22;
            font-size: 13px;
            font-weight: bold;
            color: #298dff;
            letter-spacing: 8px;
          }
        }
      }
    }
  }
}

.gantt_tooltip,
.custom-plan-tooltip {
  background: #434f71;
  color: #ddd;
  border-radius: 4px;
}
.custom-plan-tooltip {
  min-width: 240px;
  header {
    padding: 0px;
    p,
    h3 {
      padding: 4px 0;
      margin: 2px 0;
    }
  }
  b {
    color: #eee;
  }
  > div {
    margin-top: 8px;
    line-height: 1.8em;
  }
}
