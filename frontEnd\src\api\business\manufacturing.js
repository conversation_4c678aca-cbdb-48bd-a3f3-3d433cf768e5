import request from '@/utils/request'
//获取产量历史数据
export function GetProduceHistory(data) {
    return request({
        method: 'post',
        url: '/DF/ReportAnalyse/GetProduceHistory',
        data
    })
}
//获取装焊指标
export function GetWeldTarget(data) {
    return request({
        method: 'post',
        url: '/DF/ReportAnalyse/GetWeldTarget',
        data
    })
}
//获取班组指标
export function GetTeamTarget(data) {
    return request({
        method: 'post',
        url: '/DF/ReportAnalyse/GetTeamTarget',
        data
    })
}
//获取能耗分析
export function GetEnergyAnalyse(data) {
    return request({
        method: 'post',
        url: '/DF/ReportAnalyse/GetEnergyAnalyse',
        data
    })
}
//获取装焊班组排名
export function GetTeamRank(data) {
    return request({
        method: 'post',
        url: '/DF/ReportAnalyse/GetTeamRank',
        data
    })
}
//获取每吨产量碳排放
export function GetCarbonEmission(data) {
    return request({
        method: 'post',
        url: '/DF/ReportAnalyse/GetCarbonEmission',
        data
    })
}
//获取单构件平均加工时长
export function GetProcessingTime(data) {
    return request({
        method: 'post',
        url: '/DF/ReportAnalyse/GetProcessingTime',
        data
    })
}
//看板基础
export function GetProductionBoardBase(data) {
    return request({
        method: 'post',
        url: '/DF/Report/GetProductionBoardBase',
        data
    })
}
//看板基础
export function GetJumpUrl(data) {
    return request({
        method: 'post',
        url: '/DF/ReportAnalyse/GetJumpUrl',
        data
    })
}