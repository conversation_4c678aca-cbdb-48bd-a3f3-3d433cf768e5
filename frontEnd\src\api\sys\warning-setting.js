import request from '@/utils/request'

// 获取预警设置分页列表
export function GetWarningSettingPageList(data) {
  return request({
    url: '/Platform/WarningSetting/GetWarningSettingPageList',
    method: 'post',
    data: data
  })
}

//  获取预警实体 (Auth)
export function GetWarningSettingEntity(data) {
  return request({
    url: '/Platform/WarningSetting/GetWarningSettingEntity',
    method: 'post',
    data: data
  })
}

export function ChangeScheduleStatus(data) {
  return request({
    url: '/Platform/WarningSetting/ChangeWarningStatus',
    method: 'post',
    data
  })
}

export function DeleteSchedule(data) {
  return request({
    url: '/Platform/WarningSetting/DeleteWarningSetting',
    method: 'post',
    data
  })
}

//  添加/更新实体 (Auth)
export function SaveWarningSettingEntity(data) {
  return request({
    url: '/Platform/WarningSetting/SaveWarningSettingEntity',
    method: 'post',
    data: data
  })
}
// 获取预警对象的列表
export function GetWarningObjectList(data) {
  return request({
    url: '/Platform/WarningObject/GetWarningObjectList',
    method: 'post',
    data: data
  })
}
// 获取可预警实体列表
export function QueryLocalPreWarningList(data) {
  return request({
    url: '/Platform/WarningSetting/QueryLocalPreWarningList',
    method: 'post',
    data: data
  })
}

