import request from '@/utils/request'
// 分析告警列表
export function GetBehaviorWarningList(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastBehaviorWarning/GetBehaviorWarningList',
    data
  })
}
export function GetBehaviorWarningListSZCJ(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastBehaviorWarning/GetBehaviorWarningList/szcj',
    data
  })
}
// 获取广播行为分析告警详情
export function GetBehaviorWarningEntity(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastBehaviorWarning/GetBehaviorWarningEntity',
    data
  })
}

// 触发行为分析告警广播
export function TriggerBehaviorWarning(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastBehaviorWarning/TriggerBehaviorWarning',
    data
  })
}

// 音频设置列表
export function GetWarningSettingList(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastMediaWarningSetting/GetWarningSettingList',
    data
  })
}

// 新增/修改广播媒体文件音频设置
export function EditWarningSetting(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastMediaWarningSetting/EditWarningSetting',
    data
  })
}

// 查看音频设置文件详情
export function GetWarningSettingEntity(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastMediaWarningSetting/GetWarningSettingEntity',
    data
  })
}

// 删除音频设置文件
export function DeleteWarningSetting(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastMediaWarningSetting/DeleteWarningSetting',
    data
  })
}

// 区域设置列表
export function GetAreaSettingList(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastAreaSetting/GetAreaSettingList',
    data
  })
}

// 批量 新增/修改告警区域设备
export function BatchEditAreaSetting(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastAreaSetting/BatchEditAreaSetting',
    data
  })
}

// 查看报警区域设置详情
export function GetAreaEntity(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastAreaSetting/GetAreaEntity',
    data
  })
}

// 删除区域设备配置数据
export function DeleteAreaSetting(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastAreaSetting/DeleteAreaSetting',
    data
  })
}

//  设备管理
export function GetEquipmentList(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastEquipment/GetEquipmentList',
    data
  })
}
//  设备管理
export function GetTreeAddress(data) {
  return request({
    method: 'post',
    url: '/DF/DFPark/GetTreeAddress',
    data
  })
}

// 获取广播时间段控制设置数据
export function GetBroTimeRangeList(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastTimeRange/GetBroTimeRangeList',
    data
  })
}

// 批量 新增/修改广播时间段控制设置
export function BatchEditBroTimeRange(data) {
  return request({
    method: 'post',
    url: '/DF/BroadcastTimeRange/BatchEditBroTimeRange',
    data
  })
}

