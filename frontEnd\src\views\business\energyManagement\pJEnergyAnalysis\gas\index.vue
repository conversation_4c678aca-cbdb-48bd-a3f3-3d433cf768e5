<template>
  <el-row v-loading="loading" :gutter="20" class="containerBox">
    <template v-for="(item, index) in componentsData">
      <el-col :key="`gasUsed-${index}`" :span="10">
        <gasUsed :custom-gas-used-config="item" :is-photovoltaic="Is_Photovoltaic" />
      </el-col>
      <el-col :key="`barChat-${index}`" :span="14">
        <barChat
          :custom-bar-chat-config="item"
          @radioChange="radioChange"
          @gasFlow="gasFlow"
        />
      </el-col>
    </template>
    <gasFlowDialog
      ref="gasFlowDialog"
      :custom-bar-chat-config="dialogObj"
      @radioChangeDialog="gasFlow"
    />
  </el-row>
</template>

<script>
import gasUsed from './components/gasUsed'
import barChat from './components/barChat'
import gasFlowDialog from './components/dialog'
import {
  GetGasTimePeriodDosageBarDiagram,
  GetGasEachNodeDosageTreeDiagram,
  GetGasLiquidPercent
} from '@/api/business/pJEnergyAnalysis'
import { GetPreferenceSettingValue } from '@/api/sys/system-setting'

export default {
  components: {
    gasUsed,
    barChat,
    gasFlowDialog
  },
  props: {
    componentsConfig: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      componentsData: [
        {
          baseData: {
            title: '氧气',
            color: '#00A8FE',
            unit: '吨',
            Total: 0,
            DataType: '全部'
          },
          gasData: {
            tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',
            showTotal: true,
            fillHeight: '100%',
            residue: {
              code: 'Oxygen',
              value: 0,
              percentage: '0%'
            },
            colData: [
              {
                title: '一车间',
                value: 0,
                percentage: '80%',
                iconName: 'workshop'
              },
              {
                title: '二车间',
                value: 0,
                percentage: '60%',
                iconName: 'workshop'
              },
              {
                title: '配送中心',
                value: 456,
                percentage: '40%',
                iconName: 'delivery'
              }
            ]
          },
          radioGroupData: [],
          barData: {
            tooltip: {
              trigger: 'axis'
            },
            xAxis: {
              type: 'category',
              data: [],
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              }
            },
            grid: {
              left: '2%',
              right: '0%',
              bottom: '10%',
              // top: '20%',
              containLabel: true
            },
            yAxis: {
              type: 'value',
              nameTextStyle: {
                color: '#888888'
              }
            },
            series: [
              {
                data: [],
                type: 'bar',
                symbol: 'emptyCircle',
                barWidth: 10,
                itemStyle: {
                  color: '#00A8FE'
                },
                tooltip: {
                  valueFormatter: function(value) {
                    return value + ' 吨'
                  }
                }
              }
            ]
          }
        },
        {
          baseData: {
            title: '二氧化碳',
            color: '#3BC9FF',
            unit: '吨',
            Total: 0
          },
          gasData: {
            tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',
            showTotal: true,
            fillHeight: '100%',
            residue: {
              code: 'Carbon',
              value: 0,
              percentage: '0%'
            },
            colData: [
              {
                title: '一车间',
                value: 0,
                percentage: '80%',
                iconName: 'workshop'
              },
              {
                title: '二车间',
                value: 0,
                percentage: '60%',
                iconName: 'workshop'
              },
              {
                title: '配送中心',
                value: 456,
                percentage: '40%',
                iconName: 'delivery'
              }
            ]
          },
          radioGroupData: [],
          barData: {
            tooltip: {
              trigger: 'axis'
            },
            xAxis: {
              type: 'category',
              data: [],
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              }
            },
            grid: {
              left: '2%',
              right: '0%',
              bottom: '10%',
              // top: '20%',
              containLabel: true
            },
            yAxis: {
              type: 'value',
              nameTextStyle: {
                color: '#888888'
              }
            },
            series: [
              {
                data: [],
                type: 'bar',
                symbol: 'emptyCircle',
                barWidth: 10,
                itemStyle: {
                  color: '#3BC9FF'
                },
                tooltip: {
                  valueFormatter: function(value) {
                    return value + ' 吨'
                  }
                }
              }
            ]
          }
        }
        // {
        //   baseData: {
        //     title: '氩气',
        //     unit: '吨',
        //     color: '#A190FC',
        //     Total: 0
        //   },
        //   gasData: {
        //     tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',
        //     showTotal: true,
        //     fillHeight: '100%',
        //     residue: {
        //       code: 'Argon',
        //       value: 0,
        //       percentage: '0%'
        //     },
        //     colData: [
        //       {
        //         title: '一车间',
        //         value: 0,
        //         percentage: '80%',
        //         iconName: 'workshop'
        //       },
        //       {
        //         title: '二车间',
        //         value: 0,
        //         percentage: '60%',
        //         iconName: 'workshop'
        //       }
        //     ]
        //   },
        //   radioGroupData: [],
        //   barData: {
        //     tooltip: {
        //       trigger: 'axis'
        //     },
        //     xAxis: {
        //       type: 'category',
        //       data: [],
        //       axisTick: {
        //         show: false
        //       },
        //       axisLine: {
        //         show: false
        //       }
        //     },
        //     grid: {
        //       left: '2%',
        //       right: '0%',
        //       bottom: '10%',
        //       // top: '20%',
        //       containLabel: true
        //     },
        //     yAxis: {
        //       type: 'value',
        //       nameTextStyle: {
        //         color: '#888888'
        //       }
        //     },
        //     series: [
        //       {
        //         data: [],
        //         type: 'bar',
        //         symbol: 'emptyCircle',
        //         barWidth: 10,
        //         itemStyle: {
        //           color: '#A190FC'
        //         },
        //         tooltip: {
        //           valueFormatter: function(value) {
        //             return value + ' 吨'
        //           }
        //         }
        //       }
        //     ]
        //   }
        // },
        // {
        //   baseData: {
        //     title: '丙烷',
        //     unit: '立方',
        //     color: '#F86161',
        //     Total: 0
        //   },
        //   gasData: {
        //     tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',
        //     showTotal: false,
        //     fillHeight: '100%',
        //     residue: {
        //       value: 0,
        //       percentage: '80%'
        //     },
        //     colData: [
        //       {
        //         title: '一车间',
        //         value: 0,
        //         percentage: '80%',
        //         iconName: 'workshop'
        //       },
        //       {
        //         title: '二车间',
        //         value: 0,
        //         percentage: '60%',
        //         iconName: 'workshop'
        //       },
        //       {
        //         title: '配送中心',
        //         value: 456,
        //         percentage: '40%',
        //         iconName: 'delivery'
        //       }
        //     ]
        //   },
        //   radioGroupData: [],
        //   barData: {
        //     tooltip: {
        //       trigger: 'axis'
        //     },
        //     xAxis: {
        //       type: 'category',
        //       data: [],
        //       axisTick: {
        //         show: false
        //       },
        //       axisLine: {
        //         show: false
        //       }
        //     },
        //     grid: {
        //       left: '2%',
        //       right: '0%',
        //       bottom: '10%',
        //       // top: '20%',
        //       containLabel: true
        //     },
        //     yAxis: {
        //       type: 'value',
        //       nameTextStyle: {
        //         color: '#888888'
        //       }
        //     },
        //     series: [
        //       {
        //         data: [],
        //         type: 'bar',
        //         symbol: 'emptyCircle',
        //         barWidth: 10,
        //         itemStyle: {
        //           color: '#F86161'
        //         },
        //         tooltip: {
        //           valueFormatter: function(value) {
        //             return value + ' 立方'
        //           }
        //         }
        //       }
        //     ]
        //   }
        // }
      ],
      componentsDialogData: [
        {
          radioGroupData: [],
          barData: {
            tooltip: {
              trigger: 'axis'
            },
            xAxis: {
              type: 'category',
              data: [],
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              }
            },
            grid: {
              left: '2%',
              right: '0%',
              bottom: '10%',
              // top: '20%',
              containLabel: true
            },
            yAxis: {
              type: 'value',
              nameTextStyle: {
                color: '#888888'
              }
            },
            series: [
              {
                data: [],
                type: 'bar',
                symbol: 'emptyCircle',
                barWidth: 10,
                itemStyle: {
                  color: '#00A8FE'
                },
                tooltip: {
                  valueFormatter: function(value) {
                    return value + ' 立方'
                  }
                }
              }
            ]
          },
          GasType: 1
        },
        {
          radioGroupData: [],
          barData: {
            tooltip: {
              trigger: 'axis'
            },
            xAxis: {
              type: 'category',
              data: [],
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              }
            },
            grid: {
              left: '2%',
              right: '0%',
              bottom: '10%',
              // top: '20%',
              containLabel: true
            },
            yAxis: {
              type: 'value',
              nameTextStyle: {
                color: '#888888'
              }
            },
            series: [
              {
                data: [],
                type: 'bar',
                symbol: 'emptyCircle',
                barWidth: 10,
                itemStyle: {
                  color: '#3BC9FF'
                },
                tooltip: {
                  valueFormatter: function(value) {
                    return value + ' 立方'
                  }
                }
              }
            ]
          },
          GasType: 2
        },
        {
          radioGroupData: [],
          barData: {
            tooltip: {
              trigger: 'axis'
            },
            xAxis: {
              type: 'category',
              data: [],
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              }
            },
            grid: {
              left: '2%',
              right: '0%',
              bottom: '10%',
              // top: '20%',
              containLabel: true
            },
            yAxis: {
              type: 'value',
              nameTextStyle: {
                color: '#888888'
              }
            },
            series: [
              {
                data: [],
                type: 'bar',
                symbol: 'emptyCircle',
                barWidth: 10,
                itemStyle: {
                  color: '#A190FC'
                },
                tooltip: {
                  valueFormatter: function(value) {
                    return value + ' 立方'
                  }
                }
              }
            ]
          },
          GasType: 3
        }
      ],
      dialogObj: {},
      loading: false,
      Is_Photovoltaic: false
    }
  },
  // inject: ['DateType', 'StartTime', 'EndTime'],
  watch: {
    componentsConfig: {
      handler(nv, ov) {
        console.log('watch')
        this.fetchData()
      }
    }
  },
  async created() {
    await this.getPreferenceSettingValue()
    this.fetchData()
    this.getGasLiquidPercent()
  },
  mounted() {

  },
  // computed: {
  //   parentData() {
  //     return {
  //       DateType: this.DateType(),
  //       StartTime: this.StartTime(),
  //       EndTime: this.EndTime(),
  //     }
  //   }
  // },
  methods: {
    async getPreferenceSettingValue() {
      const res = await GetPreferenceSettingValue({ Code: 'Is_Photovoltaic' })
      if (res && res.IsSucceed) {
        this.Is_Photovoltaic = res.Data === 'true'
      }
    },

    async fetchData() {
      const promises = ['1', '2', '3', '4'].map(async(i) => {
        const [leftResData, rightResData] = await Promise.all([
          this.getGasEachNodeDosageTreeDiagram({
            ...this.componentsConfig,
            GasType: i
          }),
          this.getGasTimePeriodDosageBarDiagram({
            ...this.componentsConfig,
            GasType: i,
            IsTotalNode: true
          })
        ])

        this.componentsData[i - 1].gasData.colData = leftResData.Nodes
        // this.componentsData[i - 1].gasData.showTotal = true;
        this.componentsData[i - 1].gasData.tooltip =
          '假定罐内气压约为1.6兆帕，温度约为-193度'
        this.componentsData[i - 1].baseData.Total = rightResData.Total
        this.componentsData[i - 1].baseData.GasType = i
        if (i !== 4) {
          this.componentsDialogData[i - 1].GasType = i
          this.componentsDialogData[i - 1].radioGroupData = [
            '全部',
            ...leftResData.Nodes.map((item) => item.Key)
          ]
        }
        this.componentsData[i - 1].radioGroupData = [
          '全部',
          ...leftResData.Nodes.map((item) => item.Key)
        ]

        const xAxisData = (rightResData.List ?? []).map((item) => item.Key)
        const seriesData = (rightResData.List ?? []).map((item) => item.Value)

        this.componentsData[i - 1].barData.xAxis.data = xAxisData
        this.componentsData[i - 1].barData.series[0].data = seriesData
      })

      await Promise.all(promises)
    },

    async getGasLiquidPercent(data) {
      const res = await GetGasLiquidPercent(data)
      this.componentsData.forEach((element) => {
        if (res.Data.length > 0) {
          const obj = res.Data.find(
            (item) => element.gasData.residue.code === item.Code
          )
          element.gasData.residue = {
            value: obj.Volume,
            percentage: `${obj.Percent}%`
          }
          element.gasData.fillHeight = `${obj.Percent}%`
        }
      })
    },
    async getGasTimePeriodDosageBarDiagram(data) {
      const res = await GetGasTimePeriodDosageBarDiagram(data)
      return res.Data
    },
    async getGasEachNodeDosageTreeDiagram(data) {
      const res = await GetGasEachNodeDosageTreeDiagram(data)
      return res.Data
    },
    async radioChange(data) {
      const i = data.GasType
      const IsTotalNode = data.val === '全部'
      const params = {
        ...this.componentsConfig,
        GasType: i,
        IsTotalNode,
        NodeName: data.val
      }
      if (data.val === '全部') {
        delete params.NodeName
      }
      const rightResData = await this.getGasTimePeriodDosageBarDiagram(params)
      const xAxisData = (rightResData.List ?? []).map((item) => item.Key)
      const seriesData = (rightResData.List ?? []).map((item) => item.Value)
      this.componentsData[i - 1].barData.xAxis.data = xAxisData
      this.componentsData[i - 1].barData.series[0].data = seriesData
      this.componentsData[i - 1].baseData.Total = rightResData.Total
    },
    async gasFlow(data) {
      this.loading = true
      this.$refs.gasFlowDialog.handleOpen()
      const i = data.GasType
      const IsTotalNode = data.val === '全部'
      const params = {
        ...this.componentsConfig,
        GasType: i,
        IsTotalNode,
        NodeName: data.val,
        IsCube: true
      }
      if (data.val === '全部') {
        delete params.NodeName
      }
      const rightResData = await this.getGasTimePeriodDosageBarDiagram(params)
      const xAxisData = (rightResData.List ?? []).map((item) => item.Key)
      const seriesData = (rightResData.List ?? []).map((item) => item.Value)
      this.componentsDialogData[i - 1].barData.xAxis.data = xAxisData
      this.componentsDialogData[i - 1].barData.series[0].data = seriesData
      this.dialogObj = this.componentsDialogData[i - 1]
      this.loading = false
    }
  }
}
</script>

<style scoped lang="scss">
.containerBox {
  .el-col {
    margin-bottom: 16px;
  }
}
</style>
