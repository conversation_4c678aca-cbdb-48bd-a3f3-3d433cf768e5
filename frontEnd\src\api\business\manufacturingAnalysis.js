import request from '@/utils/request'

// 树形结构
export function GetShopTree(data) {
  return request({
    method: 'post',
    url: '/DF/Report/GetShopTree',
    data
  })
}
//获取区域配置
export function GetAreaConfigInfo(data) {
  return request({
    method: 'post',
    url: '/DF/ReportAnalyse/GetAreaConfigInfo',
    data
  })
}
//获取班组列表
export function GetTeamList(data) {
  return request({
    method: 'post',
    url: '/DF/ReportAnalyse/GetTeamList',
    data
  })
}
//获取设备列表
export function GetEquipList(data) {
  return request({
    method: 'post',
    url: '/DF/ReportAnalyse/GetEquipList',
    data
  })
}
//保存区域配置
export function SaveAreaConfig(data) {
  return request({
    method: 'post',
    url: '/DF/ReportAnalyse/SaveAreaConfig',
    data
  })
}

// 任务量饼图
export function GetTaskRate(data) {
  return request({
    method: 'post',
    url: '/DF/Report/GetTaskRate',
    data
  })
}

// 任务量柱状图
export function GetTaskDaily(data) {
  return request({
    method: 'post',
    url: '/DF/Report/GetTaskDaily',
    data
  })
}

// 项目产量
export function GetTeamProject(data) {
  return request({
    method: 'post',
    url: '/DF/Report/GetTeamProject',
    data
  })
}

// 接受转移
export function GetTransferInfo(data) {
  return request({
    method: 'post',
    url: '/DF/Report/GetTransferInfo',
    data
  })
}

// 任务单
export function GetTaskInfo(data) {
  return request({
    method: 'post',
    url: '/DF/Report/GetTaskInfo',
    data
  })
}

// 工序所属区域
export function GetElectricity(data) {
  return request({
    method: 'post',
    url: '/DF/Report/GetElectricity',
    data
  })
}
// 人员在岗率
export function GetInJobCount(data) {
  return request({
    method: 'post',
    url: '/DF/Report/GetInJobCount',
    data
  })
}
// 在岗人效
export function GetInJobData(data) {
  return request({
    method: 'post',
    url: '/DF/Report/GetInJobData',
    data
  })
}
