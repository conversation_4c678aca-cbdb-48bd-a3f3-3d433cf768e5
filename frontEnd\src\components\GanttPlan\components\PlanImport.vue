<template>
  <div class="c-upload">
    <div
      style="background:#F7F8F9;padding:20px;margin-bottom:20px;border: 1px solid #D9DBE2;display:flex;flex-direction:row;align-items:center;justify-content: space-between;"
    >
      <p>1.其它软件导出文件</p>
    </div>
    <div style="background:#F7F8F9;padding:20px;border: 1px solid #D9DBE2;">
      <div style="margin-bottom:16px;">
        2.选择本地文件
      </div>
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        :action="action"
        :headers="reqHeader"
        :multiple="multiple"
        :accept="exts.map(ext => '.' + ext).join(',')"
        :file-list="fileList"
        :before-upload="beforeUpload"
        :on-progress="uploadProgressChange"
        :on-change="uploadStatusChange"
        :on-error="uploadError"
        :on-success="uploadSuccess"
        :auto-upload="false"
        name="files"
      >
        <svg-icon
          class="icon-svg"
          name="upload-icon"
          icon-class="upload-icon"
          width="200"
          height="200"
        />
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击选择</em>
          <div slot="tip" class="el-upload__tip">
            支持格式：{{ exts.join('、') }}，最大文件限制：{{ filesize }}M
          </div>
        </div>
        <div slot="file" slot-scope="{ file }">
          <div :class="{ 'up-item': true, error: file.status === 'fail' }">
            <div
              v-if="progresses[file.name] > 0"
              class="percent"
              :style="{ width: progresses[file.name] + '%' }"
            />
            <div class="bar">
              <div class="title">
                <svg-icon
                  style="height:30px;width:30px;"
                  :name="extIcon(file)"
                  :icon-class="extIcon(file)"
                />
                {{ file.name }}
              </div>
              <div class="remove">
                <i
                  v-if="file.status === 'fail'"
                  class="el-icon-refresh-right"
                  title="重新上传"
                  @click="reUpload(file)"
                />
                <i
                  class="el-icon-close"
                  title="移除文件"
                  @click="removeFile(file)"
                />
              </div>
            </div>
          </div>
        </div>
      </el-upload>
    </div>
    <div style="text-align:right;margin-top:20px">
      <el-button size="mini" @click="cancel">取消</el-button>

      <el-button
        type="success"
        size="mini"
        @click="beginUpload"
      >导入</el-button>
    </div>
  </div>
</template>
<script>
import request from '@/utils/request'
import store from '@/store'
import { getToken } from '@/utils/auth'
export default {
  name: 'PlanImport',
  props: {
    action: {
      type: String,
      default: ''
    },
    exts: {
      type: Array,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: false
    },
    filesize: {
      type: Number,
      default: 5
    }
  },
  data() {
    return {
      fileList: [],
      progresses: {},
      reqHeader: null,
      parsedData: null // 导入后解析生成的数据
    }
  },
  created() {
    this.reqHeader = {}
    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      this.reqHeader['Authorization'] = getToken()
    }
    if (store.getters.Last_Working_Object_Id) {
      this.reqHeader.Last_Working_Object_Id =
        store.getters.Last_Working_Object_Id
    }
  },
  methods: {
    beforeUpload(file) {
      const ext = file.name.split('.').pop()
      let isSupport = true
      if (this.exts.length > 0) {
        isSupport = this.exts.indexOf(ext) > -1
      }

      let insize = true
      insize = file.size / 1024 / 1024 < this.filesize

      if (!isSupport) {
        this.$message.error('上传文件格式错误!')
      }
      if (!insize) {
        this.$message.error('上传文件大小不能超过 ' + this.filesize + 'MB!')
      }
      return isSupport && insize
    },
    uploadProgressChange(event, file, fileList) {
      if (event.percent === 100) {
        setTimeout(() => {
          this.progresses[file.name] = 0
          this.progresses = Object.assign({}, this.progresses)
        }, 600)
      }
    },
    uploadStatusChange(file, fileList) {
      if (file.status === 'ready') {
        this.fileList.push(file)
      } else if (file.status === 'fail') {
        this.fileList = this.fileList.concat([])
      }
      var allUploaded = true
      this.fileList.forEach(f => {
        if (f.status !== 'success') {
          allUploaded = false
        }
      })
      if (allUploaded) {
        this.$emit('dialogFormSubmitSuccess', {
          type: 'imported',
          data: null
        })
        this.cancel()
      }
    },
    uploadError(err, file, fileList) {
      console.log(err, fileList)
    },
    uploadSuccess(response, file, fileList) {
      console.log(response, file)
      if (!response.IsSucceed) {
        file.status = 'fail'
        this.$message.error(response.Message)
      }
    },
    cancel() {
      this.$refs.upload.clearFiles()
      this.fileList = []
      this.$emit('dialogCancel')
    },
    beginUpload() {
      console.log('begin upload...')
      this.fileList = this.fileList.map(f => {
        f.status = 'ready'
        return f
      })
      this.$refs.upload.submit()
    },
    reUpload(file) {
      file.status = 'ready'
      this.$refs.upload.submit()
    },
    removeFile(file) {
      this.fileList = this.fileList.filter(f => f.name !== file.name)
    },
    extIcon(file) {
      let icon = 'document_unknown_icon'
      switch (file.name.split('.').pop()) {
        case 'xls':
        case 'xlsx':
          icon = 'document_form_icon'
          break
        case 'txt':
          icon = 'document_txt_icon'
          break
        case 'doc':
        case 'docx':
          icon = 'document_word_icon'
          break
        case 'zip':
        case '7z':
        case 'rar':
          icon = 'document_zip_icon'
          break
        case 'png':
        case 'jpg':
        case 'jpeg':
        case 'gif':
        case 'bmp':
          icon = 'multimedia_image_icon'
          break
        case 'ppt':
        case 'pptx':
          icon = 'document_ppt_icon'
          break
        case 'pdf':
          icon = 'document_pdf_icon'
          break
      }
      return icon
    }
  }
}
</script>

<style lang="scss" scoped>
.icon-svg {
  width: 96px;
  height: 96px;
}
.c-upload {
  .upload-demo {
    text-align: center;
  }
  .up-item {
    background: #fff;
    border: 1px solid #e6e7ec;
    border-color: #e6e7ec;
    border-radius: 4px;
    height: 48px;
    line-height: 48px;
    width: 360px;
    display: inline-block;
    position: relative;

    .percent {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      background: rgba(106, 179, 254, 0.25);
    }
    .bar {
      position: relative;
      padding: 0 16px;
      height: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .title {
        display: flex;
        align-items: center;
        svg {
          margin-right: 12px;
        }
      }
      .remove {
        position: absolute;
        right: 16px;
        top: 0;
        display: flex;
        align-items: center;
        height: 48px;
        i {
          cursor: pointer;
        }
        i:hover {
          color: #bbb;
        }
        .el-icon-close {
          position: initial;
          display: initial;
          font-size: 1.2em;
        }
      }
    }
  }
  .up-item.error {
    background: #fdf6ec;
    color: #e6a23c;
    border-color: #f5dab1;
    .el-icon-close {
      color: #e6a23c;
    }
  }
}
</style>
<style lang="scss">
.c-upload {
  .el-upload-dragger {
    border-radius: 0;
  }
  .el-upload-list__item {
    outline: none;
  }
}
</style>
