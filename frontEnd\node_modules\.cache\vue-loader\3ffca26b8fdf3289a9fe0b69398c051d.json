{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\generation.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\generation.vue", "mtime": 1754615596910}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["generation.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "generation.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis/eleNew/components", "sourcesContent": ["<template>\n  <div v-loading=\"loading\" class=\"generation\" element-loading-text=\"加载中...\">\n    <div class=\"title\">\n      <div class=\"left\">发电及用电趋势图</div>\n      <div class=\"right\">不包含重钢工厂</div>\n    </div>\n    <div class=\"chartBox\">\n      <v-chart :option=\"lineChartOption\" :autoresize=\"true\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetElectricTrend } from '@/api/business/energyManagement.js'\nimport VChart from 'vue-echarts'\nimport { use } from 'echarts/core'\nimport { CanvasRenderer } from 'echarts/renderers'\nimport { <PERSON><PERSON><PERSON>, Line<PERSON>hart, PieChart } from 'echarts/charts'\nimport {\n  GridComponent,\n  LegendComponent,\n  TooltipComponent,\n  TitleComponent,\n  DataZoomComponent\n} from 'echarts/components'\nuse([\n  Canvas<PERSON>ender<PERSON>,\n  <PERSON><PERSON>hart,\n  <PERSON><PERSON>hart,\n  <PERSON><PERSON>hart,\n  DataZoomComponent,\n  Grid<PERSON>omponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent\n])\nexport default {\n  components: {\n    VChart\n  },\n  props: {\n    isPhotovoltaic: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      lineChartOption: {\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          selected: {\n            '总发电(光伏)': false,\n            '售卖(光伏)': false\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '1%',\n          bottom: '80'\n        },\n        xAxis: {\n          type: 'category',\n          data: [],\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          }\n        },\n\n        color: ['#FF902C', '#298DFF', '#01AD5E', '#00B0F0', '#FF5E7C'],\n        yAxis: [\n          {\n            type: 'value',\n            position: 'left',\n            logBase: 10\n          }\n        ],\n        dataZoom: {\n          type: 'slider'\n        },\n        series: [\n          {\n            name: '总用电',\n            data: [5, 6, 7, 8, 1, 2, 3, 4, 6],\n            type: 'line',\n            tooltip: {\n              valueFormatter: function(value) {\n                return `${value || 0}` + ' 度'\n              }\n            }\n          },\n          {\n            name: '用电(市电)',\n            data: [1, 2, 3, 4, 5, 6, 7, 8],\n            type: 'line',\n            tooltip: {\n              valueFormatter: function(value) {\n                return `${value || 0}` + ' 度'\n              }\n            }\n          }\n        ]\n      },\n      loading: true\n    }\n  },\n  computed: {\n    parentData() {\n      return {\n        DateType: this.DateType(),\n        StartTime: this.StartTime(),\n        randomInteger: this.randomInteger()\n      }\n    }\n  },\n  watch: {\n    parentData: {\n      handler(nv, ov) {\n        this.getElectricTrend()\n      }\n    }\n  },\n  created() {\n    this.getElectricTrend()\n  },\n  mounted() {\n\n  },\n  inject: ['DateType', 'StartTime', 'randomInteger'],\n  methods: {\n    async getElectricTrend() {\n      this.loading = true\n      const res = await GetElectricTrend(this.parentData)\n      if (res.IsSucceed) {\n        this.lineChartOption.xAxis.data = res.Data.map(item => item.Key)\n        this.lineChartOption.series[0].data = res.Data.map(item => item.Total ?? 0)\n        this.lineChartOption.series[1].data = res.Data.map(item => item.Electric ?? 0)\n        if (this.isPhotovoltaic) {\n          this.lineChartOption.series.push(\n            {\n              name: '用电(光伏)',\n              data: [3, 4, 5, 6, 7, 8, 1, 2],\n              type: 'line',\n              tooltip: {\n                valueFormatter: function(value) {\n                  return `${value || 0}` + ' 度'\n                }\n              }\n            },\n            {\n              name: '总发电(光伏)',\n              data: [7, 8, 3, 4, 5, 6, 1, 2],\n              type: 'line',\n              tooltip: {\n                valueFormatter: function(value) {\n                  return `${value || 0}` + ' 度'\n                }\n              }\n            },\n            {\n              name: '售卖(光伏)',\n              data: [9, 3, 4, 5, 6, 7, 8, 1],\n              type: 'line',\n              tooltip: {\n                valueFormatter: function(value) {\n                  return `${value || 0}` + ' 度'\n                }\n              }\n            }\n          )\n          this.lineChartOption.series[2].data = res.Data.map(item => item.PV ?? 0)\n          this.lineChartOption.series[3].data = res.Data.map(item => item.TotalPV ?? 0)\n          this.lineChartOption.series[4].data = res.Data.map(item => item.SellPV ?? 0)\n        }\n      }\n      this.loading = false\n    }\n  }\n}\n</script>\n<style scoped lang='scss'>\n.generation {\n  height: 392px;\n  background: #fff;\n  border-radius: 4px;\n  width: 100%;\n  padding: 16px;\n  box-sizing: border-box;\n  margin-bottom: 16px;\n  .title {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 16px;\n    .left {\n      color: #666;\n      font-weight: bold;\n      font-size: 16px;\n    }\n    .right {\n      font-size: 12px;\n      color: #b8bec8;\n    }\n  }\n  .chartBox {\n    height: 320px;\n  }\n}\n</style>\n"]}]}