import request from '@/utils/request'
// 获取任务单分页列表
export function ReportGetTaskPageList(data) {
  return request({
    url: '/DF/OnlineData/GetTaskPageList',
    method: 'post',
    data
  })
}
// 删除任务单
export function ReportDeleteTask(data) {
  return request({
    url: '/DF/OnlineData/DeleteTask',
    method: 'post',
    data
  })
}
// 获取转移单分页列表
export function ReportGetTransferPageList(data) {
  return request({
    url: '/DF/OnlineData/GetTransferPageList',
    method: 'post',
    data
  })
}
// 删除转移单
export function ReportDeleteTransfer(data) {
  return request({
    url: '/DF/OnlineData/DeleteTransfer',
    method: 'post',
    data
  })
}
