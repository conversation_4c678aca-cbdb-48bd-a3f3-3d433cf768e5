import { constantRoutes } from '@/router'
import { RoleAuthorization } from '@/api/user'
import pageList from '@/router/modules'
import store from '@/store'
import { getQueryObject } from '@/utils'
const { name } = require('../../../package')

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role))
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit }, value) {
    return new Promise(resolve => {
      let routers = []
      const ModuleList = JSON.parse(localStorage.getItem('ModuleList'))
      const ModuleId = localStorage.getItem('ModuleId')
      if (!ModuleId || !ModuleList) {
        commit('SET_ROUTES', [])
        resolve([])
        return
      }
      const getAllAsyncRouter = getProduceMenuList(ModuleList)
      const getAsyncRouter = ModuleList.find(item => item.Id === ModuleId)

      // 处理其他子应用需要打开的菜单路由
      let appointMenu = []
      if (localStorage.getItem('AppointMenu') && localStorage.getItem('AppointMenu').includes(name)) {
        appointMenu = JSON.parse(localStorage.getItem('AppointMenu'))[name]
      }
      if (getAsyncRouter) {
        const getAsyncRouterMenu = getAllAsyncRouter.some(item => item.Id === getAsyncRouter.MenuList[0].Id) ? getAllAsyncRouter.concat(appointMenu) : getAllAsyncRouter.concat(appointMenu).concat(getAsyncRouter.MenuList)
        // const getAsyncRouterMenu = getAsyncRouter.MenuList.concat(appointMenu)
        routers = getRouter(getAsyncRouterMenu)
        commit('SET_ROUTES', routers)
        resolve(routers)
      } else {
        const getAsyncRouterMenu = getAllAsyncRouter.concat(appointMenu)
        routers = getRouter(getAsyncRouterMenu)
        commit('SET_ROUTES', routers)
        resolve(routers)
      }
    })
  }
}

// 获取当前子应用下的菜单列表
function getProduceMenuList(ModuleList) {
  return ModuleList
    .filter(module => module.ModulePlatform === name)
    .flatMap(module => module.MenuList || [])
}

function getRouter(array, parent) {
  return array.map((element, index) => {
    const { Data: { Display_Name, Icon, Url_Address, No_Cache, Code, Component, Is_Line }, Children, Id } = element
    const currentRouter = {
      path: getPath(Url_Address, Code),
      name: Code,
      component: pageList[Component],
      alwaysShow: Children.length === 1,
      meta: {
        Id,
        title: Display_Name,
        icon: Icon,
        Is_Line, // 菜单分割线
        noCache: No_Cache || false
      }
    }
    const s = getQueryObject(Url_Address)
    if (Object.keys(s).length) {
      currentRouter.query = s
    }
    if (!currentRouter.path.startsWith('http')) {
      currentRouter.path = currentRouter.path.replace('//', '/')
    }
    if (element.Children && element.Children.length > 0) {
      currentRouter.children = getRouter(element.Children, currentRouter)
    }
    return currentRouter
  })
}

function getPath(path, Code) {
  if (!path) {
    return '/' + Code
  } else {
    return path.split('?')[0]
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
