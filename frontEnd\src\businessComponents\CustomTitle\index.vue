<template>
  <div class="customTitle">
    <h2>{{ title }}</h2>
  </div>
</template>

<script>
export default {
  components: {

  },
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  created() {

  },
  mounted() {

  },
  methods: {

  },
}
</script>
<style scoped lang='scss'>
.customTitle {
  h2 {
    margin: 16px;
    font-size: 24px;
    font-weight: 400;
    color: #333;
  }
}
</style>