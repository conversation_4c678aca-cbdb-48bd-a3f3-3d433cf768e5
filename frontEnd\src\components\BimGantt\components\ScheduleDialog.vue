<template>
  <div style="margin-top:-16px; padding:0 10px;">
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="当前计划">
        <h2 style="font-size:1.8em; color:#222;">{{ plan.Name }}</h2>
      </el-form-item>
      <el-form-item label="数据日期">
        <el-date-picker
          v-model="form.Cur_Data_Date"
          type="date"
          placeholder="选择日期"
          :picker-options="pickerOptions"
        />
      </el-form-item>
      <el-form-item>
        <p>
          保存当前进度更新结果为历史版本
          <el-switch
            v-model="form.Is_Save_History"
            active-color="#13ce66"
            inactive-color="#DDD"
            style="margin-top:-2px;"
          />
        </p>
      </el-form-item>
      <el-form-item align="right" style="margin-top:32px;">
        <el-button @click="$emit('dialogCancel')">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'ScheduleDialog',
  props: {
    plan: {
      type: Object,
      default: () => ({})
    },
    gantt: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        Cur_Data_Date: '',
        Is_Save_History: true
      }
    }
  },
  computed: {
    pickerOptions() {
      const _this = this
      return {
        disabledDate(d) {
          return d < _this.plan.Plan_Start_Date
        }
      }
    }
  },
  created() {
    this.form.Cur_Data_Date = this.plan.Cur_Data_Date
  },
  methods: {
    submit() {
      if (!this.form.Cur_Data_Date) {
        return this.$message.warning('必须选择数据日期')
      }
      this.$emit('dialogFormSubmitSuccess', {
        type: 'onComputedSchedule',
        data: this.form
      })
    }
  }
}
</script>
