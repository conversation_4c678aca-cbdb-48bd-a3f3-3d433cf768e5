<template>
  <div class="task-detail">
    <el-link
      :underline="false"
      icon="el-icon-close"
      class="closeme"
      @click="closeMe"
    />
    <el-tabs v-model="tabName" type="border-card" @tab-click="handleClick">
      <el-tab-pane
        :label="
          `${
            form.type === 'project'
              ? 'WBS'
              : form.type === 'milestone'
                ? '里程碑'
                : '作业'
          }状态`
        "
        name="profile"
      >
        <el-row :gutter="20">
          <el-col :span="9">
            <div class="col-block">
              <div class="head">基本信息</div>
              <el-form
                ref="form1"
                :model="form"
                label-width="90px"
                style="width:100%"
              >
                <el-row :gutter="16">
                  <el-col :span="24">
                    <el-form-item
                      :label="
                        `${
                          form.type === 'project'
                            ? 'WBS'
                            : form.type === 'milestone'
                              ? '里程碑'
                              : '作业'
                        }名称`
                      "
                      size="mini"
                    >
                      <el-input
                        v-if="editMode && canEditTask"
                        v-model="form.text"
                        @change="formChange('text', $event)"
                      />
                      <span v-else>
                        {{ form.text }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="责任人" size="mini">
                      <el-select
                        v-if="
                          (editMode && canEditTask) ||
                            (editMode &&
                              plan.Status !== '1' &&
                              plan.Status !== '2' &&
                              canEditWBS)
                        "
                        v-model="form.Responsible_User"
                        placeholder="请选择"
                        filterable
                        @change="formChange('Responsible_User', $event)"
                      ><el-option label="选择负责人" :value="null" />
                        <el-option
                          v-for="item in members"
                          :key="item.User_Id"
                          :label="item.UserName"
                          :value="item.User_Id"
                        />
                      </el-select>
                      <span v-else>
                        {{ getUser(form.Responsible_User).UserName }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="完成进度" size="mini">
                      <template
                        v-if="
                          editMode &&
                            form.type !== 'project' &&
                            canUpdateProgress &&
                            form.type !== 'milestone'
                        "
                      >
                        <el-input-number
                          v-model="tempProgress"
                          style="width:68px;"
                          :controls="false"
                          :precision="1"
                          type="number"
                          :min="0"
                          :max="100"
                          :disabled="
                            !form.Actual_Start_Date ||
                              Boolean(form.Actual_End_Date)
                          "
                          @change="formChange('tempProgress', $event)"
                        />
                        %
                      </template>
                      <span v-else>{{ tempProgress.toFixed(2) }}%</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row
                  v-if="form.type === 'task' || form.type === 'milestone'"
                  :gutter="16"
                >
                  <el-col :span="24">
                    <el-form-item label="作业类型" size="mini">
                      <template v-if="editMode && canEditTask">
                        <el-radio
                          v-model="form.type"
                          disabled
                          label="project"
                        >WBS</el-radio>
                        <el-radio
                          v-model="form.type"
                          label="task"
                          :disabled="Boolean(form.Actual_Start_Date)"
                        >作业</el-radio>
                        <el-radio
                          v-model="form.type"
                          label="milestone"
                          :disabled="Boolean(form.Actual_Start_Date)"
                        >里程碑</el-radio>
                      </template>
                      <span v-else>
                        {{
                          form.type === 'project'
                            ? 'WBS'
                            : form.type === 'milestone'
                              ? '里程碑'
                              : '作业'
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item
                      v-if="form.type === 'project'"
                      label="运营计划开始"
                      label-width="100px"
                      size="mini"
                    >
                      {{ toDateStr(form.Control_Plan_Start_Date) }}
                    </el-form-item>
                    <el-form-item
                      v-if="form.type === 'task' || form.type === 'milestone'"
                      label="限制条件"
                      size="mini"
                    >
                      <el-select
                        v-if="editMode && canEditTask"
                        v-model="form.constraint_type"
                        placeholder="请选择"
                        :disabled="form.type === 'milestone'"
                        @change="formChange('constraint_type', $event)"
                      >
                        <el-option
                          v-for="item in constraints"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                      <span v-else>
                        {{ getConstraintType(form.constraint_type).label }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      v-if="form.type === 'project'"
                      label="运营计划结束"
                      label-width="100px"
                      size="mini"
                    >
                      {{ toDateStr(form.Control_Plan_End_Date) }}
                    </el-form-item>
                    <el-form-item
                      v-if="form.type === 'task' || form.type === 'milestone'"
                      label="限制日期"
                      size="mini"
                    >
                      <el-date-picker
                        v-if="editMode && canEditTask"
                        v-model="form.constraint_date"
                        :disabled="
                          ['asap', 'alap'].indexOf(form.constraint_type) > -1 ||
                            form.type === 'milestone'
                        "
                        class="simon-date-picker"
                        style="width:100%;"
                        type="date"
                        placeholder="选择日期"
                        @change="formChange('constraint_date', $event)"
                      />
                      <span v-else>
                        {{ toDateStr(form.constraint_date) }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="form.type === 'project'" :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="计划开始" size="mini">
                      {{ toDateStr(form.Plan_Start_Date) }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="计划结束" size="mini">
                      {{ toDateStr(form.Plan_End_Date) }}
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-col>
          <el-col :span="10">
            <div v-if="form.type === 'project'" class="col-block">
              <el-form
                ref="form2"
                :model="form"
                label-width="90px"
                style="width:100%"
              >
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="实际开始" size="mini">
                      <span>
                        {{ toDateStr(form.Actual_Start_Date) }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="实际结束" size="mini">
                      <span>
                        {{ toDateStr(form.Actual_End_Date) }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="存在问题" size="mini">
                      <el-input
                        v-if="editMode && canUpdateProgress"
                        v-model="form.Existing_Problems"
                        type="textarea"
                        rows="2"
                        @change="formChange('Existing_Problems', $event)"
                      />
                      <span v-else>
                        {{ form.Existing_Problems }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="解决措施" size="mini">
                      <el-input
                        v-if="editMode && canUpdateProgress"
                        v-model="form.Solutions"
                        type="textarea"
                        rows="2"
                        @change="formChange('Solutions', $event)"
                      />
                      <span v-else>
                        {{ form.Solutions }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="协调事项" size="mini">
                      <el-input
                        v-if="editMode && canUpdateProgress"
                        v-model="form.Need_Coordinate"
                        type="textarea"
                        rows="2"
                        @change="formChange('Need_Coordinate', $event)"
                      />
                      <span v-else>
                        {{ form.Need_Coordinate }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="协调部门" size="mini">
                      <el-input
                        v-if="editMode && canUpdateProgress"
                        v-model="form.Coordinate_Department"
                        type="textarea"
                        rows="2"
                        @change="formChange('Coordinate_Department', $event)"
                      />
                      <span v-else>
                        {{ form.Coordinate_Department }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <div
              v-if="form.type === 'task' || form.type === 'milestone'"
              class="col-block"
            >
              <div class="head">时间参数</div>
              <el-form
                ref="form2"
                :model="form"
                label-width="90px"
                style="width:100%"
              >
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="计划开始" size="mini">
                      <el-date-picker
                        v-if="
                          editMode && canEditTask && form.type !== 'milestone'
                        "
                        v-model="form.Plan_Start_Date"
                        class="simon-date-picker"
                        style="width:100%;"
                        type="date"
                        placeholder="选择日期"
                        @change="formChange('Plan_Start_Date', $event)"
                      />
                      <span v-else>
                        {{
                          form.type === 'milestone'
                            ? '-'
                            : toDateStr(form.Plan_Start_Date)
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="实际开始" size="mini">
                      <el-date-picker
                        v-if="
                          editMode &&
                            canUpdateProgress &&
                            form.type !== 'milestone'
                        "
                        v-model="form.Actual_Start_Date"
                        class="simon-date-picker"
                        style="width:100%;"
                        type="date"
                        placeholder="选择日期"
                        @change="formChange('Actual_Start_Date', $event)"
                      />
                      <span v-else>
                        {{ toDateStr(form.Actual_Start_Date) }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="计划完成" size="mini">
                      <el-date-picker
                        v-if="editMode && canEditTask"
                        v-model="form.Plan_End_Date"
                        class="simon-date-picker"
                        style="width:100%;"
                        type="date"
                        placeholder="选择日期"
                        @change="formChange('Plan_End_Date', $event)"
                      />
                      <span v-else>
                        {{ toDateStr(form.Plan_End_Date) }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="实际完成" size="mini">
                      <el-date-picker
                        v-if="editMode && canUpdateProgress"
                        v-model="form.Actual_End_Date"
                        class="simon-date-picker"
                        style="width:100%;"
                        type="date"
                        :disabled="
                          !form.Actual_Start_Date && form.type !== 'milestone'
                        "
                        placeholder="选择日期"
                        @change="formChange('Actual_End_Date', $event)"
                      />
                      <span v-else>
                        {{ toDateStr(form.Actual_End_Date) }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="计划工期" size="mini">
                      <el-input
                        v-if="editMode && canEditTask"
                        v-model="form.Plan_Duration"
                        type="number"
                        min="0"
                        :disabled="form.type === 'milestone'"
                        @change="formChange('Plan_Duration', $event)"
                      />
                      <span v-else> {{ form.Plan_Duration }}(天) </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="实际工期" size="mini">
                      <span>{{ form.Actual_Duration }}(天)</span>
                    </el-form-item>
                  </el-col> </el-row><el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="计划人工" size="mini">
                      <el-input
                        v-if="editMode && canEditTask"
                        v-model="form.Plan_Resources"
                        type="number"
                        min="0"
                        :disabled="form.type === 'milestone'"
                        @change="formChange('Plan_Resources', $event)"
                      />
                      <span v-else>
                        {{ form.Plan_Resources }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="实际人工" size="mini">
                      <el-input
                        v-if="
                          editMode &&
                            canUpdateProgress &&
                            form.type !== 'milestone'
                        "
                        v-model="form.Actual_Resources"
                        type="number"
                        min="0"
                        :disabled="form.type === 'milestone'"
                        @change="formChange('Actual_Resources', $event)"
                      />
                      <span v-else>
                        {{ form.Actual_Resources }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="col-block">
              <div class="head">
                {{
                  `${
                    form.type === 'project'
                      ? 'wbs'
                      : form.type === 'milestone'
                        ? '里程碑'
                        : '作业'
                  }备注`
                }}
              </div>
              <el-input
                v-if="editMode && canUpdateProgress"
                v-model="form.Remark"
                type="textarea"
                style="margin-left:20px;height:100%"
                @change="formChange('Remark', $event)"
              />
              <span v-else>
                {{ form.Remark }}
              </span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane v-if="form.type === 'task'" label="逻辑关系" name="relation">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="col-block">
              <div class="head">前置作业</div>
              <div class="rlist">
                <el-table
                  height="190"
                  stripe
                  :data="preTasks"
                  :cell-style="{ border: 'none' }"
                >
                  <el-table-column label="作业" prop="task" align="center">
                    <template slot-scope="{ row }">
                      {{ row['task'].text }}
                    </template>
                  </el-table-column>
                  <el-table-column label="关系" prop="link" align="center">
                    <template slot-scope="{ row }">
                      {{ getLinkType(row['link'].type).label }}
                    </template>
                  </el-table-column>
                  <el-table-column label="延迟(天)" prop="link" align="center">
                    <template slot-scope="{ row }">
                      <template
                        v-if="
                          editMode &&
                            canEditTask &&
                            !Boolean(form.Actual_Start_Date)
                        "
                      >
                        <el-input
                          v-model="row['link'].lag"
                          size="mini"
                          type="number"
                          placeholder=""
                          @change="updateLagDays(row['link'], $event)"
                        />
                      </template>
                      <template v-else>
                        {{ row['link'].lag }}
                      </template>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div v-if="false" class="rfix">
                <el-button size="mini" type="primary">分配</el-button>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="col-block">
              <div class="head">后置作业</div>
              <div class="rlist">
                <el-table
                  height="190"
                  stripe
                  :data="pastTasks"
                  :cell-style="{ border: 'none' }"
                >
                  <el-table-column label="作业" prop="task" align="center">
                    <template slot-scope="{ row }">
                      {{ row['task'].text }}
                    </template>
                  </el-table-column>
                  <el-table-column label="关系" prop="link" align="center">
                    <template slot-scope="{ row }">
                      {{ getLinkType(row['link'].type).label }}
                    </template>
                  </el-table-column>
                  <el-table-column label="延迟(天)" prop="link" align="center">
                    <template slot-scope="{ row }">
                      <template
                        v-if="
                          editMode &&
                            canEditTask &&
                            !Boolean(form.Actual_Start_Date)
                        "
                      >
                        <el-input
                          v-model="row['link'].lag"
                          size="mini"
                          type="number"
                          placeholder=""
                          @change="updateLagDays(row['link'], $event)"
                        />
                      </template>
                      <template v-else>
                        {{ row['link'].lag }}
                      </template>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div v-if="false" class="rfix">
                <el-button size="mini" type="primary">分配</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane v-if="form.type === 'task'" label="填报历史" name="history">
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="col-block">
              <div class="head">填报历史</div>
              <div class="rlist">
                <el-table
                  height="190"
                  stripe
                  :data="history"
                  :cell-style="{ border: 'none' }"
                >
                  <el-table-column
                    label="填报进度"
                    prop="Actual_Progress"
                    align="center"
                    :width="150"
                  >
                    <template
                      slot-scope="{ row }"
                    >{{
                      (Number(row['Actual_Progress']) * 100).toFixed(2)
                    }}%</template>
                  </el-table-column>
                  <!-- <el-table-column
                    label="数据日期"
                    prop="Data_Date"
                    align="center"
                    :width="150"
                  >
                    <template
                      slot-scope="{ row }"
                    >{{
                      toDateStr(row["Data_Date"])
                    }}</template>
                  </el-table-column> -->
                  <el-table-column
                    label="填报人"
                    prop="Create_UserName"
                    align="center"
                    :width="160"
                  />
                  <el-table-column
                    label="填报日期"
                    prop="Create_Date"
                    align="center"
                    :width="240"
                  >
                    <template slot-scope="{ row }">{{
                      toDateStr(row['Create_Date'])
                    }}</template>
                  </el-table-column>
                  <el-table-column label="备注" prop="Remark" align="center" />
                </el-table>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane v-if="extend" label="扩展属性" name="extend">
        <el-form label-width="100px">
          <div class="flex-form-item-wrapper">
            <template
              v-for="attr in extendfields.filter(f => f.Type !== 'longtext')"
            >
              <el-form-item :key="attr.Code" :label="attr.Label">
                <template v-if="editMode && canEditTask">
                  <el-input
                    v-if="attr.Type === 'text'"
                    v-model="form[attr.Code]"
                    autocomplete="off"
                    @change="formChange(attr.Code, $event)"
                  />
                  <el-input
                    v-if="attr.Type === 'number'"
                    v-model="form[attr.Code]"
                    type="number"
                    @change="formChange(attr.Code, $event)"
                  />
                  <el-date-picker
                    v-if="attr.Type === 'date'"
                    v-model="form[attr.Code]"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                    @change="formChange(attr.Code, $event)"
                  />
                </template>
                <span v-else>
                  {{ form[attr.Code] }}
                </span>
              </el-form-item>
            </template>
          </div>
          <div class="flex-form-item-wrapper">
            <template
              v-for="attr in extendfields.filter(f => f.Type == 'longtext')"
            >
              <el-form-item :key="attr.Code" :label="attr.Label">
                <template v-if="editMode && canEditTask">
                  <el-input
                    v-model="form[attr.Code]"
                    type="textarea"
                    :rows="2"
                    @change="formChange(attr.Code, $event)"
                  />
                </template>
                <span v-else>
                  {{ form[attr.Code] }}
                </span>
              </el-form-item>
            </template>
          </div>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { GetTaskProgressList, LINK_TYPES } from '@/api/plan/index'
import * as moment from 'moment'
export default {
  name: 'TaskDetail',
  props: {
    tabname: {
      type: String,
      default: 'profile'
    },
    taskid: {
      type: String | Number,
      default: ''
    },
    planid: {
      type: String | Number,
      default: ''
    },
    plan: {
      type: Object,
      default: () => ({})
    },
    task: {
      type: Object,
      default: () => ({})
    },
    constraints: {
      type: Array,
      default: () => []
    },
    members: {
      type: Array,
      default: () => []
    },
    gantt: {
      type: Object,
      default: null
    },
    editMode: {
      type: Boolean,
      default: true
    },
    canEditWBS: {
      type: Boolean
    },
    canEditTask: {
      type: Boolean
    },
    canUpdateProgress: {
      type: Boolean
    },
    extend: {
      // 是否具有附加的抽屉tab
      type: Boolean,
      default: false
    },
    extendfields: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {},
      taskChanged: false,
      tabName: 'profile',
      tempProgress: 0,
      history: [] // 填报历史
    }
  },
  computed: {
    /**
     * 前置作业
     */
    preTasks() {
      const links = this.gantt?.getLinks() ?? []
      const pts = []
      links.forEach(l => {
        if (l.target?.toString() === this.taskid?.toString()) {
          pts.push({ task: this.gantt.getTask(l.source), link: l })
        }
      })
      return pts
    },
    /**
     * 后置作业
     */
    pastTasks() {
      const links = this.gantt?.getLinks() ?? []
      const pts = []
      links.forEach(l => {
        if (l.source?.toString() === this.taskid?.toString()) {
          pts.push({ task: this.gantt.getTask(l.target), link: l })
        }
      })
      return pts
    },
    canEdit() {
      if (this.canEditWBS) return true
      return false
    }
  },
  watch: {
    task: function(nv, ov) {
      if (nv.id !== ov.id) {
        this.taskChanged = true
        this.buildFormFromProp()
        setTimeout(() => {
          this.taskChanged = false
        }, 100)
      }
    },
    tabName(nv) {
      if (nv === 'history') {
        this.loadTaskHistory()
      }
    },
    'form.type'(nv, ov) {
      if (ov && ov !== nv && !this.taskChanged) {
        this.formChange('type', nv)
      }
    }
  },
  created() {
    this.tabName = this.tabname ?? 'profile'
    this.buildFormFromProp()
  },
  methods: {
    buildFormFromProp() {
      if (this.tabName === 'history') {
        this.loadTaskHistory()
      }
      this.form = {
        ...this.task
      }
      this.tempProgress = this.form.Actual_Progress * 100
      this.$forceUpdate()
    },
    closeMe() {
      this.$emit('drawerCancel')
    },
    formChange(k, v) {
      // TODO: 增加一些表单内验证条件
      // ***\\
      console.log(k, v)
      // return
      if (k === 'tempProgress') {
        k = 'Actual_Progress'
        v = v / 100
      }
      this.$emit('drawerContentUpdate', {
        type: `updateGanttTask`,
        data: {
          gantt: this.gantt,
          task: this.task,
          field: k,
          value: v
        }
      })
      this.gantt.render()
    },
    updateLagDays(link, value) {
      if (!Number(value) || Number(value) < 0) {
        value = 0
      }
      this.$emit('drawerContentUpdate', {
        type: `updateLagDays`,
        data: {
          gantt: this.gantt,
          link,
          value
        }
      })
    },
    getLinkType(type) {
      return LINK_TYPES.find(t => t.value == type) ?? {}
    },
    moment(v) {
      return moment(v)
    },
    toDateStr(v) {
      if (!v) return '-'
      return moment(v)
        .startOf('date')
        .format('YYYY-MM-DD')
    },
    loadTaskHistory() {
      GetTaskProgressList({
        Plan_Id: this.planid,
        Task_Id: this.task.id
      }).then(res => {
        if (res.IsSucceed) {
          this.history = res.Data
        }
      })
    },
    handleClick(tab, event) {
      this.tabName = tab.name
    },
    getUser(id) {
      const u = this.members.find(m => m.User_Id === id)
      return u ?? {}
    },
    getConstraintType(type) {
      return this.constraints.find(c => c.value === type) ?? {}
    }
  }
}
</script>
<style lang="scss">
.simon-date-picker {
  .item.el-input__inner {
    padding-left: 36px !important;
  }
}
.flex-form-item-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  .el-form-item {
    margin-bottom: 12px;
    min-width: 305px;
    flex-shrink: 0;
  }
}
</style>
