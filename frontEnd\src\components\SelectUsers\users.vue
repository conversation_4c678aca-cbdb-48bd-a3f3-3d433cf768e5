<template>
  <div class="dep-container">
    <el-card class="box-card dep-left" shadow="none">
      <tree-detail
        ref="tree"
        :expand-on-click-node="false"
        :expanded-key="expandedKey"
        :loading="treeLoading"
        :tree-data="treeData"
        icon="icon-users"
        same-icon
        show-detail
        @handleNodeClick="handleNodeClick"
      />
    </el-card>
    <div class="dep-right" style="height: 100%;overflow: auto">
      <div style="height: 100%;overflow: auto">
        <el-table
          ref="multipleTable"
          v-loading="tbLoading"
          :data="tableData"
          height="100%"
          style="width: 100%"
          @row-click="rowClick"
          @select="handleSelectionUser"
          @selection-change="handleSelectionChange"
        >
          <el-table-column align="center" type="selection" width="55" />
          <el-table-column
            align="center"
            label="账户名"
            prop="Login_Account"
            width="120"
          />
          <el-table-column
            align="center"
            label="姓名"
            prop="Display_Name"
            width="120"
          />
          <el-table-column
            align="center"
            label="手机号"
            prop="Mobile"
          />
          <el-table-column
            align="center"
            label="邮箱"
            prop="Email"
          />
          <el-table-column
            align="center"
            label="部门"
            prop="DepartmentName"
            width="120"
          />
          <el-table-column
            align="center"
            label="状态"
            prop="UserStatusName"
            width="70"
          >
            <template slot-scope="scope">
              <el-tooltip
                v-if="scope.row.Lock_Reason !== null"
                :content="scope.row.Lock_Reason"
                class="item"
                effect="dark"
                placement="top"
              >
                <span
                  :style="scope.row.UserStatusName === '停用'? 'color:#FB6B7F' : scope.row.UserStatusName === '锁定' ? 'color:#F5C15A' :''"
                  style="cursor: pointer;"
                >{{ scope.row.UserStatusName }}</span>
              </el-tooltip>
              <span
                v-else
                :style="scope.row.UserStatusName === '停用'? 'color:#FB6B7F' : scope.row.UserStatusName === '锁定' ? 'color:#F5C15A' :''"
                style="cursor: pointer;"
              >{{ scope.row.UserStatusName }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { GetDepartmentTree, GetUserList } from '@/api/sys'
import TreeDetail from '@/components/TreeDetail'

export default {
  components: {
    TreeDetail
  },
  props: {
    userNames: {
      type: String,
      default: ''
    },
    users: {
      type: Array,
      default() {
        return []
      }
    },
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      treeLoading: false,
      expandedKey: '',
      treeData: [],
      tableData: [],
      multipleSelection: [],
      tbLoading: false,
      selectUsers: []
    }
  },
  watch: {
    multipleSelection() {
      this.selectUsers = this.selectUsers.filter(user => !this.multipleSelection.some(x => x.Id === user.Id))
    }
  },
  mounted() {
    this.$nextTick(_ => {
      this.selectUsers = [...this.users]
      this.fetchTreeData()
    })
  },
  methods: {
    groupData() {
      const arr = [...this.selectUsers, ...this.multipleSelection]
      this.selectUsers = Array.from(new Set(arr))
    },
    handleSelectionUser(val) {
      this.multipleSelection = val
    },
    fetchTreeData() {
      this.treeLoading = true
      GetDepartmentTree().then(res => {
        this.treeData = res.Data
        this.treeLoading = false
        const { Id } = this.treeData && this.treeData.length > 0 && this.treeData[0]
        this.expandedKey = Id
        this.fetchUserList(Id)
      })
    },
    fetchUserList(departmentId) {
      this.groupData()
      this.tbLoading = true
      GetUserList({ departmentId }).then(res => {
        this.tableData = res.Data
        this.tbLoading = false
        this.$nextTick(_ => {
          this.multipleSelection = this.tableData.filter(x => this.selectUsers.some(item => item.Id === x.Id))
          this.setSelectTable()
        })
      })
    },
    handleNodeClick(item) {
      this.fetchUserList(item.Id)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    getCheckedData() {
      this.groupData()
      console.log(' this.selectUsers', this.selectUsers, this.multipleSelection)
      this.$emit('update:users', this.selectUsers)
      this.$emit('handleUpdate', 1)
    },
    rowClick(row) {

    },
    // 默认选中
    setSelectTable() {
      this.multipleSelection.forEach(row => {
        this.$refs.multipleTable.toggleRowSelection(row)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dep-container {
  display: flex;
  height: 100%;
  overflow: auto;
  .dep-left {
    min-width: 180px;
    ::v-deep{
      .el-card__body{
        overflow: auto;
        height: 100%;
        padding: 10px;
      }
    }
  }

  .dep-right {
    margin-left: 10px;
    flex: 1;
  }
}
</style>
