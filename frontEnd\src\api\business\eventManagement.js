//
import request from "@/utils/request";

//  获取所有用户
export function GetUsers(data) {
  return request({
    method: "post",
    url: "/Platform/Workbench/GetUsers",
    data,
  });
}
//  获取所有角色
export function GetRoles(data) {
  return request({
    method: "post",
    url: "/Platform/Workbench/GetRoles",
    data,
  });
}

// 获取通知公告列表
export function GetPageList(data) {
  return request({
    method: "post",
    url: "/Platform/WBNotice/GetPageList",
    data,
  });
}

// 保存通知公告
export function SaveNotice(data) {
  return request({
    method: "post",
    url: "/Platform/WBNotice/SaveNotice",
    data,
  });
}

// 批量关闭通知
export function BatchCloseNotice(data) {
  return request({
    method: "post",
    url: "/Platform/WBNotice/BatchCloseNotice",
    data,
  });
}
// 删除通知公告
export function DeleteNotice(data) {
  return request({
    method: "post",
    url: "/Platform/WBNotice/DeleteNotice",
    data,
  });
}

// 获取明细数据
export function GetNoticeInfo(data) {
  return request({
    method: "get",
    url: "/Platform/WBNotice/GetNoticeInfo",
    params: data,
  });
}

// 获取通知公告下拉框
export function GetNoticeDropDownOption(data) {
  return request({
    method: "get",
    url: "/Platform/WBNotice/GetNoticeDropDownOption",
    params: data,
  });
}

// 获取发布单位列表
export function GetPublishUnitList(data) {
  return request({
    method: "get",
    url: "/Platform/WBNotice/GetPublishUnitList",
    params: data,
  });
}

// 获取级联数据
export function GetDeptUserList(data) {
  return request({
    method: "get",
    url: "/Platform/WBNotice/GetDeptUserList",
    params: data,
  });
}

//  任务设置
export function GetTaskConfigPageList(data) {
  return request({
    method: "post",
    url: "/Platform/TaskConfig/GetTaskConfigPageList",
    data,
  });
}
//  启用/关闭手机短信通知
export function EnableMobileMessageNotice(data) {
  return request({
    method: "post",
    url: "/Platform/TaskConfig/EnableMobileMessageNotice",
    data,
  });
}

//启用/关闭站内信通知
export function EnableSiteNotice(data) {
  return request({
    method: "post",
    url: "/Platform/TaskConfig/EnableSiteNotice",
    data,
  });
}
//启用/关闭告警设置
export function EnableTaskConfig(data) {
  return request({
    method: "post",
    url: "/Platform/TaskConfig/EnableTaskConfig",
    data,
  });
}

//设置通知对象
export function SetNoticeObject(data) {
  return request({
    method: "post",
    url: "/Platform/TaskConfig/SetNoticeObject",
    data,
  });
}



//查询告警设置列表
export function GetWarningConfigPageList(data) {
  return request({
    method: "post",
    url: "/Platform/WarningConfig/GetWarningConfigPageList",
    data,
  });
}
//启用/关闭站内信通知
export function EnableWarningSiteNotice(data) {
  return request({
    method: "post",
    url: "/Platform/WarningConfig/EnableSiteNotice",
    data,
  });
}

//启用/关闭手机短信通知
export function EnableWarningMobileMessageNotice(data) {
  return request({
    method: "post",
    url: "/Platform/WarningConfig/EnableMobileMessageNotice",
    data,
  });
}

//启用/关闭告警设置
export function EnableWarningConfig(data) {
  return request({
    method: "post",
    url: "/Platform/WarningConfig/EnableWarningConfig",
    data,
  });
}

//设置通知对象
export function SetWarningNoticeObject(data) {
  return request({
    method: "post",
    url: "/Platform/WarningConfig/SetNoticeObject",
    data,
  });
}

// 任务中心
// 查询任务中心列表
export function GetTaskPageList(data) {
  return request({
    method: "post",
    url: "/Platform/Task/GetTaskPageList",
    data,
  });
}
// 更新任务
export function UpdateTask(data) {
  return request({
    method: "post",
    url: "/Platform/Task/UpdateTask",
    data,
  });
}
// 类型
export function GetTypesByModule(data) {
  return request({
    method: "post",
    url: "/Platform/Workbench/GetTypesByModule",
    data,
  });
}
// 模块
export function GetModule(data) {
  return request({
    method: "post",
    url: "/Platform/Workbench/GetModule",
    data,
  });
}
// 告警中心
export function GetWarningPageList(data) {
  return request({
    method: "post",
    url: "/Platform/Warning/GetWarningPageList",
    data,
  });
}
// 批量设置告警中心
export function SetWarningStatus(data) {
  return request({
    method: "post",
    url: "/Platform/Warning/SetWarningStatus",
    data,
  });
}

// 获取消息中心
export function GetWBMessageList(data) {
  return request({
    method: "post",
    url: "/Platform/WBMessage/GetList",
    data,
  });
}
// 获取消息类型列表
export function GetMessageType(data) {
  return request({
    method: "get",
    url: "/Platform/WBMessage/GetMessageType",
    params: data,
  });
}











