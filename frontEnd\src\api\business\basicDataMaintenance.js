import request from '@/utils/request'
// 基础资料维护模块

/** ************  园区基本信息 ****************/
// 保存园区信息
export function SavePartEntity(data) {
  return request({
    method: 'post',
    url: '/DF/InforMaintain/SavePartEntity',
    data
  })
}
// 获取园区信息
export function GetParkEntity(data) {
  return request({
    method: 'post',
    url: '/DF/InforMaintain/GetParkEntity',
    data
  })
}
/** ************  建筑物管理 ****************/
// 获取用途分类列表
export function GetPurposeList(data) {
  return request({
    method: 'post',
    url: '/DF/InforMaintain/GetPurposeList',
    data
  })
}
// 获取建筑物分页列表
export function GetBuildingPageList(data) {
  return request({
    method: 'post',
    url: '/DF/InforMaintain/GetBuildingPageList',
    data
  })
}
// 保存建筑物详情
export function SaveBuildingEntity(data) {
  return request({
    method: 'post',
    url: '/DF/InforMaintain/SaveBuildingEntity',
    data
  })
}
// 获取建筑物详情
export function GetBuildingDetail(data) {
  return request({
    method: 'post',
    url: '/DF/InforMaintain/GetBuildingDetail',
    data
  })
}
// 删除建筑物
export function DeleteBuildingEntity(data) {
  return request({
    method: 'post',
    url: '/DF/InforMaintain/DeleteBuildingEntity',
    data
  })
}
