<template>
  <div>
    <el-input v-model="comNames" :readonly="true" @click.native="dialogVisible = true" />
    <el-dialog
      :visible.sync="dialogVisible"
      :destroy-on-close="true"
      class="cs-dialog plm-custom-dialog"
      title="审核者"
      width="60%"
    >
      <!-- 使用v-if的原因：dialog在关闭的时候会执行组件里面的mounted，所以需要关闭dialog时销毁子组件 -->
      <select-tab-com
        v-if="dialogVisible"
        ref="tabs"
        :show.sync="dialogVisible"
        :user-names.sync="userNamesText"
        :group-names.sync="groupNamesText"
        :role-names.sync="roleNamesText"
        :department-names.sync="departmentNamesText"
        :users.sync="selectUsers"
        :group.sync="selectGroup"
        :roles.sync="selectRoles"
        :departments.sync="selectDepartments"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import selectTabCom from './selectTabCom'

export default {
  components: {
    selectTabCom
  },
  props: {
    group: {
      type: Array,
      default() {
        return []
      }
    },
    users: {
      type: Array,
      default() {
        return []
      }
    },
    roles: {
      type: Array,
      default() {
        return []
      }
    },
    departments: {
      type: Array,
      default() {
        return []
      }
    },
    groupNames: {
      type: String,
      default: ''
    },
    userNames: {
      type: String,
      default: ''
    },
    roleNames: {
      type: String,
      default: ''
    },
    departmentNames: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      selectUserList: []
    }
  },
  computed: {
    selectGroup: {
      get() {
        return this.group
      },
      set(val) {
        this.$emit('group-change', 'group', val)
      }
    },
    selectUsers: {
      get() {
        return this.users
      },
      set(val) {
        this.$emit('users-change', 'users', val)
      }
    },
    selectDepartments: {
      get() {
        return this.departments
      },
      set(val) {
        this.$emit('departments-change', 'departments', val)
      }
    },
    selectRoles: {
      get() {
        return this.roles
      },
      set(val) {
        this.$emit('roles-change', 'roles', val)
      }
    },
    groupNamesText: {
      get() {
        return this.groupNames
      },
      set(val) {
        this.$emit('group-change', 'groupText', val)
      }
    },
    userNamesText: {
      get() {
        return this.userNames
      },
      set(val) {
        this.$emit('users-change', 'usersText', val)
      }
    },
    roleNamesText: {
      get() {
        return this.roleNames
      },
      set(val) {
        this.$emit('roles-change', 'rolesText', val)
      }
    },
    departmentNamesText: {
      get() {
        return this.departmentNames
      },
      set(val) {
        this.$emit('departments-change', 'departmentsText', val)
      }
    },
    comNames() {
      if (this.groupNamesText || this.userNamesText || this.roleNamesText || this.departmentNamesText) {
        return `${this.groupNamesText},${this.userNamesText},${this.roleNamesText},${this.departmentNamesText}`.split(',').filter(_ => !!_).toString()
      } else {
        return ''
      }
    }
  },
  methods: {
    async handleSubmit() {
      await this.$refs.tabs.handleSubmit()
      // console.log('finish')
      this.dialogVisible = false
    }
  }

}
</script>

<style lang="scss" scoped>
.cs-dialog {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  margin: 0;

  ::v-deep {
    .el-dialog{
      height: 70%;
    }

    .el-dialog__body {
      padding: 10px;
      height: calc(100% - 72px - 52px);
      overflow: hidden;
    }

    .el-tabs,.el-tab-pane{
      height: 100%;
    }
    .el-tabs__content{
      height: 100% !important;
    }
  }
}
</style>
