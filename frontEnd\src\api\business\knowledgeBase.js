// 知识库
import request from '@/utils/request'

// 获取附件
export function AttachmentGetEntities(data) {
  return request({
    url: '/Platform/Sys_File/GetAttachmentEntities',
    method: 'post',
    data
  })
}

// BIM文件更改【加载】状态
export function ChangeLoad(data) {
  return request({
    url: '/Platform/Sys_File/IsLoad',
    method: 'post',
    data
  })
}

export function FileDelete(data) {
  return request({
    url: '/Platform/Sys_File/Delete',
    method: 'post',
    data
  })
}

export function FileMove(data) {
  return request({
    url: '/Platform/Sys_File/Move',
    method: 'post',
    data
  })
}

export function FileTypeDelete(data) {
  return request({
    url: '/Platform/Sys_FileType/Delete',
    method: 'post',
    data
  })
}

// 获取文件类别内容
export function FileTypeGetEntities(data) {
  return request({
    url: '/Platform/Sys_FileType/GetEntities',
    method: 'post',
    data
  })
}

// 获取附件
export function GetEntitiesByRecordId(data) {
  return request({
    url: '/Platform/Sys_File/GetEntitiesByRecordId',
    method: 'post',
    data
  })
}

// 获取文件夹下的内容
export function GetFilesByType(data) {
  return request({
    url: '/Platform/Sys_File/GetPicEntities',
    method: 'post',
    data
  })
}

export function GetLoadingFiles(data) {
  return request({
    url: '/Platform/Sys_File/GetBIMList',
    method: 'post',
    data
  })
}

// 历史记录
export function FileHistory(data) {
  return request({
    url: '/Platform/Sys_File/OldFile',
    method: 'post',
    data
  })
}

export function FileAdd(data) {
  return request({
    url: '/Platform/Sys_File/Add',
    method: 'post',
    data,
    timeout: 1000 * 60 * 30
  })
}

export function FileEdit(data) {
  return request({
    url: '/Platform/Sys_File/Edit',
    method: 'post',
    data
  })
}

export function FileGetEntity(data) {
  return request({
    url: '/Platform/Sys_File/GetEntity',
    method: 'post',
    data
  })
}

// 新增文件夹
export function FileTypeAdd(data) {
  return request({
    url: '/Platform/Sys_FileType/Add',
    method: 'post',
    data
  })
}

export function FileTypeEdit(data) {
  return request({
    url: '/Platform/Sys_FileType/Edit',
    method: 'post',
    data
  })
}

export function FileTypeGetEntity(data) {
  return request({
    url: '/Platform/Sys_FileType/GetEntity',
    method: 'post',
    data
  })
}

// 获取当前项目联系人
export function GetProjectContacts(data) {
  return request({
    url: '/Platform/User/GetUserList',
    method: 'post',
    data
  })
}

// 通讯录
// 获取部门人员列表
export function GetUserList(data) {
  return request({
    url: '/DF/Contacts/GetUserList',
    method: 'post',
    data
  })
}
