import request from '@/utils/request'
import qs from 'qs'

//  停车场管理
// #region
// 获取停车场管理列表
export function GetPageList(data) {
  return request({
    method: 'post',
    url: '/DF/Parking/GetPageList',
    data
  })
}

// 获取停车场管理列表
export function SavePacking(data) {
  return request({
    method: 'post',
    url: '/DF/Parking/SavePacking',
    data
  })
}

// 获取停车场明细信息
export function GetPackingInfo(data) {
  return request({
    method: 'post',
    url: '/DF/Parking/GetPackingInfo',
    data
  })
}

// 删除停车场信息
export function DelPacking(data) {
  return request({
    method: 'post',
    url: '/DF/Parking/DelPacking',
    data
  })
}
// 获取车辆列表
export function GetVehicleList(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicle/GetVehicleList',
    data
  })
}

// 配置通行规则
export function SetPackingConfig(data) {
  return request({
    method: 'post',
    url: '/DF/Parking/SetPackingConfig',
    data
  })
}

// 设置停车状态
export function SetPackingStatus(data) {
  return request({
    method: 'post',
    url: '/DF/Parking/SetPackingStatus',
    data
  })
}

// 导出停车场数据信息
export function ExportData(data) {
  return request({
    method: 'get',
    url: '/DF/Parking/ExportData',
    params: data,
    responseType: 'blob'
  })
}

// 导入停车场数据信息
export function ImportDataStream(data) {
  return request({
    method: 'post',
    url: '/DF/Parking/ImportDataStream',
    data
  })
}
// #endregion

// 出入口管理
// #region
// 获取停车场出入口列表
export function GetGateWayPageList(data) {
  return request({
    method: 'post',
    url: '/DF/ParkingGateway/GetPageList',
    data
  })
}

// 保存停车场出入口信息
export function SaveGateway(data) {
  return request({
    method: 'post',
    url: '/DF/ParkingGateway/SaveGateway',
    data
  })
}

// 删除停车场出入口信息
export function DelGateway(data) {
  return request({
    method: 'post',
    url: '/DF/ParkingGateway/DelGateway',
    data
  })
}
// 获取停车场列表下拉不分页
export function GetParkingList(data) {
  return request({
    method: 'post',
    url: '/DF/Parking/GetParkingList',
    data
  })
}

// 启用/禁用出入口
export function SetGatewayStatus(data) {
  return request({
    method: 'post',
    url: '/DF/ParkingGateway/SetGatewayStatus',
    data
  })
}

// 获取设备列表下拉不分页
export function GetEquipmentList(data) {
  return request({
    method: 'post',
    url: '/DF/BarrierGateEquipment/GetEquipmentList',
    data
  })
}

// 设置关联道闸设备
export function SetRelatedEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/ParkingGateway/SetRelatedEquipment',
    data
  })
}

// 设置停车场出入口导出
export function ExportParkingGateway(data) {
  return request({
    method: 'get',
    url: '/DF/ParkingGateway/ExportData',
    params: data,
    responseType: 'blob'
  })
}

// 设置停车场出入口导入
export function ImportParkingGateway(data) {
  return request({
    method: 'post',
    url: '/DF/ParkingGateway/ImportDataStream',
    data
  })
}
// #endregion

// 道闸设备管理
// #region
// 获取设备道闸列表
export function GetBarrierPageList(data) {
  return request({
    method: 'post',
    url: '/DF/BarrierGateEquipment/GetPageList',
    data
  })
}

// 保存道闸设备信息
export function SaveEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/BarrierGateEquipment/SaveEquipment',
    data
  })
}

// 删除设备信息
export function DelEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/BarrierGateEquipment/DelEquipment',
    data
  })
}

// 导出停车场道闸设备信息
export function ExportBarrierEquipment(data) {
  return request({
    method: 'get',
    url: '/DF/BarrierGateEquipment/ExportData',
    params: data,
    responseType: 'blob'
  })
}

// 导出停车场道闸设备信息
export function ImportBarrierEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/BarrierGateEquipment/ImportDataStream',
    data
  })
}
// #endregion

// 车辆管理
// #region
// 获取车辆管理列表
export function GetVehiclePageList(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicle/GetPageList',
    data
  })
}

// 保存车辆信息
export function SaveVehicle(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicle/SaveVehicle',
    data
  })
}

// 删除车辆信息
export function DelVehicle(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicle/DelVehicle',
    data
  })
}

// 删除列入黑名单
export function SetVehicleStatus(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicle/SetVehicleStatus',
    data
  })
}

// 车辆管理导出
export function ExportVehicleData(data) {
  return request({
    method: 'get',
    url: '/DF/Vehicle/ExportData',
    params: data,
    responseType: 'blob'
  })
}

// 车辆管理导入
export function ImportVehicleData(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicle/ImportDataStream',
    data
  })
}
// #endregion

// 车辆告警规则配置
// #region
// 获取告警规则配置信息
export function GetConfigInfo(data) {
  return request({
    method: 'get',
    url: '/DF/AlarmConfig/GetConfigInfo',
    params: data
  })
}

// 获取告警规则配置信息
export function SetConfigInfo(data) {
  return request({
    method: 'post',
    url: '/DF/AlarmConfig/SetConfig',
    data
  })
}

// #endregion

// 车辆通行记录
// #region
// 获取车辆通行记录
export function GetPassRecordList(data) {
  return request({
    method: 'post',
    url: '/DF/VehiclePassRecord/GetPageList',
    data
  })
}

// 导出车辆通行记录
export function ExportPassRecordData(data) {
  return request({
    method: 'get',
    url: '/DF/VehiclePassRecord/ExportData',
    params: data,
    responseType: 'blob'
  })
}
// #endregion

// 车辆告警记录
// #region
// 获取车辆告警记录
export function GetVehicleAlarmList(data) {
  return request({
    method: 'post',
    url: '/DF/VehicleAlarm/GetPageList',
    data
  })
}

// 关闭告警通知
export function SetAlarmStatus(data) {
  return request({
    method: 'post',
    url: '/DF/VehicleAlarm/SetAlarmStatus',
    data
  })
}

// 导出车辆告警列表
export function ExportVehicleAlarm(data) {
  return request({
    method: 'get',
    url: '/DF/VehicleAlarm/ExportData',
    params: data,
    responseType: 'blob'
  })
}

// 导出车辆黑名单
export function ExportBlackListData(data) {
  return request({
    method: 'get',
    url: '/DF/Vehicle/ExportBlackListData',
    params: data,
    responseType: 'blob'
  })
}
// 导出车辆通行记录-超时
export function ExportTimeoutData(data) {
  return request({
    method: 'get',
    url: '/DF/ParkingVehicle/ExportTimeoutData',
    params: data,
    responseType: 'blob'
  })
}
// 导出车辆通行记录
export function parkingVehicleExportData(data) {
  return request({
    method: 'get',
    url: '/DF/ParkingVehicle/ExportData',
    params: data,
    responseType: 'blob'
  })
}
// 获取车辆黑名单列表
export function vehicleGetPageList(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicle/GetPageList',
    data
  })
}

// 获取现有车辆列表
export function parkingVehicleGetPageList(data) {
  return request({
    method: 'post',
    url: '/DF/ParkingVehicle/GetPageList',
    data
  })
}

// 关闭告警
export function UpdateWarningStatus(data) {
  return request({
    method: 'post',
    url: '/DF/VehicleAlarm/UpdateWarningStatus',
    data
  })
}

// 车辆出入设置
// #region
// 获取出入规则设置
export function GetVehicleConfigInfo(data) {
  return request({
    method: 'get',
    url: '/DF/VehicleConfig/GetConfigInfo',
    params: data
  })
}

// 获取出入规则设置
export function SaveConfig(data) {
  return request({
    method: 'post',
    url: '/DF/VehicleConfig/SaveConfig',
    data
  })
}

// 获取出入口设置列表
export function GetEntranceList(data) {
  return request({
    method: 'post',
    url: '/DF/VehicleConfig/GetEntranceList',
    data
  })
}
// 获取出入口设置列表
export function GetEquipList(data) {
  return request({
    method: 'get',
    url: '/DF/VehicleConfig/GetEquipList',
    params: data
  })
}

// 获取出入口设置列表
export function GetEntranceEquipDetail(data) {
  return request({
    method: 'post',
    url: '/DF/VehicleConfig/GetEntranceEquipDetail',
    data
  })
}
// 保存出入口设备
export function SaveEntranceEquip(data) {
  return request({
    method: 'post',
    url: '/DF/VehicleConfig/SaveEntranceEquip',
    data
  })
}
// 删除出入口设备配置
export function DelEntranceEquip(data) {
  return request({
    method: 'post',
    url: '/DF/VehicleConfig/DelEntranceEquip',
    data
  })
}
// #endregion

// 道闸设备管理
// #region
// 获取出入规则设置
export function GetVBEquipList(data) {
  return request({
    method: 'post',
    url: '/DF/VBEquipment/GetEquipList',
    data
  })
}

// 获取三方设备列表
export function GetThirdEquipList(data) {
  return request({
    method: 'get',
    url: '/DF/VBEquipment/GetThirdEquipList',
    params: data
  })
}

// 获取设备详情
export function GetEquipDetail(data) {
  return request({
    method: 'post',
    url: '/DF/VBEquipment/GetEquipDetail',
    data
  })
}
// 保存设备信息
export function SaveEquip(data) {
  return request({
    method: 'post',
    url: '/DF/VBEquipment/SaveEquip',
    data
  })
}

// 删除设备信息
export function DelEquip(data) {
  return request({
    method: 'post',
    url: '/DF/VBEquipment/DelEquip',
    data
  })
}

// 获取查询条件下拉框选项
export function GetDropList(data) {
  return request({
    method: 'get',
    url: '/DF/VBEquipment/GetDropList',
    params: data
  })
}

// 导出车辆道闸设备列表
export function ExportVBData(data) {
  return request({
    method: 'post',
    url: '/DF/VBEquipment/ExportData',
    data,
    responseType: 'blob'
  })
}

// 下载导入模板
export function DownloadVBEquipTemplate(data) {
  return request({
    method: 'get',
    url: '/DF/VBEquipment/DownloadTemplate',
    params: data,
    responseType: 'blob'
  })
}

// 导入车辆数据信息
export function ImportVBEquipDataStream(data) {
  return request({
    method: 'post',
    url: '/DF/VBEquipment/ImportDataStream',
    data
  })
}
// #endregion


// v2 版本 内部车辆管理
// 获取车辆列表
export function VehiclesGetVehicleList(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicles/GetVehicleList',
    data
  })
}
// 保存车辆信息
export function VehiclesSaveVehicle(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicles/SaveVehicle',
    data
  })
}
// 删除车辆
export function VehiclesDelVehicle(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicles/DelVehicle',
    data
  })
}
// 导出
export function VehiclesExportData(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicles/ExportData',
    data,
    responseType: 'blob'
  })
}

// 获取部门列表
export function VehiclesGetDeptList(data) {
  return request({
    method: 'get',
    url: '/DF/Vehicles/GetDeptList',
    params: data,
  })
}

// 获取部门人员列表
export function VehiclesGetDeptUserList(data) {
  return request({
    method: 'get',
    url: '/DF/Vehicles/GetDeptUserList',
    params: data,
  })
}

// 获取电表类型
export function GetDictionaryDetailListByCode(data) {
  return request({
    method: 'post',
    url: '/Platform/Dictionary/GetDictionaryDetailListByCode',
    data,
  })
}

// 下载内部车辆 模板
export function VehiclesDownloadTemplate(data) {
  return request({
    method: 'get',
    url: '/DF/Vehicles/DownloadTemplate',
    params: data,
    responseType: 'blob'
  })
}
// 导入二进制流
export function VehiclesImportDataStream(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicles/ImportDataStream',
    data,
  })
}
// 获取内部车辆详情信息
export function VehiclesGetVehicleDetail(data) {
  return request({
    method: 'post',
    url: '/DF/Vehicles/GetVehicleDetail',
    data,
  })
}

// 获取查询列表下拉选项
export function VBPassRecordGetDropList(data) {
  return request({
    method: 'get',
    url: '/DF/VBPassRecord/GetDropList',
    params: data,
  })
}

// 查询车辆通行记录列表
export function VBPassRecordGetPassRecordList(data) {
  return request({
    method: 'post',
    url: '/DF/VBPassRecord/GetPassRecordList',
    data,
  })
}

// 获取车辆通行记录详情
export function VBPassRecordGetPassRecordDetail(data) {
  return request({
    method: 'post',
    url: '/DF/VBPassRecord/GetPassRecordDetail',
    data,
  })
}

// 导出车辆通行记录列表
export function VBPassRecordExportData(data) {
  return request({
    method: 'post',
    url: '/DF/VBPassRecord/ExportData',
    data,
    responseType: 'blob'
  })
}

// 查询在园车辆列表
export function VBInParkVehiclesGetInParkVehiclesList(data) {
  return request({
    method: 'post',
    url: '/DF/VBInParkVehicles/GetInParkVehiclesList',
    data,
  })
}
// 获取在园车辆详情
export function VBInParkVehiclesGetInParkVehicleDetail(data) {
  return request({
    method: 'post',
    url: '/DF/VBInParkVehicles/GetInParkVehicleDetail',
    data,
  })
}

// 获取在园车辆统计信息
export function VBInParkVehiclesGetInParkStatistics(data) {
  return request({
    method: 'post',
    url: '/DF/VBInParkVehicles/GetInParkStatistics',
    data,
  })
}

// 车辆出园
export function VBInParkVehiclesVehicleLeaving(data) {
  return request({
    method: 'post',
    url: '/DF/VBInParkVehicles/VehicleLeaving',
    data,
  })
}


// 导出车辆通行记录列表
export function VBInParkVehiclesExportData(data) {
  return request({
    method: 'post',
    url: '/DF/VBInParkVehicles/ExportData',
    data,
    responseType: 'blob'
  })
}


// 车辆分析
// 获取在园车辆统计
export function GetInParkVehiclesFX(data) {
  return request({
    method: 'get',
    url: '/DF/VehiclesAnalyses/GetInParkVehicles',
    params: data,
  })
}
// 获取最新出入记录
export function GetPassRecordFX(data) {
  return request({
    method: 'get',
    url: '/DF/VehiclesAnalyses/GetPassRecord',
    params: data,
  })
}

// 获取超时车辆分析
export function GetTimeoutVehiclesFX(data) {
  return request({
    method: 'get',
    url: '/DF/VehiclesAnalyses/GetTimeoutVehicles',
    params: data,
  })
}
// 获取设备实时状态统计
export function GetEquipStatusFX(data) {
  return request({
    method: 'get',
    url: '/DF/VehiclesAnalyses/GetEquipStatus',
    params: data,
  })
}

// 获取车辆出入类型分析
export function GetPassTypeStatisticsFX(data) {
  return request({
    method: 'get',
    url: '/DF/VehiclesAnalyses/GetPassTypeStatistics',
    params: data,
  })
}

//获取车辆出入趋势
export function GetPassRecordTrendFX(data) {
  return request({
    method: 'get',
    url: '/DF/VehiclesAnalyses/GetPassRecordTrend',
    params: data,
  })
}

// 获取查看更多跳转Url
export function GetJumpUrlFX(data) {
  return request({
    method: 'get',
    url: '/DF/VehiclesAnalyses/GetJumpUrl',
    params: data,
  })
}

// 获取车辆出入统计
export function GetPassRecordStatisticsFX(data) {
  return request({
    method: 'get',
    url: '/DF/VehiclesAnalyses/GetPassRecordStatistics',
    params: data,
  })
}
