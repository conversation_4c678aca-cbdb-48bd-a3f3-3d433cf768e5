import request from '@/utils/request'
//获取 年/月/周/日 使用量统计 （共计用水）
export function GetWaterAnalyseStatistic(data) {
    return request({
        method: 'post',
        url: '/DF/PEnergyAnalyse/GetWaterAnalyseStatistic',
        data
    })
}
//获取能耗分析-全部用水
export function GetWaterArea(data) {
    return request({
        method: 'post',
        url: '/DF/PEnergyAnalyse/GetWaterArea',
        data
    })
}
//获取能耗分析-各类型用水
export function GetWaterTypeArea(data) {
    return request({
        method: 'post',
        url: '/DF/PEnergyAnalyse/GetWaterTypeArea',
        data
    })
}
//获取能耗分析-生活区用水
export function GetWaterLifeArea(data) {
    return request({
        method: 'post',
        url: '/DF/PEnergyAnalyse/GetWaterLifeArea',
        data
    })
}
//获取能耗分析-生产区用水
export function GetWaterProduceArea(data) {
    return request({
        method: 'post',
        url: '/DF/PEnergyAnalyse/GetWaterProduceArea',
        data
    })
}
//获取能耗分析-绿化用水
export function GetWaterGreenArea(data) {
    return request({
        method: 'post',
        url: '/DF/PEnergyAnalyse/GetWaterGreenArea',
        data
    })
}
//获取气体时间段内用量柱状图
export function GetGasTimePeriodDosageBarDiagram(data) {
    return request({
        method: 'post',
        url: '/df/PEnergyAnalyse/GetGasTimePeriodDosageBarDiagram',
        data
    })
}
//获取气体各个节点用量饼图
export function GetGasEachNodeDosagePieDiagram(data) {
    return request({
        method: 'post',
        url: '/df/PEnergyAnalyse/GetGasEachNodeDosagePieDiagram',
        data
    })
}
//获取气体各个节点用量树图
export function GetGasEachNodeDosageTreeDiagram(data) {
    return request({
        method: 'post',
        url: '/df/PEnergyAnalyse/GetGasEachNodeDosageTreeDiagram',
        data
    })
}
//获取对应单节点时间内用能情况
export function GetNodePowerUsage(data) {
    return request({
        method: 'post',
        url: '/DF/PEnergyAnalyse/GetNodePowerUsage',
        data
    })
}

// 剩余气体
export function GetGasLiquidPercent(data) {
  return request({
      method: 'post',
      url: '/DF/PEnergyAnalyse/GetGasLiquidPercent',
      data
  })
}

