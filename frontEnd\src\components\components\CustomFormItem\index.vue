
<template>
  <div class="CustomFormItem">
    <template v-if="config.type==='input'">
      <el-input v-if="config.inputConfig.inputLimit==='num2'" :placeholder="config.inputConfig.placeholder" :type="config.inputConfig.textarea?'textarea':''" :autosize="{ minRows: config.inputConfig.minRows, maxRows:  config.inputConfig.maxRows }" v-model="tmpvalue" :style="{width:comWidth}" :disabled="disabled"  onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" @blur="(e)=>(tmpvalue = e.target.value)"/>
      <el-input v-else-if="config.inputConfig.inputLimit==='num'"  :placeholder="config.inputConfig.placeholder" :type="config.inputConfig.textarea?'textarea':''" :autosize="{ minRows: config.inputConfig.minRows, maxRows:  config.inputConfig.maxRows }" v-model="tmpvalue" :style="{width:comWidth}" :disabled="disabled"  onkeyup="value=value.replace(/\D/g,'')" @blur="(e)=>(tmpvalue = e.target.value)"/>
      <el-input v-else-if="config.inputConfig.inputLimit==='Chinese'" :placeholder="config.inputConfig.placeholder" :type="config.inputConfig.textarea?'textarea':''" :autosize="{ minRows: config.inputConfig.minRows, maxRows:  config.inputConfig.maxRows }" v-model="tmpvalue" :style="{width:comWidth}" :disabled="disabled"  onkeyup="value=value.replace(/[^\u4e00-\u9fa5]/g,'')" @blur="(e)=>(tmpvalue = e.target.value)"/>
      <el-input v-else-if="config.inputConfig.inputLimit==='letter'" :placeholder="config.inputConfig.placeholder" :type="config.inputConfig.textarea?'textarea':''" :autosize="{ minRows: config.inputConfig.minRows, maxRows:  config.inputConfig.maxRows }" v-model="tmpvalue" :style="{width:comWidth}" :disabled="disabled"  onkeyup="value=value.replace(/[^a-zA-Z]/g,'')" @blur="(e)=>(tmpvalue = e.target.value)"/>
      <el-input v-else-if="config.inputConfig.inputLimit==='Id'" :placeholder="config.inputConfig.placeholder" :type="config.inputConfig.textarea?'textarea':''" :autosize="{ minRows: config.inputConfig.minRows, maxRows:  config.inputConfig.maxRows }" v-model="tmpvalue" :style="{width:comWidth}" :disabled="disabled"  onkeyup="value=value.replace(/^([1-9]\d{5})(\d{4})(\d{2})(\d{2})(\d{3})(\d|X)$/g, '$1')" @blur="(e)=>(tmpvalue = e.target.value)"/>
      <el-input v-else-if="config.inputConfig.inputLimit==='tel'" :placeholder="config.inputConfig.placeholder" :type="config.inputConfig.textarea?'textarea':''" :autosize="{ minRows: config.inputConfig.minRows, maxRows:  config.inputConfig.maxRows }" v-model="tmpvalue" :style="{width:comWidth}" :disabled="disabled"  onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" @blur="(e)=>(tmpvalue = e.target.value)"/>
      <el-input v-else v-model="tmpvalue" :style="{width:comWidth}" :placeholder="config.inputConfig.placeholder" :type="config.inputConfig.textarea?'textarea':''" :autosize="{ minRows: config.inputConfig.minRows, maxRows:  config.inputConfig.maxRows }" :disabled="disabled" />
    </template>
    <template v-else-if="config.type==='select'">
      <el-select v-model="tmpvalue" :placeholder="config.selectConfig.placeholder"  :multiple="config.selectConfig.multiple" :disabled="disabled" :clearable="config.selectConfig.clearable" :filterable="config.selectConfig.filterable" :style="{width:comWidth}">
        <el-option v-for="(item,index) in data" :key="'select'+index" :value="item[config.selectConfig.value||'value']" :label="item[config.selectConfig.name||'name']"/>
      </el-select>
    </template>
    <template v-else-if="config.type==='switch'">
      <el-switch  :value="!!tmpvalue" :active-color="config.switchConfig.activeColor" :disabled="disabled" :inactive-color="config.switchConfig.inactiveColor"/>
    </template>
    <template v-else-if="config.type==='date'">
      <el-date-picker
        v-model="tmpvalue"
        :type="(config.dateConfig.range?`${config.dateConfig.type}range`:config.dateConfig.type)"
        value-format="yyyy-MM-dd"
        :format="config.dateConfig.format"
        :placeholder="config.dateConfig.placeholder"
        :disabled="disabled"
        :range-separator="config.dateConfig.range?config.dateConfig.rangeSeparator:''"
        :start-placeholder="config.dateConfig.range?config.dateConfig.startPlaceholder:''"
        :end-placeholder="config.dateConfig.range?config.dateConfig.endPlaceholder:''"
        :style="{width:comWidth}"/>
    </template>
    <template v-else-if="config.type==='radio'">
      <el-radio v-for="(item,index) in data" :key="'radio'+index" v-model="tmpvalue" :disabled="disabled" :label="item[config.radioConfig.value]">{{item[config.radioConfig.label]}}</el-radio>
    </template>
    <template v-else-if="config.type==='upload'">
      <el-upload
        class="upload-demo"
        v-if="!config.uploadConfig.picture"
        drag
        action="https://jsonplaceholder.typicode.com/posts/"
        multiple>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <el-upload
        v-if="config.uploadConfig.picture"
        action="https://jsonplaceholder.typicode.com/posts/"
        list-type="picture-card"
        >
        <i class="el-icon-plus"></i>
      </el-upload>
    </template>
  </div>
</template>

<script>
export default {
  name: 'CustomFormItem',
  props: {
    type: {
      type: String,
      default: 'input'
    },
    value:{

    },
    defaultwidth:{

    },
    disabled:{

    },
    config:{

    },
    data:{
      default:()=>{
        return[{
          name:"示例1",
          lable:"示例1"
        }]
      }
    }
  },
  watch: {
    tmpvalue(val) {
      this.$emit('input', val)
    },
    value(val) {
      this.tmpvalue = val
    }
  },
  data() {
    return {
      tmpvalue:''
    }
  },
  computed: {
    comWidth(){
      return this.config.inputWidth&&this.config.inputWidthUnit?this.config.inputWidth+this.config.inputWidthUnit:this.defaultwidth
    },
  },
  created() {},

  methods: {
    reset(){
      this.tmpvalue=''
    },
    // 验证
    validate(){
      return new Promise((resolve,reject)=>{
        if(this.config.required&&!this.tmpvalue){
          reject('必填项')
        }else{
          resolve()
        }
      })
    },
    // 获取值
    getValue(){
      return this.tmpvalue
    },
    // getrules(){
    //   var res=''
    //   switch (this.config.inputConfig.inputLimit) {
    //     case 'num':
    //       res=`value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')`
    //       break;

    //     default:
    //       break;
    //   }
    //   return res
    // }
  }
}
</script>

<style lang="scss" scoped>
.CustomFormItem{

}
::v-deep .el-input.is-disabled .el-input__inner{
  color:#606266;
  cursor:default;
}
::v-deep .el-switch.is-disabled .el-switch__core{
  cursor:default;
}
::v-deep .el-radio__input.is-disabled .el-radio__inner {
  cursor:default;
}
::v-deep .el-radio__input.is-disabled+span.el-radio__label{
  cursor:default;
}
</style>
