import request from '@/utils/request'

export function GetPreferenceSettingList(data) {
  return request({
    method: 'post',
    url: '/Platform/PreferenceSetting/GetPreferenceSettingList',
    data
  })
}

export function GetPreferenceSettingPageList(data) {
  return request({
    method: 'post',
    url: '/Platform/PreferenceSetting/GetPreferenceSettingPageList',
    data
  })
}

export function GetPreferenceSettingEntity(data) {
  return request({
    method: 'post',
    url: '/Platform/PreferenceSetting/GetPreferenceSettingEntity',
    data
  })
}

export function SavePreferenceSetting(data) {
  return request({
    method: 'post',
    url: '/Platform/PreferenceSetting/SavePreferenceSetting',
    data
  })
}

export function GetPreferenceSettingValue(data) {
  return request({
    method: 'post',
    url: '/Platform/PreferenceSetting/GetPreferenceSettingValue',
    data
  })
}
