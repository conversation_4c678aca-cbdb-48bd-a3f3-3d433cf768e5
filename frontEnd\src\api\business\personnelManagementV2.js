import request from '@/utils/request'
// 查询人员列表
export function querypersonnel(data) {
  return request({
    method: 'post',
    url: '/df/personnel/v2/querypersonnel',
    data
  })
}
// 查询人员详情
export function GetPersonnelDetail(data) {
  return request({
    method: 'post',
    url: '/df/personnel/v2/GetPersonnelDetail',
    data
  })
}
// 新增人员信息
export function savepersonnel(data) {
  return request({
    method: 'post',
    url: '/df/personnel/v2/savepersonnel',
    data
  })
}
// 删除人员信息
export function DeletePersonnel(data) {
  return request({
    method: 'post',
    url: '/df/personnel/v2/DeletePersonnel',
    data
  })
}
// 导出人员为 Excel 文件
export function DownloadPersonnelsToExcel(data) {
  return request({
    method: 'post',
    url: '/df/personnel/v2/DownloadPersonnelsToExcel',
    data
  })
}
// v2 导出人员为 Excel 文件
export function ExportPersonnelList(data) {
  return request({
    method: 'post',
    url: '/DF/Personnel/v2/ExportPersonnelList',
    data
  })
}
// 下载人员导入模板
export function DownloadPersonnelsTemplate(data) {
  return request({
    method: 'post',
    url: '/df/personnel/v2/DownloadPersonnelsTemplate',
    data
  })
}
// 导入
export function ImportPersonnel(data) {
  return request({
    method: 'post',
    url: '/df/personnel/v2/ImportPersonnel',
    data
  })
}

// 获取考勤班组
export function GetClockkingInTeams(data) {
  return request({
    method: 'post',
    url: '/df/personnel/v2/GetClockkingInTeams',
    data
  })
}

// 班组分类
export function GetClockkingInTeamTypes(data) {
  return request({
    method: 'post',
    url: '/df/personnel/v2/GetClockkingInTeamTypes',
    data
  })
}
// 获取人员详情
export function getPersonnelDetailV2(data) {
  return request({
    method: 'post',
    url: '/df/personnel/v2/GetPersonnelDetail',
    data
  })
}

// 上传用户头像（zip）
export function UploadUserImage(data) {
  return request({
    method: 'post',
    url: '/DF/Personnel/v2/UploadUserImage',
    data
  })
}
