{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\store\\modules\\permission.js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\store\\modules\\permission.js", "mtime": 1754554578755}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["constantRoutes", "RoleAuthorization", "pageList", "store", "getQueryObject", "_require", "require", "name", "hasPermission", "roles", "route", "meta", "some", "role", "includes", "filterAsyncRoutes", "routes", "res", "for<PERSON>ach", "tmp", "_objectSpread", "children", "push", "state", "addRoutes", "mutations", "SET_ROUTES", "concat", "actions", "generateRoutes", "_ref", "value", "commit", "Promise", "resolve", "routers", "ModuleList", "JSON", "parse", "localStorage", "getItem", "ModuleId", "getAllAsyncRouter", "getProduceMenuList", "getAsyncRouter", "find", "item", "Id", "appoint<PERSON><PERSON><PERSON>", "getAsyncRouterMenu", "MenuList", "getRouter", "filter", "module", "ModulePlatform", "flatMap", "array", "parent", "map", "element", "index", "_element$Data", "Data", "Display_Name", "Icon", "Url_Address", "No_<PERSON><PERSON>", "Code", "Component", "Is_Line", "Children", "currentRouter", "path", "<PERSON><PERSON><PERSON>", "component", "alwaysShow", "length", "title", "icon", "noCache", "s", "Object", "keys", "query", "startsWith", "replace", "split", "namespaced"], "sources": ["D:/project/platform_framework_hlj/hljbimdigitalfactory/frontEnd/src/store/modules/permission.js"], "sourcesContent": ["import { constantRoutes } from '@/router'\nimport { RoleAuthorization } from '@/api/user'\nimport pageList from '@/router/modules'\nimport store from '@/store'\nimport { getQueryObject } from '@/utils'\nconst { name } = require('../../../package')\n\n/**\n * Use meta.role to determine if the current user has permission\n * @param roles\n * @param route\n */\nfunction hasPermission(roles, route) {\n  if (route.meta && route.meta.roles) {\n    return roles.some(role => route.meta.roles.includes(role))\n  } else {\n    return true\n  }\n}\n\n/**\n * Filter asynchronous routing tables by recursion\n * @param routes asyncRoutes\n * @param roles\n */\nexport function filterAsyncRoutes(routes, roles) {\n  const res = []\n\n  routes.forEach(route => {\n    const tmp = { ...route }\n    if (hasPermission(roles, tmp)) {\n      if (tmp.children) {\n        tmp.children = filterAsyncRoutes(tmp.children, roles)\n      }\n      res.push(tmp)\n    }\n  })\n\n  return res\n}\n\nconst state = {\n  routes: [],\n  addRoutes: []\n}\n\nconst mutations = {\n  SET_ROUTES: (state, routes) => {\n    state.addRoutes = routes\n    state.routes = constantRoutes.concat(routes)\n  }\n}\n\nconst actions = {\n  generateRoutes({ commit }, value) {\n    return new Promise(resolve => {\n      let routers = []\n      const ModuleList = JSON.parse(localStorage.getItem('ModuleList'))\n      const ModuleId = localStorage.getItem('ModuleId')\n      if (!ModuleId || !ModuleList) {\n        commit('SET_ROUTES', [])\n        resolve([])\n        return\n      }\n      const getAllAsyncRouter = getProduceMenuList(ModuleList)\n      const getAsyncRouter = ModuleList.find(item => item.Id === ModuleId)\n\n      // 处理其他子应用需要打开的菜单路由\n      let appointMenu = []\n      if (localStorage.getItem('AppointMenu') && localStorage.getItem('AppointMenu').includes(name)) {\n        appointMenu = JSON.parse(localStorage.getItem('AppointMenu'))[name]\n      }\n      if (getAsyncRouter) {\n        const getAsyncRouterMenu = getAllAsyncRouter.some(item => item.Id === getAsyncRouter.MenuList[0].Id) ? getAllAsyncRouter.concat(appointMenu) : getAllAsyncRouter.concat(appointMenu).concat(getAsyncRouter.MenuList)\n        // const getAsyncRouterMenu = getAsyncRouter.MenuList.concat(appointMenu)\n        routers = getRouter(getAsyncRouterMenu)\n        commit('SET_ROUTES', routers)\n        resolve(routers)\n      } else {\n        const getAsyncRouterMenu = getAllAsyncRouter.concat(appointMenu)\n        routers = getRouter(getAsyncRouterMenu)\n        commit('SET_ROUTES', routers)\n        resolve(routers)\n      }\n    })\n  }\n}\n\n// 获取当前子应用下的菜单列表\nfunction getProduceMenuList(ModuleList) {\n  return ModuleList\n    .filter(module => module.ModulePlatform === name)\n    .flatMap(module => module.MenuList || [])\n}\n\nfunction getRouter(array, parent) {\n  return array.map((element, index) => {\n    const { Data: { Display_Name, Icon, Url_Address, No_Cache, Code, Component, Is_Line }, Children, Id } = element\n    const currentRouter = {\n      path: getPath(Url_Address, Code),\n      name: Code,\n      component: pageList[Component],\n      alwaysShow: Children.length === 1,\n      meta: {\n        Id,\n        title: Display_Name,\n        icon: Icon,\n        Is_Line, // 菜单分割线\n        noCache: No_Cache || false\n      }\n    }\n    const s = getQueryObject(Url_Address)\n    if (Object.keys(s).length) {\n      currentRouter.query = s\n    }\n    if (!currentRouter.path.startsWith('http')) {\n      currentRouter.path = currentRouter.path.replace('//', '/')\n    }\n    if (element.Children && element.Children.length > 0) {\n      currentRouter.children = getRouter(element.Children, currentRouter)\n    }\n    return currentRouter\n  })\n}\n\nfunction getPath(path, Code) {\n  if (!path) {\n    return '/' + Code\n  } else {\n    return path.split('?')[0]\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,iBAAiB,QAAQ,YAAY;AAC9C,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,cAAc,QAAQ,SAAS;AACxC,IAAAC,QAAA,GAAiBC,OAAO,CAAC,kBAAkB,CAAC;EAApCC,IAAI,GAAAF,QAAA,CAAJE,IAAI;;AAEZ;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACF,KAAK,EAAE;IAClC,OAAOA,KAAK,CAACG,IAAI,CAAC,UAAAC,IAAI;MAAA,OAAIH,KAAK,CAACC,IAAI,CAACF,KAAK,CAACK,QAAQ,CAACD,IAAI,CAAC;IAAA,EAAC;EAC5D,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,iBAAiBA,CAACC,MAAM,EAAEP,KAAK,EAAE;EAC/C,IAAMQ,GAAG,GAAG,EAAE;EAEdD,MAAM,CAACE,OAAO,CAAC,UAAAR,KAAK,EAAI;IACtB,IAAMS,GAAG,GAAAC,aAAA,KAAQV,KAAK,CAAE;IACxB,IAAIF,aAAa,CAACC,KAAK,EAAEU,GAAG,CAAC,EAAE;MAC7B,IAAIA,GAAG,CAACE,QAAQ,EAAE;QAChBF,GAAG,CAACE,QAAQ,GAAGN,iBAAiB,CAACI,GAAG,CAACE,QAAQ,EAAEZ,KAAK,CAAC;MACvD;MACAQ,GAAG,CAACK,IAAI,CAACH,GAAG,CAAC;IACf;EACF,CAAC,CAAC;EAEF,OAAOF,GAAG;AACZ;AAEA,IAAMM,KAAK,GAAG;EACZP,MAAM,EAAE,EAAE;EACVQ,SAAS,EAAE;AACb,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,UAAU,EAAE,SAAZA,UAAUA,CAAGH,KAAK,EAAEP,MAAM,EAAK;IAC7BO,KAAK,CAACC,SAAS,GAAGR,MAAM;IACxBO,KAAK,CAACP,MAAM,GAAGhB,cAAc,CAAC2B,MAAM,CAACX,MAAM,CAAC;EAC9C;AACF,CAAC;AAED,IAAMY,OAAO,GAAG;EACdC,cAAc,WAAdA,cAAcA,CAAAC,IAAA,EAAaC,KAAK,EAAE;IAAA,IAAjBC,MAAM,GAAAF,IAAA,CAANE,MAAM;IACrB,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5B,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;MACjE,IAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MACjD,IAAI,CAACC,QAAQ,IAAI,CAACL,UAAU,EAAE;QAC5BJ,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC;QACxBE,OAAO,CAAC,EAAE,CAAC;QACX;MACF;MACA,IAAMQ,iBAAiB,GAAGC,kBAAkB,CAACP,UAAU,CAAC;MACxD,IAAMQ,cAAc,GAAGR,UAAU,CAACS,IAAI,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACC,EAAE,KAAKN,QAAQ;MAAA,EAAC;;MAEpE;MACA,IAAIO,WAAW,GAAG,EAAE;MACpB,IAAIT,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC1B,QAAQ,CAACP,IAAI,CAAC,EAAE;QAC7FyC,WAAW,GAAGX,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC,CAACjC,IAAI,CAAC;MACrE;MACA,IAAIqC,cAAc,EAAE;QAClB,IAAMK,kBAAkB,GAAGP,iBAAiB,CAAC9B,IAAI,CAAC,UAAAkC,IAAI;UAAA,OAAIA,IAAI,CAACC,EAAE,KAAKH,cAAc,CAACM,QAAQ,CAAC,CAAC,CAAC,CAACH,EAAE;QAAA,EAAC,GAAGL,iBAAiB,CAACf,MAAM,CAACqB,WAAW,CAAC,GAAGN,iBAAiB,CAACf,MAAM,CAACqB,WAAW,CAAC,CAACrB,MAAM,CAACiB,cAAc,CAACM,QAAQ,CAAC;QACpN;QACAf,OAAO,GAAGgB,SAAS,CAACF,kBAAkB,CAAC;QACvCjB,MAAM,CAAC,YAAY,EAAEG,OAAO,CAAC;QAC7BD,OAAO,CAACC,OAAO,CAAC;MAClB,CAAC,MAAM;QACL,IAAMc,mBAAkB,GAAGP,iBAAiB,CAACf,MAAM,CAACqB,WAAW,CAAC;QAChEb,OAAO,GAAGgB,SAAS,CAACF,mBAAkB,CAAC;QACvCjB,MAAM,CAAC,YAAY,EAAEG,OAAO,CAAC;QAC7BD,OAAO,CAACC,OAAO,CAAC;MAClB;IACF,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,SAASQ,kBAAkBA,CAACP,UAAU,EAAE;EACtC,OAAOA,UAAU,CACdgB,MAAM,CAAC,UAAAC,MAAM;IAAA,OAAIA,MAAM,CAACC,cAAc,KAAK/C,IAAI;EAAA,EAAC,CAChDgD,OAAO,CAAC,UAAAF,MAAM;IAAA,OAAIA,MAAM,CAACH,QAAQ,IAAI,EAAE;EAAA,EAAC;AAC7C;AAEA,SAASC,SAASA,CAACK,KAAK,EAAEC,MAAM,EAAE;EAChC,OAAOD,KAAK,CAACE,GAAG,CAAC,UAACC,OAAO,EAAEC,KAAK,EAAK;IACnC,IAAAC,aAAA,GAAwGF,OAAO,CAAvGG,IAAI;MAAIC,YAAY,GAAAF,aAAA,CAAZE,YAAY;MAAEC,IAAI,GAAAH,aAAA,CAAJG,IAAI;MAAEC,WAAW,GAAAJ,aAAA,CAAXI,WAAW;MAAEC,QAAQ,GAAAL,aAAA,CAARK,QAAQ;MAAEC,IAAI,GAAAN,aAAA,CAAJM,IAAI;MAAEC,SAAS,GAAAP,aAAA,CAATO,SAAS;MAAEC,OAAO,GAAAR,aAAA,CAAPQ,OAAO;MAAIC,QAAQ,GAASX,OAAO,CAAxBW,QAAQ;MAAEvB,EAAE,GAAKY,OAAO,CAAdZ,EAAE;IACnG,IAAMwB,aAAa,GAAG;MACpBC,IAAI,EAAEC,OAAO,CAACR,WAAW,EAAEE,IAAI,CAAC;MAChC5D,IAAI,EAAE4D,IAAI;MACVO,SAAS,EAAExE,QAAQ,CAACkE,SAAS,CAAC;MAC9BO,UAAU,EAAEL,QAAQ,CAACM,MAAM,KAAK,CAAC;MACjCjE,IAAI,EAAE;QACJoC,EAAE,EAAFA,EAAE;QACF8B,KAAK,EAAEd,YAAY;QACnBe,IAAI,EAAEd,IAAI;QACVK,OAAO,EAAPA,OAAO;QAAE;QACTU,OAAO,EAAEb,QAAQ,IAAI;MACvB;IACF,CAAC;IACD,IAAMc,CAAC,GAAG5E,cAAc,CAAC6D,WAAW,CAAC;IACrC,IAAIgB,MAAM,CAACC,IAAI,CAACF,CAAC,CAAC,CAACJ,MAAM,EAAE;MACzBL,aAAa,CAACY,KAAK,GAAGH,CAAC;IACzB;IACA,IAAI,CAACT,aAAa,CAACC,IAAI,CAACY,UAAU,CAAC,MAAM,CAAC,EAAE;MAC1Cb,aAAa,CAACC,IAAI,GAAGD,aAAa,CAACC,IAAI,CAACa,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC5D;IACA,IAAI1B,OAAO,CAACW,QAAQ,IAAIX,OAAO,CAACW,QAAQ,CAACM,MAAM,GAAG,CAAC,EAAE;MACnDL,aAAa,CAAClD,QAAQ,GAAG8B,SAAS,CAACQ,OAAO,CAACW,QAAQ,EAAEC,aAAa,CAAC;IACrE;IACA,OAAOA,aAAa;EACtB,CAAC,CAAC;AACJ;AAEA,SAASE,OAAOA,CAACD,IAAI,EAAEL,IAAI,EAAE;EAC3B,IAAI,CAACK,IAAI,EAAE;IACT,OAAO,GAAG,GAAGL,IAAI;EACnB,CAAC,MAAM;IACL,OAAOK,IAAI,CAACc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3B;AACF;AAEA,eAAe;EACbC,UAAU,EAAE,IAAI;EAChBhE,KAAK,EAALA,KAAK;EACLE,SAAS,EAATA,SAAS;EACTG,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}