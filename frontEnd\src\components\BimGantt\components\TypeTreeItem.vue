<template>
  <div>
    <template v-for="item in data">
      <template v-if="item.Children && item.Children.length > 0">
        <el-submenu :key="'c-' + item.Id" :index="item.Id">
          <template slot="title">
            <span>{{ item.Label }}</span>
          </template>
          <TypeTreeItem :key="'c-' + item.Id" :data="item.Children" />
        </el-submenu>
      </template>
      <template v-else>
        <el-menu-item :key="'c-' + item.Id" :index="item.Id">
          <span>{{ item.Label }}</span>
        </el-menu-item>
      </template>
    </template>
  </div>
</template>
<script>
export default {
  name: 'TypeTreeItem',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  created() {
    console.log(this.data)
  }
}
</script>
