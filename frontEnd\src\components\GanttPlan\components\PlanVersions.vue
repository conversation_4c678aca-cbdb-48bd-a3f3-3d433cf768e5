<template>
  <ul class="history-list">
    <li
      v-for="(h, i) in historys"
      :key="i"
    >
      <span>{{ i + 1 }}</span> {{ h.Name }}
    </li>
  </ul>
</template>
<script>
export default {
  name:'PlanVersions',
  props:{
    historys:{
      type:Array,
      default:()=>[]
    }
  }
}
</script>
<style lang="scss" scoped>
ul.history-list {
  list-style: none;
  padding: 0;
  margin: 10px 20px;
  > li {
    list-style: none;
    display: block;
    padding: 8px 0;
    margin: 6px 0;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #fbfbfb;
    font-size: 14px;
    color: #888;
    cursor: pointer;
    > span {
      font-style: italic;
      font-weight: bold;
      color: #298dff;
      padding: 0 10px;
    }
    &::after {
      content: '>';
      float: right;
      margin-right: 12px;
    }
  }
}
</style>
