<template>
  <div :class="[prefixCls, { fullscreen }]">
    <Upload
      name="file"
      multiple
      :customRequest="customUpload"
      @change="handleChange"
      :showUploadList="false"
      accept=".jpg,.jpeg,.gif,.png,.webp"
    >
      <a-button type="primary" v-bind="{ ...getButtonProps }">
        {{ t('component.upload.imgUpload') }}
      </a-button>
    </Upload>
  </div>
</template>
<script lang="ts">
import { defineComponent, computed } from 'vue';

import { Upload } from 'ant-design-vue';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGlobSetting } from '/@/hooks/setting';
import { useI18n } from '/@/hooks/web/useI18n';
import { aliYunImgUpload } from '/@/api/business/upload';
export default defineComponent({
  name: 'TinymceImageUpload',
  components: { Upload },
  props: {
    fullscreen: {
      type: <PERSON><PERSON><PERSON>,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['uploading', 'done', 'error'],
  setup(props, { emit }) {
    let uploading = false;

    const { uploadUrl } = useGlobSetting();
    const { t } = useI18n();
    const { prefixCls } = useDesign('tinymce-img-upload');

    const getButtonProps = computed(() => {
      const { disabled } = props;
      return {
        disabled,
      };
    });

    const customUpload = (e) => {
      console.log(e.file.name, 'ee');
      // uploadApi 你的二次封装上传接口
      emit('uploading', e.file.name);
      uploading = true;
      aliYunImgUpload({
        file: e.file,
      })
        .then((res: any) => {
          const { data } = res;
          console.log('上传成功', res.data);
          // if (status === 'uploading') {
          //   if (!uploading) {
          //     emit('uploading', name);
          //     uploading = true;
          //   }
          // } else if (status === 200) {
          //   emit('done', data.data, data.data);
          //   uploading = false;
          // } else if (status === 'error') {
          //   emit('error');
          //   uploading = false;
          // }
          emit('done', e.file.name, data.data);
          uploading = false;
          // 调用实例的成功方法通知组件该文件上传成功
        })
        .catch((err) => {
          // 调用实例的失败方法通知组件该文件上传失败
          e.onError(err);
        });
    };

    // function handleChange(info: Recordable) {
    //   const file = info.file;
    //   const status = file?.status;
    //   const url = file?.response?.url;
    //   const name = file?.name;

    //   if (status === 'uploading') {
    //     if (!uploading) {
    //       emit('uploading', name);
    //       uploading = true;
    //     }
    //   } else if (status === 'done') {
    //     emit('done', name, url);
    //     uploading = false;
    //   } else if (status === 'error') {
    //     emit('error');
    //     uploading = false;
    //   }
    // }
    function handleChange() {
      // fileList.value = newFileList;
      // console.log(newFileList, 'newFileList');
    }

    return {
      prefixCls,
      handleChange,
      uploadUrl,
      t,
      getButtonProps,
      customUpload,
    };
  },
});
</script>
<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-tinymce-img-upload';

.@{prefix-cls} {
  position: absolute;
  top: 4px;
  right: 10px;
  z-index: 20;

  &.fullscreen {
    position: fixed;
    z-index: 10000;
  }
}
</style>
