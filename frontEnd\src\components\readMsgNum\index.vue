<template>
  <div class="msg-info" @click.stop="handleClick">
    <el-badge v-if="sysReadMsgNum" :value="sysReadMsgNum">
      <svg-icon icon-class="sysmsg" />
    </el-badge>
    <svg-icon v-else icon-class="sysmsg" />
  </div>
</template>

<script>
import { GetNoticeCount } from '@/api/sys/message'
import { mapGetters } from 'vuex'
export default {
  data() {
    return {
      unreadNum: undefined
    }
  },
  computed: {
    ...mapGetters([
      'sysReadMsgNum'
    ])
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      GetNoticeCount().then(res => {
        this.$store.dispatch('sysInfo/changeReadNNum', res.Data)
      })
    },
    handleClick() {
      if (this.$route.name !== 'PublicSysMessage') {
        this.$router.push({ name: 'PublicSysMessage' })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.msg-info {
  .el-badge {
    ::v-deep {
      .el-badge__content.is-fixed {
        top: 10px;
      }
    }
  }
}
</style>
