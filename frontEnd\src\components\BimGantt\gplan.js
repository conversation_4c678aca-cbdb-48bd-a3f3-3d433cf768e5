import * as moment from 'moment'
import { parseServeTask } from './task'
import { calc } from './calc'
/**
 * plan 属性及默认值的key-value
 */
export const PLAN_FIELDS = {
  Id: '',
  Code: '',
  Plan_Auth: '负责',
  Name: '新建计划',
  Type_Id: '0', // 分类Id
  Is_Main_Plan: false, // 是否主计划
  Is_Target_Plan: false, // 是否目标计划
  Is_Allow_Collaboration: false, // 是否允许协同编制
  Dynamic_Start_Date: moment(new Date())
    .startOf('date')
    .format('YYYY-MM-DD'),
  Dynamic_End_Date: undefined,
  Dynamic_Duration: 0,
  Percent_Complete: 0,
  Plan_Start_Date: moment(new Date())
    .startOf('date')
    .toDate(),
  Plan_End_Date: undefined,
  Plan_Duration: 0,
  Actual_Start_Date: undefined,
  Actual_End_Date: undefined,
  Actual_Duration: 0,
  Resources: '1', // 资源显示方式
  Plan_Oauth: '0', // 0 私密 1 公开
  Admin: [],
  Observer: [],
  Responsible_User: '',
  Target_Plan_Id: '',
  Plan_Calendar: JSON.parse(
    '{"week_calendar":[1,1,1,1,1,1,1], "out_calendar":[]}'
  ),
  Cur_Data_Date: moment(new Date())
    .startOf('date')
    .toDate(),
  Remark: '',
  Plan_Data: JSON.parse('{"data":[], "links":[]}')
}
export function createDefaultCalendar() {
  return JSON.parse('{"week_calendar":[1,1,1,1,1,1,1], "out_calendar":[]}')
}
export function createEmptePlanData() {
  return JSON.parse('{"data":[], "links":[]}')
}
export function createEmptyPlan(type_id = '0') {
  const p = {}
  for (const k in PLAN_FIELDS) {
    p[k] = PLAN_FIELDS[k]
  }
  return p
}
/**
 * 更新计划的属性
 * @param {*} plan
 * @param {*} field
 * @param {*} value
 */
export function updatePlanField(plan, field, value) {
  if (field == 'Plan_Start_Date' || field == 'Plan_End_Date') {
    return updatePlanPlanDate(plan, field, value)
  }
  plan[field] = value
}
function updatePlanPlanDate(plan, field, value) {
  plan[field] = moment(value)
    .startOf('date')
    .toDate()
  if (plan.Plan_Start_Date && plan.Plan_End_Date) {
    plan.Plan_Duration = diffDays(plan.Plan_Start_Date, plan.Plan_End_Date)
  } else {
    plan.Plan_Duration = 0
  }
}
function diffDays(d1, d2) {
  const ds = [moment(d1).format('x'), moment(d2).format('x')]
  ds.sort()
  return moment(parseInt(ds[1])).diff(parseInt(ds[0]), 'days') + 1
}
export function calcPlanDurations(plan) {
  if (plan.Plan_Start_Date && plan.Plan_End_Date) {
    plan.Plan_Duration = diffDays(plan.Plan_Start_Date, plan.Plan_End_Date)
  } else {
    plan.Plan_Duration = 0
  }
}
/**
 * 更新计划日历
 * @param {*} plan
 * @param {*} gantt
 * @param {*} calendar
 */
export function updateCalendar(plan, gantt, calendar) {
  plan.Plan_Calendar = calendar
  calendar.week_calendar.forEach((v, i) => {
    const day = i
    const hours = v === 0 ? false : ['0:00-23:59']
    gantt.setWorkTime({ day: day, hours: hours })
  })
  calendar.out_calendar.forEach(ex => {
    const hours = ex.type === 0 ? false : ['0:00-23:59']
    const dates = getDatesRange(ex)
    dates.forEach(d => {
      gantt.setWorkTime({
        date: moment(d)
          .startOf('date')
          .toDate(),
        hours: hours
      })
    })
  })

  gantt.render()
}
export function getDatesRange({ start_date, end_date }) {
  const dates = []
  const deltaDays = moment(end_date).diff(start_date, 'days')
  for (let i = 0; i <= deltaDays; i++) {
    const date = moment(start_date)
      .add(i, 'days')
      .format('YYYY-MM-DD')
    dates.push(date)
  }
  return dates
}

const DATE_FIELDS = [
  'Actual_End_Date',
  'Actual_Start_Date',
  'Cur_Data_Date',
  'Plan_End_Date',
  'Plan_Start_Date'
]
export function parseServerPlanEntity(odata) {
  const p = {}
  Object.keys(odata).forEach(k => {
    if (DATE_FIELDS.indexOf(k) > -1) {
      p[k] = odata[k]
        ? moment(odata[k])
          .startOf('date')
          .toDate()
        : null
    } else {
      p[k] = odata[k]
    }
  })
  if (!p['Plan_Calendar']) {
    try {
      p['Plan_Calendar'] = JSON.parse(p['Calendar']) ?? createDefaultCalendar()
    } catch (err) {
      p['Plan_Calendar'] = createDefaultCalendar()
    }
  }
  if (p['Observer'] && p['Observer'].indexOf('[') > -1) {
    try {
      p['Observer'] = JSON.parse(p['Observer']) ?? []
    } catch (err) {
      p['Observer'] = []
    }
  } else {
    p['Observer'] = p['Observer']?.split(',') ?? []
  }
  if (p['Admin'] && p['Admin'].indexOf('[') > -1) {
    try {
      p['Admin'] = JSON.parse(p['Admin']) ?? []
    } catch (err) {
      p['Admin'] = []
    }
  } else {
    p['Admin'] = p['Admin']?.split(',') ?? []
  }
  p['Plan_Data'].data.forEach((d, i) => {
    p['Plan_Data'].data[i] = parseServeTask(d)
  })

  return p
}
/**
 * 计算进度
 * @param {*} gantt
 * @param {*} plan
 */
export function compute(gantt, plan) {
  return new Promise((resolve, reject) => {
    if (!plan.Cur_Data_Date) return reject('计划状态日期不能为空')
    const tasks = gantt.getTaskByTime()
    const links = gantt.getLinks()
    schedule(plan, tasks, links, gantt)
      .then(() => {
        resolve({ plan, tasks })
      })
      .catch(e => {
        // throw e
        reject(e)
      })
  })
}
const ComputedState = {
  /**
   * 初始状态
   */
  INIT: 0,
  /**
   * 计算完成
   */
  READY: 1
}
function schedule(plan, tasks, links, gantt) {
  return calc(gantt, plan, tasks, links)
    .then(res => {
      console.log(res)
    })
    .catch(e => {
      console.log('error:', e)
      throw e
    })
}
// pureTasks 表示没有实际开始/没有前置关系/限制条件除越晚越好之外
function computePureTask(task, data_date, gantt) {
  if (task.constraint_type === 'asap') {
    // -- 越早越好，最早开始为数据日期
    task.Plan_Start_Date = data_date
  } else if (task.constraint_type === 'mso') {
    // -- 必须开始于
    if (moment(data_date).isAfter(task.constraint_date, 'day')) {
      // 数据日期晚于限制日期，最早开始为数据日期
      task.Plan_Start_Date = data_date
    } else {
      // 除外最早开始为限制日期
      task.Plan_Start_Date = task.constraint_date
    }
  } else if (task.constraint_type === 'mfo') {
    // -- 必须结束于
    const _start = gantt.calculateEndDate({
      start_date: task.constraint_date,
      duration: -task.Plan_Duration + 1
    })
    if (moment(data_date).isAfter(_start, 'day')) {
      // 数据日期晚于根据结束日期计算的最早开始，最早开始为数据日期
      task.Plan_Start_Date = data_date
    } else {
      // 除外最早开始为根据结束日期计算的最早开始
      task.Plan_Start_Date = _start
    }
  } else if (task.constraint_type === 'snet') {
    // -- 不得早于...开始
    if (moment(data_date).isAfter(task.constraint_date, 'day')) {
      // 数据日期晚于限制日期，最早开始为数据日期
      task.Plan_Start_Date = data_date
    } else {
      // 除外最早开始为限制日期
      task.Plan_Start_Date = task.constraint_date
    }
  } else if (task.constraint_type === 'snlt') {
    // -- 不得晚于...开始
    // 理论未开始的作业最早开始就是数据日期
    task.Plan_Start_Date = data_date
  } else if (task.constraint_type === 'fnet') {
    // -- 不得早于...结束
    const _start = gantt.calculateEndDate({
      start_date: task.constraint_date,
      duration: -task.Plan_Duration + 1
    })
    if (moment(data_date).isAfter(_start, 'day')) {
      // 数据日期晚于根据结束日期计算的最早开始，最早开始为数据日期
      task.Plan_Start_Date = data_date
    } else {
      // 除外最早开始为根据结束日期计算的最早开始
      task.Plan_Start_Date = _start
    }
  } else if (task.constraint_type === 'fnlt') {
    // -- 不得晚于...结束
    // 理论未开始的作业最早开始就是数据日期
    task.Plan_Start_Date = data_date
  }

  task.Needed_Start_Date = task.Plan_Start_Date
  task.Dynamic_Duration = self.Needed_Duration = task.Plan_Duration
  task.start_date = task.Plan_Start_Date
  task.duration = task.Plan_Duration
  task.end_date = gantt.calculateEndDate({
    start_date: task.start_date,
    duration: task.Plan_Duration
  })
  task.Plan_End_Date = task.Needed_End_Date = moment(task.end_date)
    .add(-1, 'days')
    .startOf('date')
    .toDate()
  task.Dynamic_Start_Date = moment(task.Plan_Start_Date).format('YYYY-MM-DD')
  task.Dynamic_End_Date = moment(task.Plan_End_Date).format('YYYY-MM-DD')
  // 标记处理状态
  task.__FLAG = ComputedState.READY
}
// pureActualTasks 表示实际已经开始/没有前置关系/限制条件除越晚越好之外
function computePureActualTask(task, data_date, gantt) {
  // return
  task.start_date = task.Actual_Start_Date
  task.Dynamic_Start_Date = moment(task.Actual_Start_Date).format(
    'YYYY-MM-DD[A]'
  )
  if (!task.Actual_End_Date) {
    task.Actual_Duration = gantt.calculateDuration({
      start_date: task.Actual_Start_Date,
      end_date: moment(data_date).toDate()
    })
    if (task.Actual_Duration < 0) {
      task.Actual_Duration = 0
    } else {
      task.Actual_Duration += 1
    }
  } else {
    task.Actual_Duration =
      gantt.calculateDuration({
        start_date: task.Actual_Start_Date,
        end_date: task.Actual_End_Date
      }) + 1
  }
  task.Needed_Duration = Math.ceil(
    task.Plan_Duration * (1 - task.Actual_Progress)
  )
  task.Dynamic_Duration = task.Actual_Duration + task.Needed_Duration
  task.Needed_Start_Date = moment(data_date).toDate()
  task.Needed_End_Date = gantt.calculateEndDate({
    start_date: data_date,
    duration: task.Needed_Duration
  })
  task.end_date = moment(task.Needed_End_Date)
    .add(1, 'days')
    .startOf('date')
    .toDate()
  task.Dynamic_End_Date = moment(task.Needed_End_Date).format('YYYY-MM-DD')
  task.duration = task.Dynamic_Duration =
    task.Actual_Duration + task.Needed_Duration
  task.progress = task.Actual_Duration / task.duration
}
// predPureTasks 没有实际开始/有前置关系/限制条件越晚越好之外
// FS -0 , SS-1, FF-2, SF-3
function computePredPureTasks(task, data_date, links, gantt) {
  console.log(task)
  if (task.__FLAG === ComputedState.READY) return
  const pres = links.filter(l => l.target === task.id).map(l => l.source)
  const preTasks = pres.map(p => {
    return gantt.getTask(p)
  })
  preTasks.forEach(t => {
    computePredPureTasks(t, data_date, links, gantt)
  })
  if (preTasks.length <= 0) return // 没有前置作业的，在别的流程处理
  // 处理作业
  if (task.constraint_type === 'asap') {
    // -- 越早越好
  }
}
function getLineTasks(tid, links) {
  let ls = []
  const prel = links.find(l => l.target === tid)
  if (prel) {
    ls = [prel.source].concat(ls)
    ls = getLineTasks(prel.source, links).concat(ls)
  }
  return ls
}

/**
 * 原计算改造
 */
function calcGanttDatas(gantt, plan, tasks, links) {
  return new Promise((resolve, reject) => {
    const result = {}
    let lastActualStartDate = null // 实际开工最晚的作业开工日期
    let lastActualEndDate = null // 实际结束最晚的作业时间
    const taskAndMilestone = [] // 任务及里程碑数组
    const wbsArr = [] // WBS数组
    tasks.forEach(t => {
      if (result.Dynamic_Start_Date == undefined) {
        result.Dynamic_Start_Date = t.Plan_Start_Date
      } else if (result.Dynamic_Start_Date > t.Plan_Start_Date) {
        result.Dynamic_Start_Date = t.Plan_Start_Date
      }
      if (result.Dynamic_End_Date == undefined) {
        result.Dynamic_End_Date = t.Plan_End_Date
      } else if (result.Dynamic_End_Date < t.Plan_End_Date) {
        result.Dynamic_End_Date = t.Plan_End_Date
      }
      t['iscriticalPath'] = 0
      t['isCalcforward'] = false
      t['isCalcbackward'] = false

      // 检测实际进行的作业的最晚的 开始/结束 日期
      if (t.Actual_Start_Date) {
        lastActualStartDate = Math.max(
          lastActualStartDate || 0,
          t.Actual_Start_Date
        )
      }
      if (t.Actual_End_Date) {
        lastActualEndDate = Math.max(lastActualEndDate || 0, t.Actual_End_Date)
      }
      // 按类型推入数组
      if (['task', 'milestone'].indexOf(t.type) > -1) {
        taskAndMilestone.push(t)
      }
      if (t.type === 'project') {
        wbsArr.push(t)
      }
    })
    // 状态日期合法性验证
    if (lastActualStartDate) {
      lastActualStartDate = moment(lastActualStartDate).toDate()
    }
    if (lastActualEndDate) {
      lastActualEndDate = moment(lastActualEndDate).toDate()
    }
    if (
      moment(lastActualStartDate).isAfter(plan.Cur_Data_Date) ||
      moment(lastActualEndDate).isAfter(plan.Cur_Data_Date)
    ) {
      return reject('状态日期后已经有实际开始的作业，无法计算')
    }

    // 连接有效性
    for (var i = 0; i < links.length; i++) {
      const l = links[i]
      // 任务类型及任务存在验证
      var sourcetask = tasks.find(item => item.id == l.source)
      var targettask = tasks.find(item => item.id == l.target)
      if (
        !sourcetask ||
        !targettask ||
        sourcetask.type == 'project' ||
        targettask.type == 'project'
      ) {
        return reject('连接的作业存在异常，无法计算')
        break
      }
      // 圆形验证
      const isCircleChecked = checkCircle([], l, links)
      if (!isCircleChecked) {
        return reject('存在圆形连接，无法计算')
        break
      }
    }
    // 前推计算
    taskAndMilestone.forEach(t => {
      if (t.isCalcbackward) return
      calcTaskforward(t, links, tasks, plan, gantt)
    })

    // 后推计算

    // ---
    console.log(result)
  })
}

/**
 * 循环查找关系路径,判断是否形成圆形
 * @param {*} link 连接 source
 * @param {*} links
 * @returns
 */
function checkCircle(paths, link, links) {
  let checked = true
  paths.push(link.source)
  paths.push(link.target)
  const nexts = links.filter(l => l.source == link.target)
  for (let i = 0; i < nexts.length; i++) {
    if (paths.indexOf(nexts[i].target) > -1) {
      checked = false
      break
    }
    checked = checkCircle(paths, nexts[i], links)
  }
  return checked
}
function calcTaskforward(_this, links, tasks, plan, gantt) {
  // 查找具有前置节点的连接
  const preLinks = links.filter(l => l.target == _this.id)
  if (preLinks.length <= 0) {
    // 无前置节点
    _this.start_date = plan.Cur_Data_Date
  } else {
    // 有前置节点
    preLinks.forEach(l => {
      const source = tasks.find(t => t.id == l.source)
      // 先计算前置节点
      calcTaskforward(source, links, tasks, plan, gantt)

      // 根据前置节点关系计算
      console.log(_this)
      if (source.type !== 'milestone') {
        if (l.type == '0') {
          // FS
          _this.start_date = ganttAddDate(
            source.Early_End_Date,
            l.lag || 0 + 1,
            gantt
          )
        } else if (l.type == '1') {
          // SS
          _this.start_date = ganttAddDate(
            source.Early_Start_Date,
            l.lag || 0,
            gantt
          )
        } else if (l.type == '2') {
          // FF
          _this.end_date = ganttAddDate(
            source.Early_End_Date,
            l.lag || 0,
            gantt
          )
          _this.start_date = ganttAddDate(
            _this.end_date,
            -1 * _this.duration,
            gantt
          )
        } else if (l.type == '3') {
          // SF
          _this.end_date = ganttAddDate(
            source.Early_Start_Date,
            l.lag || 0,
            gantt
          )
          _this.start_date = ganttAddDate(
            _this.end_date,
            -1 * _this.duration,
            gantt
          )
        }
      } else {
        if (l.type == '0') {
          _this.start_date = ganttAddDate(
            source.Early_Start_Date,
            l.lag || 0,
            gantt
          )
        } else if (l.type == '1') {
          _this.start_date = ganttAddDate(
            source.Early_Start_Date,
            l.lag || 0,
            gantt
          )
        } else if (l.type == '2') {
          _this.end_date = ganttAddDate(
            source.Early_Start_Date,
            l.lag || 0,
            gantt
          )
          _this.start_date = _this.end_date
        } else if (l.type == '3') {
          _this.end_date = ganttAddDate(
            source.Early_Start_Date,
            l.lag || 0,
            gantt
          )
          _this.start_date = _this.end_date
        }
      }
    })
  }
  // 开始实际判断
  if (_this.Actual_Start_Date) {
    // 实际已开始，根据实际计算
    _this.start_date = _this.Actual_Start_Date
    _this.Plan_Start_Date = _this.start_date
    // _this.
  } else {
    // 未实际开始，根据限制条件计算
    switch (_this.constraint_type) {
      case 'alap':
        break
      case 'mso':
        break
      case 'mfo':
        break
      case 'snet':
        break
      case 'snlt':
        break
      case 'fnet':
        break
      case 'fnlt':
        break
      case 'asap':
      default:
        // -- 之前过程设置的开始日期就是最早日期
        break
    }
  }
  _this.Early_Start_Date = _this.start_date
  if (_this.type === 'milestone') {
    _this.Early_End_Date = _this.Early_Start_Date
  } else {
    _this.Early_End_Date = ganttAddDate(
      _this.Early_Start_Date,
      _this.duration,
      gantt
    )
  }
  _this.end_date = _this.Early_End_Date
  // 前推计算标记完成
  _this.isCalcbackward = true
}

function ganttAddDate(start_date, duration, gantt) {
  return gantt.calculateEndDate({
    start_date: start_date,
    duration: Number(duration) || 0
  })
}
