<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60" height="60" viewBox="0 0 60 60">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        fill: rgba(41,141,255,0.16);
      }

      .cls-3 {
        clip-path: url(#clip-path);
      }

      .cls-4 {
        fill: #298dff;
      }
    </style>
    <clipPath id="clip-path">
      <rect id="矩形_2553" data-name="矩形 2553" class="cls-1" width="32" height="32"/>
    </clipPath>
  </defs>
  <g id="组_8046" data-name="组 8046" transform="translate(-1421 -135)">
    <rect id="矩形_3004" data-name="矩形 3004" class="cls-2" width="60" height="60" rx="20" transform="translate(1421 135)"/>
    <g id="file-question-filled" transform="translate(1435 149)">
      <g id="file-question-filled-2" data-name="file-question-filled" class="cls-3">
        <path id="路径_4823" data-name="路径 4823" class="cls-4" d="M27,4H24V3a1,1,0,0,0-1-1H9A1,1,0,0,0,8,3V4H5A1,1,0,0,0,4,5V29a1,1,0,0,0,1,1H27a1,1,0,0,0,1-1V5A1,1,0,0,0,27,4ZM17.08,25.63H14.85V23.4h2.23Zm1.38-7.16c-1.13,1-1.42,1.39-1.42,2.63v.4H14.86v-.65a3.51,3.51,0,0,1,1.33-3l.94-.89a2.63,2.63,0,0,0,1-2A2,2,0,0,0,16,13a2.25,2.25,0,0,0-2.25,2.16L11.5,15a4.48,4.48,0,0,1,4.7-4c2.14,0,4.3,1.2,4.3,3.88C20.5,16.61,19.73,17.37,18.46,18.47ZM22,6H10V4H22Z"/>
      </g>
    </g>
  </g>
</svg>
