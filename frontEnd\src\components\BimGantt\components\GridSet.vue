<template>
  <div
    style="flex-direction:column; align-items:center;position:relative;"
  >
    <el-button size="mini" id="restore" @click="restoreDefault">恢复默认</el-button>
    <TreeTransfer
      mode="transferSort"
      :check-strictly="false"
      :up-down-disable="true"
      :from_data="all"
      :to_data="selected"
      :all_data="oall"
      :title="['可用的选项', '已选的选项']"
      height="540px"
      pid="ParentNodes"
      :default-props="{ label: 'Label', children: 'Children' }"
      filter
      open-all
      showbutton
      @cancelDialog="cancel"
      @submitDialog="submit"
    />
    <el-transfer
      v-if="false"
      v-model="selected"
      :titles="['可用栏位', '已选栏位']"
      style="margin:0 auto;"
      :data="allColumns"
    >
      <div slot="right-footer" style="text-align:center;padding-top:4px;">
        <el-button
          size="middle"
          type="warning"
          @click="restoreDefault"
        >恢复默认</el-button>
      </div>
    </el-transfer>
    <!-- <div style="text-align:center;padding-top:24px;">
      <el-button type="success" @click="submit">确认</el-button>
      <el-button @click="$emit('dialogCancel')">取消</el-button>
    </div> -->
  </div>
</template>
<script>
import * as BGT from '../index'
import TreeTransfer from '@/components/tree-transfer'
export default {
  name: 'GridSet',
  components: {
    TreeTransfer
  },
  props: {
    origin: {
      // 用所已选原始
      type: Array,
      default: () => []
    },
    all: {
      // 所有可用列
      type: Array,
      default: () => []
    },
    oall: {
      // 接口原all
      type: Array,
      default: () => []
    },
    gantt: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      selected: []
    }
  },
  computed: {
    allColumns() {
      return [{ label: '标识', key: '', disabled: true }].concat(
        BGT.ALL_TASK_COLUMNS.map(c => {
          return {
            label: c.label,
            key: c.name,
            disabled: ['text'].indexOf(c.name) > -1
          }
        })
      )
    }
  },
  created() {
    this.selected = this.origin
  },
  methods: {
    restoreDefault() {
      //console.log('111')
      this.$emit('restoreDefault')
      //this.dialogVisible = false
    },
    cancel() {
      this.$emit('dialogCancel')
    },
    submit(data) {
      //console.log(1, this.toData, this.selected)
      this.selected = data
      // return
      this.$emit('dialogFormSubmitSuccess', {
        type: 'setGanttColumns',
        data: this.selected
      })
    },
    // 添加按钮
    add(fromData, toData, obj) {
      //console.log(fromData, obj)
      this.selected = toData
      //console.log(1, this.selected)
    },
    // 移除按钮
    remove(fromData, toData, obj) {
      //console.log(fromData, obj)
      this.selected = toData
      //console.log(2, this.selected)
    }
  }
}
</script>
<style scoped>
#restore{
  position: absolute;
  right: 0;
  top: -30px;
  z-index: 9999;
}
</style>
