{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\components\\gasUsed.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\components\\gasUsed.vue", "mtime": 1754560054588}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfaGxqL2hsamJpbWRpZ2l0YWxmYWN0b3J5L2Zyb250RW5kL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7fSwKICBwcm9wczogewogICAgY3VzdG9tR2FzVXNlZENvbmZpZzogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkge30KICAgIH0sCiAgICBpc1Bob3Rvdm9sdGFpYzogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHdvcmtzaG9wOiByZXF1aXJlKCdAL2Fzc2V0cy93b3Jrc2hvcC5wbmcnKSwKICAgICAgZGVsaXZlcnk6IHJlcXVpcmUoJ0AvYXNzZXRzL2RlbGl2ZXJ5LnBuZycpLAogICAgICBnYXM6IHJlcXVpcmUoJ0AvYXNzZXRzL0J1c2luZXNzL2dhcy5wbmcnKQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICBkYXRhT2JqOiBmdW5jdGlvbiBkYXRhT2JqKCkgewogICAgICB2YXIgZGF0YSA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgdGhpcy5jdXN0b21HYXNVc2VkQ29uZmlnLmJhc2VEYXRhKSwgdGhpcy5jdXN0b21HYXNVc2VkQ29uZmlnLmdhc0RhdGEpOwogICAgICByZXR1cm4gZGF0YTsKICAgIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7fSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkge30sCiAgbWV0aG9kczoge30KfTs="}, {"version": 3, "names": ["components", "props", "customGasUsedConfig", "type", "Object", "default", "isPhotovoltaic", "Boolean", "data", "workshop", "require", "delivery", "gas", "computed", "dataObj", "_objectSpread", "baseData", "gasData", "created", "mounted", "methods"], "sources": ["src/views/business/energyManagement/pJEnergyAnalysis/gas/components/gasUsed.vue"], "sourcesContent": ["<template>\n  <div class=\"gas-container\">\n    <div class=\"title\">\n      {{ dataObj.title }}\n      <el-tooltip class=\"item\" effect=\"dark\" placement=\"top-start\">\n        <div slot=\"content\">{{ dataObj.tooltip }}</div>\n        <img\n          style=\"width: 16px; height: 16px; margin-left: 8px\"\n          src=\"@/assets/question.png\"\n          alt=\"\"\n        >\n      </el-tooltip>\n    </div>\n    <div class=\"middle\">\n      <div class=\"Bgbox\">\n        <div class=\"fill\">\n          <div\n            :style=\"{ height: dataObj.fillHeight, background: dataObj.color }\"\n          />\n        </div>\n        <div class=\"iconBg\" />\n      </div>\n      <div v-if=\"dataObj.showTotal\" class=\"middleText\">\n        剩余\n        <span :style=\"{ color: dataObj.color }\">{{\n          dataObj.residue.value\n        }}</span> {{ dataObj.unit }}\n        <span :style=\"{ color: dataObj.color }\">{{\n          dataObj.residue.percentage\n        }}</span>\n      </div>\n    </div>\n\n    <!-- 龙建科工 暂时隐藏 -->\n    <!-- <el-row :gutter=\"20\" class=\"customRow\">\n      <el-col v-for=\"(item, index) in dataObj.colData\" :key=\"index\" :span=\"8\">\n        <div class=\"bottomContainer\">\n          <img class=\"arrow\" :src=\"gas\" alt=\"\">\n          <div class=\"down\">\n            <img\n              :src=\"item.iconName == 'delivery' ? delivery : workshop\"\n              alt=\"\"\n            >\n            <div class=\"textData\">\n              <h2>{{ item.Key }}</h2>\n              <p :style=\"{ color: dataObj.color }\">\n                <span style=\"width: 70px\">{{ item.Percent }} %</span><span\n                  style=\"flex: 1\"\n                >{{ item.Value }} {{ dataObj.unit }}</span>\n              </p>\n            </div>\n          </div>\n        </div>\n      </el-col>\n    </el-row> -->\n  </div>\n</template>\n\n<script>\nexport default {\n  components: {},\n  props: {\n    customGasUsedConfig: {\n      type: Object,\n      default: () => {}\n    },\n    isPhotovoltaic: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      workshop: require('@/assets/workshop.png'),\n      delivery: require('@/assets/delivery.png'),\n      gas: require('@/assets/Business/gas.png')\n    }\n  },\n  computed: {\n    dataObj() {\n      const data = {\n        ...this.customGasUsedConfig.baseData,\n        ...this.customGasUsedConfig.gasData\n      }\n      return data\n    }\n  },\n  created() {},\n  mounted() {},\n  methods: {}\n}\n</script>\n <style scoped lang='scss'>\n.gas-container {\n  width: 100%;\n  height: 363px;\n  padding: 16px 24px;\n  border-radius: 4px;\n  display: flex;\n  flex-direction: column;\n  background: #fff;\n  .title {\n    display: flex;\n    align-items: center;\n    margin-bottom: 24px;\n  }\n  .middle {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    padding: 12px;\n    height: 133px;\n    background: linear-gradient(\n      90deg,\n      rgba(41, 141, 255, 0.05) 0%,\n      rgba(41, 141, 255, 0) 100%\n    );\n    .Bgbox {\n      width: 48px;\n      height: 110px;\n      margin-right: 24px;\n      position: relative;\n      .iconBg {\n        width: 48px;\n        height: 110px;\n        background: url(\"../../../../../../assets/Business/box.png\");\n        background-size: cover;\n        position: absolute;\n        top: 2px;\n        left: 0;\n      }\n      .fill {\n        position: absolute;\n        width: 40px;\n        height: 77px;\n        bottom: 12px;\n        left: 4px;\n        overflow: hidden;\n        div {\n          width: 40px;\n          position: absolute;\n          left: 0;\n          bottom: 0;\n        }\n      }\n    }\n    .middleText {\n      font-size: 24px;\n      color: #333;\n      span {\n        font-weight: bold;\n        margin: 0 16px;\n      }\n    }\n  }\n  .customRow {\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n    .bottomContainer {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      .arrow {\n        height: 72px;\n        width: 28px;\n      }\n      .down {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        height: 83px;\n        background: linear-gradient(\n          90deg,\n          rgba(41, 141, 255, 0.05) 0%,\n          rgba(41, 141, 255, 0) 100%\n        );\n        img {\n          width: 24px;\n          height: 24px;\n          margin-right: 16px;\n        }\n        .textData {\n          h2 {\n            color: #333;\n            font-size: 16px;\n            font-weight: 400;\n            margin-bottom: 16px;\n          }\n          p {\n            display: flex;\n          }\n          span {\n            display: block;\n            font-weight: bold;\n            font-size: 14px;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;EACAA,UAAA;EACAC,KAAA;IACAC,mBAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;IACA;IACAC,cAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA,EAAAC,OAAA;MACAC,QAAA,EAAAD,OAAA;MACAE,GAAA,EAAAF,OAAA;IACA;EACA;EACAG,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,IAAAN,IAAA,GAAAO,aAAA,CAAAA,aAAA,KACA,KAAAb,mBAAA,CAAAc,QAAA,GACA,KAAAd,mBAAA,CAAAe,OAAA,CACA;MACA,OAAAT,IAAA;IACA;EACA;EACAU,OAAA,WAAAA,QAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;AACA", "ignoreList": []}]}