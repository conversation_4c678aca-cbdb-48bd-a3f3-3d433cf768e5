<template>
  <el-select
    ref="select"
    v-model="value"
    v-bind="$attrs"
    @clear="handleSelectClear"
    @remove-tag="handleRemoveTag"
  >
    <el-option value="">
      <el-tree
        ref="tree"
        :data="data"
        highlight-current
        v-on="$listeners"
        @node-click="handleNodeClick"
      />
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'SelectTree',
  components: {

  },
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      value: '',
      valueIds: []
    }
  },
  mounted() {
    this.value = this.$attrs.multiple ? [] : ''
  },
  methods: {
    hiddenPopper() {
      this.$refs.select.blur()
    },

    handleNodeClick(data) {
      if (this.$attrs.multiple) {
        if (this.value.indexOf(data.label) > -1) return false
        this.value.push(data.label)
        this.valueIds.push(data.id)
      } else {
        this.value = data.label
        this.valueIds = data.id
      }
      this.$emit('getValueIds', this.valueIds)
      this.hiddenPopper()
    },

    handleSelectClear() {
      this.$emit('input', '')
    },

    handleRemoveTag(value) {
      const fileterValue = this.value.filter(item => item !== value)
      this.$emit('input', fileterValue)
    }
  }
}
</script>

<style scoped>

</style>
