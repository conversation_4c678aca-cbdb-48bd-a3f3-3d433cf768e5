<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60" height="60" viewBox="0 0 60 60">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        fill: rgba(41,141,255,0.16);
      }

      .cls-3 {
        clip-path: url(#clip-path);
      }

      .cls-4 {
        fill: #298dff;
      }
    </style>
    <clipPath id="clip-path">
      <rect id="矩形_2642" data-name="矩形 2642" class="cls-1" width="32" height="32"/>
    </clipPath>
  </defs>
  <g id="组_8043" data-name="组 8043" transform="translate(-582 -135)">
    <rect id="矩形_2998" data-name="矩形 2998" class="cls-2" width="60" height="60" rx="20" transform="translate(582 135)"/>
    <g id="paper-filled" transform="translate(596 149)">
      <g id="paper-filled-2" data-name="paper-filled" class="cls-3">
        <path id="路径_4913" data-name="路径 4913" class="cls-4" d="M28.83,22.73l-.2-.73H26V6a3,3,0,0,0-3-3H6A3.29,3.29,0,0,0,3,6.5,3.29,3.29,0,0,0,6,10V26a3,3,0,0,0,3,3H23.79a5.34,5.34,0,0,0,3.83-1.64A4.84,4.84,0,0,0,29,23.75a4.26,4.26,0,0,0-.17-1.02ZM6,8c-.53,0-1-.7-1-1.5S5.47,5,6,5Zm5,1H21v2H11Zm0,6H21v2H11ZM26.17,26a3.31,3.31,0,0,1-2.38,1H11a5,5,0,0,0,1-3H27a2.94,2.94,0,0,1-.83,2Z"/>
      </g>
    </g>
  </g>
</svg>
