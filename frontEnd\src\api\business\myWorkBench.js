import request from '@/utils/request'
import qs from 'qs'

// 告警通知
// 查询工作台业务模块（下拉接口)
export function GetModule(data) {
  return request({
    method: 'post',
    url: '/Platform/Workbench/GetModule',
    data
  })
}
// 根据业务模块获取工作台事件类型分类（下拉接口）
export function GetTypesByModule(data) {
  return request({
    method: 'post',
    url: '/Platform/Workbench/GetTypesByModule',
    data
  })
}

//  我的消息
// #region
// 获取消息类型列表
export function GetMessageType(data) {
  return request({
    method: 'get',
    url: '/Platform/WBMessage/GetMessageType',
    params: data
  })
}

// 查看消息列表
export function GetMessagePageList(data) {
  return request({
    method: 'post',
    url: '/Platform/WBMessage/GetPageList',
    data
  })
}

// 已读消息
export function ReadMessage(data) {
  return request({
    method: 'post',
    url: '/Platform/WBMessage/ReadMessage',
    data
  })
}
// #endregion

//  我的任务
// #region
// 获取我的任务列表
export function GetTaskPageList(data) {
  return request({
    method: 'post',
    url: '/Platform/MyTask/GetPageList',
    data
  })
}

// 获取我的任务类型
export function GetTaskType(data) {
  return request({
    method: 'get',
    url: '/Platform/MyTask/GetTaskType',
    params: data
  })
}

// 获取我的任务状态
export function GetTaskStatus(data) {
  return request({
    method: 'get',
    url: '/Platform/MyTask/GetTaskStatus',
    params: data
  })
}
// endregion

//  告警列表
// #region
// 获取我的告警列表
export function GetWarnPageList(data) {
  return request({
    method: 'post',
    url: '/Platform/MyWarning/GetPageList',
    data
  })
}

// 获取告警状态
export function GetWarningStatus(data) {
  return request({
    method: 'get',
    url: '/Platform/MyWarning/GetWarningStatus',
    params: data
  })
}

// 获取告警模块
export function GetWarningModule(data) {
  return request({
    method: 'get',
    url: '/Platform/MyWarning/GetWarningModule',
    params: data
  })
}

// 获取告警类型
export function GetWarningType(data) {
  return request({
    method: 'get',
    url: '/Platform/MyWarning/GetWarningType',
    params: data
  })
}

// 关闭告警
export function CloseWarning(data) {
  return request({
    method: 'post',
    url: '/Platform/MyWarning/CloseWarning',
    data
  })
}
// endregion

//  我的工作台
// #region
// 获取个人信息
export function GetUserInfo(data) {
  return request({
    method: 'get',
    url: '/Platform/PersonalWorkbench/GetUserInfo',
    params: data
  })
}

// 获取角色选择模块信息
export function GetDropModuleMenu(data) {
  return request({
    method: 'get',
    url: '/Platform/PersonalWorkbench/GetDropModuleMenu',
    params: data
  })
}

// 获取快捷操作菜单
export function GetUserMenu(data) {
  return request({
    method: 'get',
    url: '/Platform/PersonalWorkbench/GetUserMenu',
    params: data
  })
}

// 保存菜单配置
export function SaveUserModuleSetting(data) {
  return request({
    method: 'post',
    url: '/Platform/PersonalWorkbench/SaveUserModuleSetting',
    data
  })
}
// endregion
