export const tools = [
  {
    type: 'drag',
    icon: 'el-icon-rank',
    defaultIcon: 'el-icon-rank',
    name: '拖拽'
  },
  {
    type: 'connection',
    icon: 'el-icon-share',
    defaultIcon: 'el-icon-share',
    name: '连线'
  }
  // {
  //   type: 'zoom-in',
  //   icon: 'el-icon-zoom-in',
  //   name: '放大'
  // },
  // {
  //   type: 'zoom-out',
  //   icon: 'el-icon-zoom-out',
  //   name: '缩小'
  // }
]

export const commonNodes = [
  {
    type: 'start round mix',
    name: '开始',
    icon: 'iconfont icon-play',
    defaultIcon: 'iconfont icon-play',
    belongto: 'commonNodes'
  },
  {
    type: 'end round',
    name: '结束',
    icon: 'iconfont icon-end',
    defaultIcon: 'iconfont icon-end',
    belongto: 'commonNodes'
  },
  {
    type: 'node',
    name: '任务节点',
    icon: 'iconfont icon-expand2',
    defaultIcon: 'iconfont icon-expand2',
    belongto: 'commonNodes'
  },
  {
    type: 'fork',
    name: '会签开始',
    icon: 'iconfont icon-flow-start',
    defaultIcon: 'iconfont icon-flow-start',
    belongto: 'commonNodes'
  },
  {
    type: 'join',
    name: '会签结束',
    icon: 'iconfont icon-flow-end',
    defaultIcon: 'iconfont icon-flow-end',
    belongto: 'commonNodes'
  }
]

export const highNodes = [
  {
    type: 'child-flow',
    name: '子流程',
    icon: 'ChildFlowIcon',
    defaultIcon: 'ChildFlowIcon',
    belongto: 'highNodes'
  }
]

export const laneNodes = [
  {
    type: 'x-lane',
    name: '横向泳道',
    icon: 'iconfont icon-lane-horizontal',
    defaultIcon: 'iconfont icon-lane-horizontal',
    belongto: 'laneNodes'
  },
  {
    type: 'y-lane',
    name: '纵向泳道',
    icon: 'iconfont icon-lane-vertical',
    defaultIcon: 'iconfont icon-lane-vertical',
    belongto: 'laneNodes'
  }
]
