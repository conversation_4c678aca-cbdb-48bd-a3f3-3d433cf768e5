<template>
  <div>
    <el-input
      v-model="minV"
      style="width:40px;text-align:center;"
      placeholder="最小值"
      size="mini"
      @input="textInputMin"
      @change="miniChange"
      @focus="$emit('focus')"
    />
    ~
    <el-input
      v-model="maxV"
      style="width:40px;text-align:center;"
      placeholder="最大值"
      size="mini"
      @input="textInputMax"
      @change="maxChange"
      @focus="$emit('focus')"
    />
  </div>
</template>
<script>
export default {
  name: 'NumberRange',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => [null, null]
    },
    min: {
      type: Number,
      default: null
    },
    max: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      maxV: null,
      minV: null
    }
  },
  created() {
    console.log(this.value)
  },
  methods: {
    textInputMin(e) {
      if (Number(e) === NaN) {
        this.minV = null
      }
    },
    textInputMax(e) {
      if (Number(e) === NaN) {
        this.maxV = null
      }
    },
    miniChange(v) {
      this.minV = this.min ? Number(this.min) : Number(this.minV) || null
      this.maxV = this.max ? Number(this.max) : Number(this.maxV) || null
      this.$emit('change', [this.minV, this.maxV])
    },
    maxChange(v) {
      this.minV = this.min ? Number(this.min) : Number(this.minV) || null
      this.maxV = this.max ? Number(this.max) : Number(this.maxV) || null
      this.$emit('change', [this.minV, this.maxV])
    }
  }
}
</script>
