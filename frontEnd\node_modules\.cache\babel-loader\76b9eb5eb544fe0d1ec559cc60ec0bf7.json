{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\generation.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\generation.vue", "mtime": 1754615596910}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetElectricTrend", "<PERSON><PERSON>", "use", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Line<PERSON>hart", "<PERSON><PERSON><PERSON>", "GridComponent", "LegendComponent", "TooltipComponent", "TitleComponent", "DataZoomComponent", "components", "props", "isPhotovoltaic", "type", "Boolean", "default", "data", "lineChartOption", "tooltip", "trigger", "legend", "selected", "grid", "left", "right", "bottom", "xAxis", "axisLine", "show", "axisTick", "color", "yAxis", "position", "logBase", "dataZoom", "series", "name", "valueFormatter", "value", "concat", "loading", "computed", "parentData", "DateType", "StartTime", "randomInteger", "watch", "handler", "nv", "ov", "getElectricTrend", "created", "mounted", "inject", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "IsSucceed", "Data", "map", "item", "Key", "_item$Total", "Total", "_item$Electric", "Electric", "push", "_item$PV", "PV", "_item$TotalPV", "TotalPV", "_item$SellPV", "SellPV", "stop"], "sources": ["src/views/business/energyManagement/pJEnergyAnalysis/eleNew/components/generation.vue"], "sourcesContent": ["<template>\n  <div v-loading=\"loading\" class=\"generation\" element-loading-text=\"加载中...\">\n    <div class=\"title\">\n      <div class=\"left\">发电及用电趋势图</div>\n      <div class=\"right\">不包含重钢工厂</div>\n    </div>\n    <div class=\"chartBox\">\n      <v-chart :option=\"lineChartOption\" :autoresize=\"true\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetElectricTrend } from '@/api/business/energyManagement.js'\nimport VChart from 'vue-echarts'\nimport { use } from 'echarts/core'\nimport { CanvasRenderer } from 'echarts/renderers'\nimport { <PERSON><PERSON><PERSON>, Line<PERSON>hart, PieChart } from 'echarts/charts'\nimport {\n  GridComponent,\n  LegendComponent,\n  TooltipComponent,\n  TitleComponent,\n  DataZoomComponent\n} from 'echarts/components'\nuse([\n  Canvas<PERSON>ender<PERSON>,\n  <PERSON><PERSON>hart,\n  <PERSON><PERSON>hart,\n  <PERSON><PERSON>hart,\n  DataZoomComponent,\n  Grid<PERSON>omponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent\n])\nexport default {\n  components: {\n    VChart\n  },\n  props: {\n    isPhotovoltaic: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      lineChartOption: {\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          selected: {\n            '总发电(光伏)': false,\n            '售卖(光伏)': false\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '1%',\n          bottom: '80'\n        },\n        xAxis: {\n          type: 'category',\n          data: [],\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          }\n        },\n\n        color: ['#FF902C', '#298DFF', '#01AD5E', '#00B0F0', '#FF5E7C'],\n        yAxis: [\n          {\n            type: 'value',\n            position: 'left',\n            logBase: 10\n          }\n        ],\n        dataZoom: {\n          type: 'slider'\n        },\n        series: [\n          {\n            name: '总用电',\n            data: [5, 6, 7, 8, 1, 2, 3, 4, 6],\n            type: 'line',\n            tooltip: {\n              valueFormatter: function(value) {\n                return `${value || 0}` + ' 度'\n              }\n            }\n          },\n          {\n            name: '用电(市电)',\n            data: [1, 2, 3, 4, 5, 6, 7, 8],\n            type: 'line',\n            tooltip: {\n              valueFormatter: function(value) {\n                return `${value || 0}` + ' 度'\n              }\n            }\n          }\n        ]\n      },\n      loading: true\n    }\n  },\n  computed: {\n    parentData() {\n      return {\n        DateType: this.DateType(),\n        StartTime: this.StartTime(),\n        randomInteger: this.randomInteger()\n      }\n    }\n  },\n  watch: {\n    parentData: {\n      handler(nv, ov) {\n        this.getElectricTrend()\n      }\n    }\n  },\n  created() {\n    this.getElectricTrend()\n  },\n  mounted() {\n\n  },\n  inject: ['DateType', 'StartTime', 'randomInteger'],\n  methods: {\n    async getElectricTrend() {\n      this.loading = true\n      const res = await GetElectricTrend(this.parentData)\n      if (res.IsSucceed) {\n        this.lineChartOption.xAxis.data = res.Data.map(item => item.Key)\n        this.lineChartOption.series[0].data = res.Data.map(item => item.Total ?? 0)\n        this.lineChartOption.series[1].data = res.Data.map(item => item.Electric ?? 0)\n        if (this.isPhotovoltaic) {\n          this.lineChartOption.series.push(\n            {\n              name: '用电(光伏)',\n              data: [3, 4, 5, 6, 7, 8, 1, 2],\n              type: 'line',\n              tooltip: {\n                valueFormatter: function(value) {\n                  return `${value || 0}` + ' 度'\n                }\n              }\n            },\n            {\n              name: '总发电(光伏)',\n              data: [7, 8, 3, 4, 5, 6, 1, 2],\n              type: 'line',\n              tooltip: {\n                valueFormatter: function(value) {\n                  return `${value || 0}` + ' 度'\n                }\n              }\n            },\n            {\n              name: '售卖(光伏)',\n              data: [9, 3, 4, 5, 6, 7, 8, 1],\n              type: 'line',\n              tooltip: {\n                valueFormatter: function(value) {\n                  return `${value || 0}` + ' 度'\n                }\n              }\n            }\n          )\n          this.lineChartOption.series[2].data = res.Data.map(item => item.PV ?? 0)\n          this.lineChartOption.series[3].data = res.Data.map(item => item.TotalPV ?? 0)\n          this.lineChartOption.series[4].data = res.Data.map(item => item.SellPV ?? 0)\n        }\n      }\n      this.loading = false\n    }\n  }\n}\n</script>\n<style scoped lang='scss'>\n.generation {\n  height: 392px;\n  background: #fff;\n  border-radius: 4px;\n  width: 100%;\n  padding: 16px;\n  box-sizing: border-box;\n  margin-bottom: 16px;\n  .title {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 16px;\n    .left {\n      color: #666;\n      font-weight: bold;\n      font-size: 16px;\n    }\n    .right {\n      font-size: 12px;\n      color: #b8bec8;\n    }\n  }\n  .chartBox {\n    height: 320px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;AAaA,SAAAA,gBAAA;AACA,OAAAC,MAAA;AACA,SAAAC,GAAA;AACA,SAAAC,cAAA;AACA,SAAAC,QAAA,EAAAC,SAAA,EAAAC,QAAA;AACA,SACAC,aAAA,EACAC,eAAA,EACAC,gBAAA,EACAC,cAAA,EACAC,iBAAA,QACA;AACAT,GAAA,EACAC,cAAA,EACAC,QAAA,EACAC,SAAA,EACAC,QAAA,EACAK,iBAAA,EACAJ,aAAA,EACAC,eAAA,EACAE,cAAA,EACAD,gBAAA,CACA;AACA;EACAG,UAAA;IACAX,MAAA,EAAAA;EACA;EACAY,KAAA;IACAC,cAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,eAAA;QACAC,OAAA;UACAC,OAAA;QACA;QACAC,MAAA;UACAC,QAAA;YACA;YACA;UACA;QACA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;QACA;QACAC,KAAA;UACAb,IAAA;UACAG,IAAA;UACAW,QAAA;YACAC,IAAA;UACA;UACAC,QAAA;YACAD,IAAA;UACA;QACA;QAEAE,KAAA;QACAC,KAAA,GACA;UACAlB,IAAA;UACAmB,QAAA;UACAC,OAAA;QACA,EACA;QACAC,QAAA;UACArB,IAAA;QACA;QACAsB,MAAA,GACA;UACAC,IAAA;UACApB,IAAA;UACAH,IAAA;UACAK,OAAA;YACAmB,cAAA,WAAAA,eAAAC,KAAA;cACA,UAAAC,MAAA,CAAAD,KAAA;YACA;UACA;QACA,GACA;UACAF,IAAA;UACApB,IAAA;UACAH,IAAA;UACAK,OAAA;YACAmB,cAAA,WAAAA,eAAAC,KAAA;cACA,UAAAC,MAAA,CAAAD,KAAA;YACA;UACA;QACA;MAEA;MACAE,OAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA;QACAC,QAAA,OAAAA,QAAA;QACAC,SAAA,OAAAA,SAAA;QACAC,aAAA,OAAAA,aAAA;MACA;IACA;EACA;EACAC,KAAA;IACAJ,UAAA;MACAK,OAAA,WAAAA,QAAAC,EAAA,EAAAC,EAAA;QACA,KAAAC,gBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,gBAAA;EACA;EACAE,OAAA,WAAAA,QAAA,GAEA;EACAC,MAAA;EACAC,OAAA;IACAJ,gBAAA,WAAAA,iBAAA;MAAA,IAAAK,KAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAAf,OAAA;cAAAuB,QAAA,CAAAE,IAAA;cAAA,OACAnE,gBAAA,CAAAyD,KAAA,CAAAb,UAAA;YAAA;cAAAkB,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,SAAA;gBACAZ,KAAA,CAAAtC,eAAA,CAAAS,KAAA,CAAAV,IAAA,GAAA4C,GAAA,CAAAQ,IAAA,CAAAC,GAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,GAAA;gBAAA;gBACAhB,KAAA,CAAAtC,eAAA,CAAAkB,MAAA,IAAAnB,IAAA,GAAA4C,GAAA,CAAAQ,IAAA,CAAAC,GAAA,WAAAC,IAAA;kBAAA,IAAAE,WAAA;kBAAA,QAAAA,WAAA,GAAAF,IAAA,CAAAG,KAAA,cAAAD,WAAA,cAAAA,WAAA;gBAAA;gBACAjB,KAAA,CAAAtC,eAAA,CAAAkB,MAAA,IAAAnB,IAAA,GAAA4C,GAAA,CAAAQ,IAAA,CAAAC,GAAA,WAAAC,IAAA;kBAAA,IAAAI,cAAA;kBAAA,QAAAA,cAAA,GAAAJ,IAAA,CAAAK,QAAA,cAAAD,cAAA,cAAAA,cAAA;gBAAA;gBACA,IAAAnB,KAAA,CAAA3C,cAAA;kBACA2C,KAAA,CAAAtC,eAAA,CAAAkB,MAAA,CAAAyC,IAAA,CACA;oBACAxC,IAAA;oBACApB,IAAA;oBACAH,IAAA;oBACAK,OAAA;sBACAmB,cAAA,WAAAA,eAAAC,KAAA;wBACA,UAAAC,MAAA,CAAAD,KAAA;sBACA;oBACA;kBACA,GACA;oBACAF,IAAA;oBACApB,IAAA;oBACAH,IAAA;oBACAK,OAAA;sBACAmB,cAAA,WAAAA,eAAAC,KAAA;wBACA,UAAAC,MAAA,CAAAD,KAAA;sBACA;oBACA;kBACA,GACA;oBACAF,IAAA;oBACApB,IAAA;oBACAH,IAAA;oBACAK,OAAA;sBACAmB,cAAA,WAAAA,eAAAC,KAAA;wBACA,UAAAC,MAAA,CAAAD,KAAA;sBACA;oBACA;kBACA,CACA;kBACAiB,KAAA,CAAAtC,eAAA,CAAAkB,MAAA,IAAAnB,IAAA,GAAA4C,GAAA,CAAAQ,IAAA,CAAAC,GAAA,WAAAC,IAAA;oBAAA,IAAAO,QAAA;oBAAA,QAAAA,QAAA,GAAAP,IAAA,CAAAQ,EAAA,cAAAD,QAAA,cAAAA,QAAA;kBAAA;kBACAtB,KAAA,CAAAtC,eAAA,CAAAkB,MAAA,IAAAnB,IAAA,GAAA4C,GAAA,CAAAQ,IAAA,CAAAC,GAAA,WAAAC,IAAA;oBAAA,IAAAS,aAAA;oBAAA,QAAAA,aAAA,GAAAT,IAAA,CAAAU,OAAA,cAAAD,aAAA,cAAAA,aAAA;kBAAA;kBACAxB,KAAA,CAAAtC,eAAA,CAAAkB,MAAA,IAAAnB,IAAA,GAAA4C,GAAA,CAAAQ,IAAA,CAAAC,GAAA,WAAAC,IAAA;oBAAA,IAAAW,YAAA;oBAAA,QAAAA,YAAA,GAAAX,IAAA,CAAAY,MAAA,cAAAD,YAAA,cAAAA,YAAA;kBAAA;gBACA;cACA;cACA1B,KAAA,CAAAf,OAAA;YAAA;YAAA;cAAA,OAAAuB,QAAA,CAAAoB,IAAA;UAAA;QAAA,GAAAxB,OAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}