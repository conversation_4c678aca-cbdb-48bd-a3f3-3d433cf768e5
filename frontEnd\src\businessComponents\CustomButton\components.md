## CustomForm自定义组件使用说明

**### 自定义组件 CustomCheckCard**

```js
<CustomForm
	:customFormItems="customForm.formItems"
	:customFormButtons="customForm.customFormButtons"
	:value="ruleForm"
	:rules="customForm.rules"
	@submitForm="submitForm"
/>
```

**#### 组件配置 script**

```
ruleForm: {
companyName: '',
companyInfo: '',
selectType: '',
isEnable: false,
commodity: [],
sex: '',
sex1: '',
Date: '',
DateRang: '',
tel: ''
}
```

```js
customForm: {
    formItems: [
      {
        key: "companyName",//字段ID
        label: "公司名称",//Form的label
        type: "input",//input:普通输入框,textarea:文本域,select:下拉选择器,datepicker:日期选择器
        placeholder: "请输入公司名称",
        otherOptions: { //除了model以外的其他的参数,具体请参考element文档
          clearable: true,
        },
        width: '500px',
        change: (e) => { //change事件
          console.log(e)
        }
      },
      {
        key: "companyInfo",
        label: "公司信息",
        type: "textarea",
        placeholder: "请输入公司信息",
        otherOptions: {
          rows: 6,
        },
        change: (e) => {
          console.log(e)
        }
      },
      {
        key: "selectType",
        label: "公司类型",
        type: "select",
        placeholder: "请选择公司类型",
        options: [
          { label: '互联网', value: 'Internet' },
          { label: '传统', value: 'tradition' },
          { label: '电商', value: 'E-commerce' },
        ],
        change: (e) => {
          console.log(e)
        }
      },
      {
        key: "isEnable",
        label: "是否启用",
        type: "switch",
        otherOptions: {
          // disabled: true,
          activeColor: "#13ce66",
          inactiveColor: "#ff4949"
        },
        change: (e) => {
          console.log(e)
        }
      },
      {
        key: "commodity",
        label: "在售商品",
        type: "checkbox",
        checkboxOptions: [
          {
            label: '手机',
            otherOptions: {
              disabled: true,
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            label: '电脑',
            change: (e) => {
              console.log(e)
            }
          },
        ],
        change: (e) => {
          console.log(e)
        }
      },
      {
        key: "sex",
        label: "性别",
        type: "radio",
        radioOptions: [
          {
            label: '男',
            otherOptions: {
              disabled: true,
            },
            change: (e) => {
              console.log(e)
            }
          },
          {
            label: '女',
            change: (e) => {
              console.log(e)
            }
          },
        ],
        change: (e) => {
          console.log(e)
        }
      },
      {
        key: "sex1",
        label: "性别",
        type: "radioBtn",
        radioOptions: [
          {
            label: '男',
            change: (e) => {
              console.log(e)
            }
          },
          {
            label: '女',
            change: (e) => {
              console.log(e)
            }
          },
        ],
        change: (e) => {
          console.log(e)
        }
      },
      {
        key: "Date",
        label: "日期",
        type: "datePicker",
        otherOptions: {
          type: "date"
        }
      },
      {
        key: "DateRang",
        label: "日期范围",
        type: "datePicker",
        otherOptions: {
          type: "daterange",
          rangeSeparator: "至",
          startPlaceholder: "开始日期",
          endPlaceholder: "结束日期"
        }
      },
      {
        key: "tel",
        label: "手机号",
        type: "input",
        placeholder: "请输入手机号",
        maxlength: 11,
      }
    ],
    rules: {//请参照elementForm rules
      companyName: [
        {
          required: true,
          message: "公司名称不能为空",
          trigger: ["blur", "change"],
        },
      ],
      tel: [
        { required: true, message: "请输入手机号", trigger: ["blur", "change"] },
        {
          min: 11,
          message: "手机号格式错误!",
          trigger: ["blur", "change"],
        },
      ],
    },
    customFormButtons: {
      submitName: '查询',
      resetName: '取消'
    }
  },
```

```js
submitForm(data) {
  console.log(data)
}
```