import request from '@/utils/request'
// 查询人员列表
export function querypersonnel(data) {
  return request({
    method: 'post',
    url: '/df/personnel/querypersonnel',
    data
  })
}
// 查询人员详情
export function GetPersonnelDetail(data) {
  return request({
    method: 'post',
    url: '/df/personnel/GetPersonnelDetail',
    data
  })
}
// 新增人员信息
export function savepersonnel(data) {
  return request({
    method: 'post',
    url: '/df/personnel/savepersonnel',
    data
  })
}
// 删除人员信息
export function DeletePersonnel(data) {
  return request({
    method: 'post',
    url: '/df/personnel/DeletePersonnel',
    data
  })
}
// 导出人员为 Excel 文件
export function DownloadPersonnelsToExcel(data) {
  return request({
    method: 'post',
    url: '/df/personnel/DownloadPersonnelsToExcel',
    data
  })
}
// v2 导出人员为 Excel 文件
export function ExportPersonnelList(data) {
  return request({
    method: 'post',
    url: '/DF/Personnel/v2/ExportPersonnelList',
    data
  })
}
// 下载人员导入模板
export function DownloadPersonnelsTemplate(data) {
  return request({
    method: 'post',
    url: '/df/personnel/DownloadPersonnelsTemplate',
    data
  })
}
// 导入
export function ImportPersonnel(data) {
  return request({
    method: 'post',
    url: '/df/personnel/ImportPersonnel',
    data
  })
}

// 获取考勤班组
export function GetClockkingInTeams(data) {
  return request({
    method: 'post',
    url: '/df/personnel/GetClockkingInTeams',
    data
  })
}

// 班组分类
export function GetClockkingInTeamTypes(data) {
  return request({
    method: 'post',
    url: '/df/personnel/GetClockkingInTeamTypes',
    data
  })
}
