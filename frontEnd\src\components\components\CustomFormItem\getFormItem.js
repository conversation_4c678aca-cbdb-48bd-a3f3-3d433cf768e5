export function getFormItem(col,config) {
    var comWidth=col.inputWidth&&col.inputWidthUnit?col.inputWidth+col.inputWidthUnit:config.inputWidth+config.inputWidthUnit
    var disabled=`'${col.disabledStatus}'.includes(config.currStatusCode)`
    var res=''
    if(col.type==='input'){
      res= `<el-input 
      ${col.inputConfig.textarea?`type="textarea"`:''}
        :autosize="{ minRows: ${col.inputConfig.minRows}, maxRows:  ${col.inputConfig.maxRows} }" 
        v-model="form.${col.path}" 
        style="width:${comWidth}" 
        :disabled="${disabled}"
        placeholder="${col.inputConfig.placeholder?col.inputConfig.placeholder:'请填写内容'}"
        ${getCheckConfig(col)}
        @blur="(e)=>(form.${col.path} = e.target.value)"/>`
    }else if(col.type==='select'){
      res=`<el-select 
      v-model="form.${col.path}" 
      :multiple="${col.selectConfig.multiple}" 
      :disabled="${disabled}" 
      :clearable="${col.selectConfig.clearable}" 
      :filterable="${col.selectConfig.filterable}" 
      placeholder="${col.selectConfig.placeholder?col.selectConfig.placeholder:'请选择'}"
      ${col.onChange?(col.onChange.length>0?`@change="${col.code}Change"`:''):''}
      style="width:${comWidth}">
        <el-option 
        v-for="(item,index) in ${col.usingcode?col.usingcode:[]}" 
        :key="'select'+index" 
        :value="item.${col.selectConfig.type==='dictionary'?'Value':col.selectConfig.value}" 
        :label="item.${col.selectConfig.type==='dictionary'?'Display_Name':col.selectConfig.label}"
        />
      </el-select>`
    }else if(col.type==='switch'){
      res=`<el-switch v-model="form.${col.path}" ${col.switchConfig.activeColor?`:active-color="${col.switchConfig.activeColor}"`:''} :disabled="${disabled}" ${col.switchConfig.inactiveColor?`:inactive-color="${col.switchConfig.inactiveColor}"`:''}/>`
    }else if(col.type==='date'){
      res= `<el-date-picker 
      v-model="form.${col.path}" 
      type="${col.dateConfig.range?col.dateConfig.type+'range':col.dateConfig.type}" 
      value-format="yyyy-MM-dd hh:mm:ss" 
      format="${col.dateConfig.format}" 
      placeholder="${col.dateConfig.placeholder?col.dateConfig.placeholder:'请选择'}"
      :disabled="${disabled}"
      ${col.dateConfig.range?`:range-separator="${col.dateConfig.rangeSeparator}"`:''}
      ${col.dateConfig.range?`:start-placeholder="${col.dateConfig.startPlaceholder}"`:''}
      ${col.dateConfig.range?`:end-placeholder="${col.dateConfig.endPlaceholder}"`:''}
      style="width:${comWidth}"/>`
    }else if(col.type==='radio'){
      res=`<el-radio 
        v-for="(item,index) in ${col.usingcode?col.usingcode:[]}" 
        :key="'radio'+index" 
        v-model="form.${col.path}" 
        :disabled="${disabled}" 
        :label="item.${col.radioConfig.value}">
        {{item.${col.radioConfig.label}}}
      </el-radio>`
    }else if(col.type==='upload'){
      if(col.uploadConfig.picture){
        res=`<OSSUpload
          class="upload-demo"
          list-type="picture-card"
          action="string"
          :on-success="${col.code}HandleSuccess"
          :file-list="${col.code}FileList"
          :on-remove="${col.code}HandleRemove"
          :on-preview="${col.code}handlePreview"
          :limit="${col.uploadConfig.limit}"
          ${col.uploadConfig.multiple?'multiple':''}>
          <i class="el-icon-plus"></i>
        </OSSUpload>`
      }else{
        res=`<OSSUpload
          class="upload-demo"
          drag
          action="string"
          :on-success="${col.code}HandleSuccess"
          :file-list="${col.code}FileList"
          :on-remove="${col.code}HandleRemove"
          :on-preview="${col.code}handlePreview"
          :limit="${col.uploadConfig.limit}"
          ${col.uploadConfig.multiple?'multiple':''}>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </OSSUpload>`
      }
    }
      
    return res
}

function getCheckConfig(col) {
  var res=''
    if(col.inputConfig.inputLimit==='num2'){  
      res= `onkeyup="value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, '$1')" `
    }else if(col.inputConfig.inputLimit==='num'){  
      res= `onkeyup="value=value.replace(/\\D/g,'')" `
    }else if(col.inputConfig.inputLimit==='Chinese'){  
      res= `onkeyup="value=value.replace(/[^\\u4e00-\\u9fa5]/g,'')" `
    }else if(col.inputConfig.inputLimit==='letter'){  
      res= `onkeyup="value=value.replace(/[^a-zA-Z]/g,'')" `
    }else if(col.inputConfig.inputLimit==='Id'){  
      res= `onkeyup="value=value.replace(/^([1-9]\\d{5})(\\d{4})(\\d{2})(\\d{2})(\\d{3})(\\d|X)$/g, '$1')" `
    }else if(col.inputConfig.inputLimit==='tel'){  
      res= `value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, '$1')" `
    }
    return res
}