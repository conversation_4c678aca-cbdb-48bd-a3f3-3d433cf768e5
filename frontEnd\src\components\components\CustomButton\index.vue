
<template>
  <div class="CustomButton">
    <el-button
      v-for="(item, index) in customButtonObj.buttonList"
      :key="index"
      :round="item.round"
      :plain="item.plain"
      :circle="item.circle"
      :loading="item.loading"
      :disabled="item.disabled"
      :icon="item.icon"
      :autofocus="item.autofocus"
      :type="item.type"
      :size="item.size"
      v-show="item.isShow||item.isShow===undefined"
      @click="()=>{
        item.onclick ? item.onclick(item) : null
      }"
    >{{ item.text }}</el-button>
  </div>
</template>

<script>
export default {
  name: 'CustomButton',
  props: {
    customButtonConfig: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {
    customButtonObj() {
      return this.customButtonConfig
    }
  },
  created() {},

  methods: {}
}
</script>

<style lang="scss" scoped>
</style>
