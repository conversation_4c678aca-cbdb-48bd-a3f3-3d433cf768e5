<template>
  <div
    :class="{ 'dynamic-table': true, 'head-search': headSearchEnable }"
    :style="{
      minHeight: Number(opts.Height) > 0 ? Number(opts.Height) + 'px' : false
    }"
  >
    <div
      ref="tableWrapper"
      class="table-wrapper"
      style="position:relative; overflow:hidden;"
    >
      <div
        v-if="opts.Is_Filter && searchBtnShown"
        ref="searchbtn"
        class="fixed-search"
        :style="{
          position: 'absolute',
          zIndex: 1000
        }"
      >
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click="doSearch"
        >搜索</el-button>
      </div>
      <el-table
        :key="opts.Code"
        ref="dtable"
        :size="size"
        :border="border"
        :stripe="stripe"
        :data="tableData"
        highlight-current-row
        :row-style="tableRowStyle"
        :row-class-name="tableRowClassName"
        :default-expand-all="opts.Is_Expand_All"
        :row-key="opts.Tree_Key"
        :expand-row-keys="expends"
        :height="tbHeight"
        :max-height="maxHeight"
        :lazy="opts.Is_Lazy"
        :load="lazyLoadChildren"
        :tree-props="{
          children: opts.Children_Field,
          hasChildren: opts.Has_Children_Field
        }"
        :show-summary="opts.Is_Summary"
        :summary-method="customSummary"
        :style="{
          width: opts.Is_Auto_Width
            ? false
            : opts.Width == 0
              ? false
              : Number(opts.Width) + 'px'
        }"
        @current-change="handleCurrentRowChange"
        @row-click="handleRowClick"
        @cell-click="handleCellClick"
        @expand-change="expandChange"
        @header-dragend="headerDragend"
        @selection-change="handleSelectionChange"
        @select="handleSelect"
        @select-all="handleSelectAll"
        @sort-change="tableCustomSortChange"
      >
        <el-table-column
          v-if="opts.Is_Select"
          type="selection"
          :reserve-selection="opts.Is_Reserve"
          :width="selectWidth"
          fixed="left"
          align="center"
          :resizable="false"
          :selectable="checkSelectable"
        >
          <template slot="header" slot-scope="{ column }">
            {{ column.label }}
            <!-- el-table 中 type 为 selection 的列无 header 插槽-->
            <div v-if="opts.Is_Filter" class="custom-filter" @click.stop />
          </template>
        </el-table-column>
        <el-table-column
          v-if="opts.Is_Sub_Grid"
          type="expand"
          label="展开"
          :resizable="false"
        >
          <template slot="header" slot-scope="{ column }">
            {{ column.label }}
            <div v-if="opts.Is_Filter" class="custom-filter" @click.stop />
          </template>
          <template slot-scope="{ row, column, $index }">
            <slot name="expand" :row="row" :column="column" :$index="$index" />
          </template>
        </el-table-column>
        <el-table-column
          v-if="opts.Is_Row_Number"
          type="index"
          label="序号"
          width="50"
          align="center"
          :index="indexMethod"
          :resizable="false"
        >
          <template slot="header" slot-scope="{ column }">
            {{ column.label }}
            <div v-if="opts.Is_Filter" class="custom-filter" @click.stop />
          </template>
        </el-table-column>
        <el-table-column
          v-for="col in orderedColumns"
          :key="col.Code"
          :prop="col.Code"
          :sortable="
            col.Is_CustomSort ? 'custom' : col.Is_Sortable ? true : false
          "
          :label="col.Display_Name"
          :resizable="col.Is_Resizable || false"
          :width="opts.Is_Auto_Width ? 0 : Number(col.Width)"
          :min-width="col.Min_Width"
          :fixed="
            col.Is_Frozen ? (col.Frozen_To ? col.Frozen_To : 'left') : false
          "
          :show-summary="col.SummaryMethod"
          :align="col.Align ? col.Align : 'left'"
        >
          <template slot="header" slot-scope="{ column }">
            {{ column.label }}
            <div v-if="opts.Is_Filter" class="custom-filter" @click.stop>
              <template v-if="col.Is_Filter">
                <slot
                  v-if="$scopedSlots['hsearch_' + col.Code]"
                  :name="'hsearch_' + col.Code"
                  :column="col"
                />
                <el-date-picker
                  v-else-if="col.Filter_Type === 'date'"
                  v-model="searchedField[col.Code]"
                  class="cell-control"
                  :type="'date'"
                  style="width:96%"
                  size="mini"
                  placeholder="选择日期"
                  :value-format="col.Formatter"
                  :format="col.Formatter"
                  clearable
                  @focus="showSearch"
                  @change="
                    $emit('columnSearchChange', {
                      column: col,
                      value: $event
                    })
                  "
                />
                <el-date-picker
                  v-else-if="col.Filter_Type === 'daterange'"
                  v-model="searchedField[col.Code]"
                  class="cell-control"
                  :type="'daterange'"
                  range-separator="~"
                  style="width:96%"
                  size="mini"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :value-format="col.Formatter"
                  :format="col.Formatter"
                  clearable
                  @focus="showSearch"
                  @change="
                    $emit('columnSearchChange', {
                      column: col,
                      value: $event
                    })
                  "
                />
                <el-select
                  v-else-if="
                    col.Filter_Type === 'radio' ||
                      col.Filter_Type === 'switch' ||
                      col.Filter_Type === 'boolean'
                  "
                  v-model="searchedField[col.Code]"
                  filterable
                  placeholder="请选择"
                  style="width:96%"
                  clearable
                  @focus="showSearch"
                  @change="
                    $emit('columnSearchChange', {
                      column: col,
                      value: $event
                    })
                  "
                >
                  <el-option
                    v-for="(item, i) in parseJsonRange(col.Range)"
                    :key="i"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <NumberRange
                  v-else-if="col.Filter_Type === 'numberrange'"
                  v-model="searchedField[col.Code]"
                  :min="parseJsonRange(col.Range)[0]"
                  :max="parseJsonRange(col.Range)[1]"
                  @focus="showSearch"
                  @change="
                    $emit('columnSearchChange', {
                      column: col,
                      value: $event
                    })
                  "
                />
                <el-input
                  v-else
                  v-model="searchedField[col.Code]"
                  placeholder="搜索..."
                  clearable
                  @focus="showSearch"
                  @change="
                    $emit('columnSearchChange', {
                      column: col,
                      value: $event
                    })
                  "
                />
              </template>
            </div>
          </template>
          <template slot-scope="{ row, $index, store }">
            <slot
              v-if="$scopedSlots[col.Code]"
              :name="col.Code"
              :$index="$index"
              :column="col"
              :row="row"
              :store="store"
            />
            <el-tooltip v-else :disabled="!col.IS_ToolTip" effect="light">
              <div slot="content">
                <slot
                  name="tooltip"
                  :column="col"
                  :row="row"
                  :$index="$index"
                />
              </div>
              <div
                ref="cellbox"
                :class="{
                  'dtb-cell-break-line': opts.Is_Break_Line,
                  'dtb-cell-nowrap': !opts.Is_Break_Line
                }"
                :style="{
                  textOverflow: col.IS_ToolTip ? `ellipsis` : ''
                }"
              >
                <GTableCell
                  :key="col.Code"
                  :ref="'g-cell-' + col.Code + '-' + $index"
                  :show-tooltip="!opts.Is_Break_Line"
                  :editable="opts.Is_Edit"
                  :edit-mode="editingRowStatus[$index]"
                  :row-index="$index"
                  :column="col"
                  :row="row"
                  :options="[]"
                  :cell-editor-blur-save-model="cellEditorBlurSaveModel"
                  @inlineEdited="inlineEdited"
                  @changeRowEditMode="changeRowEditMode"
                />
                <span
                  v-if="
                    opts.Is_Edit && col.Is_Edit && !editingRowStatus[$index]
                  "
                  class="editme"
                  @click.stop="editCell(col.Code, $index)"
                ><i
                  class="el-icon-edit"
                /></span>
                <span
                  v-if="opts.Is_Edit && col.Is_Edit && editingRowStatus[$index]"
                  class="editme"
                  style="color:#67C23A"
                  @click.stop="doEdit(col.Code, $index)"
                ><i
                  class="el-icon-check"
                /></span>
                <slot
                  :name="`innertip`"
                  :column="col"
                  :row="row"
                  :$index="$index"
                  :store="store"
                />
                <template v-if="$scopedSlots['innertip-' + col.Code]">
                  <slot
                    :name="'innertip-' + col.Code"
                    :column="col"
                    :$index="$index"
                    :row="row"
                  />
                </template>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <template v-if="$scopedSlots.op">
          <el-table-column
            align="center"
            :width="opts.Op_Width"
            :label="opts.Op_Label"
            fixed="right"
            :resizable="false"
          >
            <template slot="header" slot-scope="{ column }">
              {{ column.label }}
              <div v-if="opts.Is_Filter" class="custom-filter" @click.stop />
            </template>
            <template slot-scope="{ row, $index }">
              <slot name="op" :row="row" :$index="$index" />
            </template>
          </el-table-column>
        </template>
        <template v-if="$slots.append" slot="append">
          <slot name="append" />
        </template>
      </el-table>
    </div>
    <div class="custom-pagination">
      <div v-if="opts.Is_Select && opts.Is_Page" class="checked-count">
        <el-tag
          size="medium"
          style="padding:0 24px;"
        >已选{{ checkedRows.length }}条数据</el-tag>
        <slot name="tipLabel" v-bind="{}" />
      </div>
      <el-pagination
        v-if="opts.Is_Page"
        class="pagination"
        :pager-count="pagerCount"
        background
        :current-page="CurrentPage"
        :page-sizes="pageSizes"
        :page-size="Number(opts.Row_Number)"
        :layout="
          opts.Pager_Layout
            ? opts.Pager_Layout
            : 'total, sizes, prev, pager, next, jumper'
        "
        :total="Total"
        :style="{
          textAlign: opts.Pager_Align || 'left',
          display: 'flex',
          alignItems: 'center',
          justifyContent:
            opts.Pager_Align === 'right'
              ? 'flex-end'
              : opts.Pager_Align === 'center'
                ? 'center'
                : 'flex-end'
        }"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import GTableCell, { parseJsonRange } from './GTableCell'
import NumberRange from './NumberRange'
import {
  addResizeListener,
  removeResizeListener
} from 'element-ui/src/utils/resize-event'
import { tablePageSize } from './setting'

export default {
  name: 'DynamicDataTable',
  components: {
    GTableCell,
    NumberRange
  },
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    },
    selectWidth: {
      type: Number,
      default: 48
    },
    page: {
      type: Number,
      default: 0
    },
    border: {
      type: Boolean,
      default: false
    },
    stripe: {
      type: Boolean,
      default: false
    },
    columns: {
      type: Array,
      default: () => []
    },
    cellEditorBlurSaveModel: {
      type: Boolean,
      default: true
    },
    query: {
      type: Object,
      default: () => ({})
    },
    expends: {
      type: Array,
      default: () => []
    },
    sumValues: {
      type: Array,
      default: () => []
    },
    size: {
      type: String,
      default: 'medium'
    },
    pagerCount: {
      type: Number,
      default: 7
    },
    isRadio: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      opts: {
        AutoIncrm_Id: 0, // 自增ID
        Code: '', // 表格编码
        Create_Date: '', // 创建时间
        Create_UserId: '', // 创建者
        Create_UserName: '',
        Data_Url: '', // 数据获取地址
        Data_Params: [], // 表格数据获取参数表
        Display_Name: '', // 表格名
        Height: null, // 表格高度,Height 为0，自适应外部元素高度；不定义，根据数据决定表格高度；为值则使用指定高度,
        Id: '', // 表格唯一ID
        Is_Auto_Width: true, // 列自动宽度
        Is_Deleted: false, // 可否删除
        Is_Reserve: false, // 是否跨分页保留选择行
        Is_Edit: false, // 可否编辑
        Is_Filter: false, // 是否显示过滤
        Is_Page: false, // 是否显示分页
        Is_Row_Number: false, // 是否显示行号
        Is_Select: false, // 是否显示选择列
        Is_Sub_Grid: false, // 是否有子表
        Sub_Grid_Code: '', // 子表编码
        Is_Sortable: false, // 表头排列
        Is_CustomSort: false, // 自定义排序
        Is_Summary: false, // 是否显示合计行
        Is_Break_Line: false, // 单元格内是否换行
        Menu_Id: '', // 所属菜单ID
        Modify_Date: '', // 修改日期
        Modify_UserId: '', // 修改用户
        Modify_UserName: '', //
        Pager_Id: '', // -
        Remark: '', // 表格备注信息
        Row_Number: 20, // 每页表格行数
        Sort_Column: '', // 表格默认排序字段
        Sort_Type: 'desc', // 表格默认排序方式
        Table_Name: '', // 数据库表名
        Width: 0, // 表格宽度
        Op_Width: 160, // 存在操作列，默认操作列宽度
        Op_Label: '操作', // 默认操作列标签
        Is_Expand_All: false, // 是否默认展开所有树或子表
        Tree_Key: 'Id', // 树形表需要的 row-key
        Is_Lazy: false, // 树表是否懒加载
        Children_Field: 'children', // 树表子集所在字段
        Has_Children_Field: 'hasChildren' // 树表判断是否具有子集字段
      },
      CurrentPage: 1,
      Total: 0,
      currentRow: null,
      searchedField: {}, // 检索键值对
      searchBtnShown: false,
      outerHeight: null,
      pagerH: 66, // 分页和margin高度
      editingRowStatus: {}, // 编辑状态的行及编辑状态(ready 准备编辑,valid 字段验证有效,invalid 字段验证无效,success 编辑完成,fail 编辑失败)
      checkedRows: []
    }
  },
  computed: {
    tbHeight() {
      let h
      if (this.opts.Height === null || this.opts.Height === undefined) {
        h = null
      } else if (Number(this.opts.Height) === 0) {
        h = this.outerHeight
      } else {
        h = Number(this.opts.Height)
      }
      if (this.opts.Is_Page && h !== null) {
        h -= this.pagerH
      }
      return h
    },
    maxHeight() {
      if (!this.outerHeight || this.tbHeight === null) return null
      if (Number(this.opts.Height) > 0) return Number(this.opts.Height)
      if (!this.opts.Is_Page) {
        return this.outerHeight
      } else {
        return this.outerHeight - this.pagerH
      }
    },
    tableData() {
      return this.sortData(this.$props.data)
    },
    headSearchEnable() {
      return this.opts.Is_Filter
    },
    orderedColumns() {
      const columns = this.columns.concat([])
      columns.sort((a, b) => {
        return Number(a.Sort) - Number(b.Sort)
      })
      return columns.filter(c => c.Is_Display)
    },
    pageSizes() {
      if (this.opts.Page_Sizes) return this.opts.Page_Sizes
      let sizes = tablePageSize
      const size = Number(this.opts.Row_Number)
      if (size) {
        // const w = parseInt(size / 10).toString()
        // sizes = []
        // ;[1, 2, 3, 4, 5].forEach(n => {
        //   sizes.push(n * w.length * 20)
        // })
        if (sizes.indexOf(size) < 0) {
          sizes = [size].concat(sizes)
          sizes.sort((a, b) => a - b)
        }
      }
      return sizes
    }
  },
  watch: {
    total(nv) {
      this.Total = nv
    },
    page(nv) {
      this.CurrentPage = nv
    },
    sumValues: {
      handler(nv) {
        this.sumValues = nv
        this.customSummary({})
      },
      deep: true
    },
    config(nv) {
      this.opts = Object.assign({}, this.opts, nv)
    },
    data(nv) {
      if (!this.opts.Is_Break_Line) {
        setTimeout(() => {
          this.cellBreakLineHandle()
        }, 100)
      }
    }
  },
  created() {
    this.opts = Object.assign({}, this.opts, this.config)
    this.searchedField = Object.assign({}, this.query)
    // this.CurrentPage = this.page
    // this.Total = this.total
  },
  beforeDestroy() {
    removeResizeListener(this.$el, this.windowResizeHandler)
  },
  updated() {
    if (this.opts.Is_Filter) {
      this.reposeSearchBtn()
    }
    setTimeout(() => {
      this.$refs.dtable?.doLayout()
    }, 500)
  },
  mounted() {
    addResizeListener(this.$el, this.windowResizeHandler)
    this.getOuterHeight()
  },
  methods: {
    checkSelectable(row) {
      return this.opts.checkSelectable &&
        Object.prototype.toString.call(this.opts.checkSelectable) ===
          '[object Function]'
        ? this.opts.checkSelectable(row)
        : true
    },
    doEdit(code, index) {
      this.$refs['g-cell-' + code + '-' + index][0].setEditMode(null)
    },
    editCell(code, index) {
      this.$refs['g-cell-' + code + '-' + index][0]?.setEditMode('ready')
    },
    cellBreakLineHandle() {
      this.$refs.cellbox?.forEach(div => {
        const span = div.querySelector('.cell-span')
        let endfixW = 0
        if (div.lastChild !== span && div.lastChild.nodeName !== '#comment') {
          // 带 comment
          endfixW = 24
        }
        if (div.offsetWidth < span.offsetWidth + endfixW) {
          span.style.width = div.offsetWidth - endfixW + 'px'
        }
        // span.title = span.innerText
      })
    },
    windowResizeHandler: function() {
      this.getOuterHeight()
      if (this.opts.Is_Filter) {
        this.reposeSearchBtn()
      }
      this.$refs.dtable?.doLayout()
    },
    getOuterHeight() {
      this.outerHeight = this.$el?.parentNode?.offsetHeight ?? null
    },
    showSearch() {
      this.searchBtnShown = true
    },
    hideSearch() {
      this.searchBtnShown = false
    },
    doSearch() {
      this.$emit('tableSearch', this.searchedField)
      this.hideSearch()
    },
    tableCustomSortChange({ column, prop, order }) {
      this.$emit('sort-change', { column, prop, order })
    },
    reposeSearchBtn() {
      const searchBtnTop = this.$refs.dtable?.$refs.headerWrapper?.offsetHeight
      let searchBtnRight = 0
      if (this.$scopedSlots.op) searchBtnRight = this.opts.Op_Width + 2
      if (this.$refs.dtable?.$refs.rightFixedPatch) {
        searchBtnRight += this.$refs.dtable?.$refs.rightFixedPatch.offsetWidth
      }
      if (this.$refs.searchbtn) {
        this.$refs.searchbtn.style.top = searchBtnTop - 8 + 'px'
        this.$refs.searchbtn.style.right = searchBtnRight + 'px'
      }
    },
    sortData(arr) {
      if (
        Object.prototype.toString.call(this.$props.data) !== '[object Array]'
      ) {
        arr = []
      }
      return arr.concat([])
    },
    cdebug(d) {
    },
    tableRowClassName({ row, rowIndex }) {
      let cls = ''
      if (row.onEdit) cls = 'warning-row'
      return cls
    },
    tableRowStyle({ row, rowIndex }) {
      let result
      this.$emit('setTableRowStyle', { row, rowIndex }, val => {
        result = val
      })
      return result
    },
    sortChange({ column, order, prop }) {
    },
    indexMethod(index) {
      return index + 1
    },
    columnSearchInput(column) {
      const key = column.property
    },
    columnSearchChange(column) {
      const key = column.property
    },
    handleSizeChange(size) {
      this.$emit('gridSizeChange', { page: this.CurrentPage, size })
    },
    handleCurrentChange(page) {
      this.$emit('gridPageChange', { page })
    },
    closeEditRow(index) {
      delete this.editingRowStatus[index]
      this.editingRowStatus = Object.assign({}, this.editingRowStatus)
    },
    handleCurrentRowChange(row) {
      this.currentRow = row
      this.$emit('currentRowChange', { row })
    },
    handleRowClick(row, column, event) {
      this.$emit('handleRowClick', { row, column, event })
    },
    handleCellClick(row, column, cell, event) {
    },
    expandChange(row, expandedRows) {
      this.$emit('rowExpanded', { row, expandedRows })
    },
    inlineEdited({ index, row, key, value }) {
      this.$emit('cellEditorChanged', {
        index,
        row,
        key,
        value
      })
    },
    lazyLoadChildren(tree, treeNode, resolve) {
      this.$emit('lazyLoadChildrenNodes', { tree, treeNode, resolve })
    },
    customSummary({ columns, data }) {
      if (this.sumValues) {
        return this.sumValues
      }
      const sums = {}
      columns.forEach((col, index) => {
        const colData = this.columns.find(c => c.Code === col.property)
        if (index === 0) {
          sums[index] = '计'
        } else {
          sums[index] = ''
          if (colData?.SummaryMethod) {
            if (VALID_SUM_METHODS[colData.SummaryMethod]) {
              const values = data.map(item => Number(item[col.property]))
              sums[index] = `${
                VALID_SUM_METHODS[colData.SummaryMethod].label
              }: ${VALID_SUM_METHODS[colData.SummaryMethod].func(values)}`
            }
          }
        }
      })
      return sums
    },
    headerDragend(newWidth, oldWidth, column, event) {
      if (this.opts.Is_Filter) {
        this.reposeSearchBtn()
      }
      // setTimeout(() => {
      //   this.$refs.dtable.$el
      //     .querySelectorAll('.el-table__fixed-body-wrapper')
      //     .forEach(el => {
      //       el.style.top =
      //         this.$refs.dtable.$el.querySelector(
      //           '.el-table__fixed-header-wrapper'
      //         ).clientHeight + 'px'
      //     })
      // }, 10)
    },
    handleSelectionChange(val) {
      this.checkedRows = val
      this.$emit('multiSelectedChange', val)
    },
    changeRowEditMode({ index, mode }) {
      const obj = {}
      obj[index] = mode
      this.editingRowStatus = Object.assign({}, this.editingRowStatus, obj)
      this.$emit('rowEditModeChanged', this.editingRowStatus)
    },
    parseJsonRange(range) {
      return parseJsonRange(range)
    },
    handleSelect(selection, row) {
      if (this.isRadio) {
        if (selection.length > 1) {
          const del_row = selection.shift()
          this.$refs.dtable.toggleRowSelection(del_row, false)
        }
      }
      this.$emit('select', { selection, row })
    },
    handleSelectAll(selection) {
      if (this.isRadio) {
        if (selection.length > 1) {
          selection.length = 1
        }
      }
      this.$emit('selectAll', selection)
    },
    clearSelection() {
      this.$refs.dtable.clearSelection()
    },
    toggleRowSelection(row, selected) {
      this.$refs.dtable.toggleRowSelection(row, selected)
    }
  }
}

// 可用统计方法
const VALID_SUM_METHODS = {
  sum: {
    label: '总',
    func: vals => {
      let sum = 0
      vals.forEach(v => (sum += Number(v)))
      return sum.toFixed(2)
    }
  },
  avg: {
    label: '均',
    func: vals => {
      let sum = 0
      vals.forEach(v => (sum += Number(v)))
      return (sum / vals.length).toFixed(2)
    }
  }
}
</script>
<style lang="scss" scoped>
.pagination {
  margin-top: 24px;
  margin-bottom: 10px;
  flex-shrink: 0;
  ::v-deep li:not(.disabled).active {
    background: #ffffff !important;
    color: #298dff !important;
    cursor: default;
    border: 1px solid #298dff;
    border-radius: 4px;
  }
}
.custom-pagination {
  position: relative;
  overflow: hidden;
  ::v-deep .el-pagination__sizes {
    .el-select {
      margin-top: -2px;
    }
  }
  .checked-count {
    float: left;
    position: relative;
    top: 12px;
  }
}
</style>
<style lang="scss">
// ele 的 scrollBarWidth 计算方法计算全局的滚动条大小，所以采用全局滚动条配置
// 否则单独配置容器内，会导致 fixed 列时出现高宽度 bug
.dynamic-table {
  display: flex;
  flex-direction: column;
  height: 100%;
  ::-webkit-scrollbar {
    width: 14px !important;
    height: 14px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 6px;
    // box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #bbb !important;
  }
  // ::-webkit-scrollbar-track {
  //   box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.1);
  //   border-radius: 4px;
  //   background: #ededed;
  // }
  .table-wrapper {
    /* height: 300px; */
    overflow: auto;
  }
  .cell {
    position: initial !important;
  }
  .custom-filter {
    background: #fff;
    position: absolute;
    width: 100%;
    left: 0;
    bottom: -8px;
    padding: 6px;
    text-align: center;
    border-top: 1px solid #dfe6ec;
    height: 52px;
    white-space: nowrap;
    overflow: hidden;
  }
}
.dynamic-table {
  .cell .el-input--prefix .el-input__inner {
    padding-left: 30px;
  }
  .el-table {
    border-bottom: none;
    border: solid 1px #d9dbe2;
    border-radius: 5px;
    thead {
      color: rgba(34, 40, 52, 0.65);
    }
  }
  .el-table .warning-row {
    background: oldlace !important;
  }
  .fixed-search .el-button--primary {
    background-color: #58b4b4;
    border-color: #58b4b4;
  }
  .fixed-search .el-button--primary:hover {
    background-color: #58b4b4bb;
    border-color: #58b4b4bb;
  }
  .el-table .custom-filter input:hover,
  .el-table .custom-filter select:hover {
    border: 1px solid rgba(88, 180, 180, 0.6);
  }
  .el-table .custom-filter input:focus,
  .el-table .custom-filter select:focus {
    border: 1px solid rgba(88, 180, 180, 1);
  }
}
.dynamic-table.head-search .el-table__header th .cell {
  padding-bottom: 46px;
  line-height: 35px;
}
.dynamic-table.head-search .el-table__header th {
  padding-bottom: 0;
}
.dtb-cell-nowrap {
  white-space: nowrap;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  .cell-span {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.is-center > .cell > .dtb-cell-nowrap {
  justify-content: center;
}
</style>
