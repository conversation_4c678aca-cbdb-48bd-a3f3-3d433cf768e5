<template>
  <el-button-group class="btn-group-container">
    <el-button
      v-for="(item) in options"
      :key="item.value"
      :size="size"
      :class="[item.value === value ? '' : 'z-btn']"
      :type="item.value === value ? type : ''"
      @click="$emit('click',item.value)"
    >{{ item.label }}
    </el-button>
  </el-button-group>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'click'
  },
  props: {
    value: {
      type: [String, Number, Boolean],
      default: ''
    },
    options: {
      type: Array,
      default: () => [{
        label: '是',
        value: true
      }, {
        label: '否',
        value: false
      }]
    },
    size: {
      type: String,
      default: 'small'
    },
    type: {
      type: String,
      default: 'primary'
    }
  },
  watch: {
    value(newVal) {
      this.$emit('change', newVal)
    }
  }
}
</script>

<style scoped>
.z-btn:active,.z-btn:hover{
  color: rgba(34, 40, 52, 0.65);
  border-color:  #D0D3DB;
  background-color:  unset;
}
</style>
