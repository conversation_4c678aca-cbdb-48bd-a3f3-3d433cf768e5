<template>
  <div class="flow-attr-box" style="height: 100%;">
    <el-tabs :value="activeKey" size="small" style="height: 100%;">
      <el-tab-pane disabled name="flow-attr">
        <span slot="label">
          <i class="el-icon-s-operation" />
          流程属性
        </span>
        <el-form layout="horizontal">
          <el-form-item
            :label-col="formItemLayout.labelCol"
            :wrapper-col="formItemLayout.wrapperCol"
            label="流程id"
            size="small"
          >
            <el-input :value="flowData.attr.id" disabled />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane disabled name="node-attr">
        <span slot="label">
          <i class="el-icon-notebook-2" />
          节点属性
        </span>
        <template v-if="currentSelect && currentSelect.id">
          <el-form ref="dataForm" :model="currentSelect" :rules="rules">
            <el-form-item :label="'Id'" prop="id" size="small">
              <el-input v-model="currentSelect.id" disabled readonly />
            </el-form-item>

            <el-form-item size="small">

              <el-checkbox v-model="currentSelect.isadd">是否加签</el-checkbox>
              <el-checkbox v-model="currentSelect.mustadd">必须加签</el-checkbox>
              <el-checkbox v-model="currentSelect.Is_CallBack">是否自动回调</el-checkbox>
            </el-form-item>
            <el-form-item :label="'编号'" prop="Code" size="small">
              <el-input v-model="currentSelect.Code" placeholder="非开发人员可不填"/>
            </el-form-item>
            <el-form-item :label="'名称'" prop="name" size="small">
              <el-input v-model="currentSelect.name" />
            </el-form-item>
            <template v-if="currentSelect.setInfo">
              <el-form-item :label="'三方回调URL'" prop="ThirdPartyUrl" size="small">
                <el-input v-model="currentSelect.setInfo.ThirdPartyUrl" />
              </el-form-item>
              <el-form-item :label="'执行权限'" prop="NodeDesignate" size="small">
                <el-select
                  v-model="currentSelect.setInfo.NodeDesignate"
                  :popper-append-to-body="false"
                  class="filter-item"
                  placeholder="请选择"
                  style="width:100%"
                  @change="handleChangeRoles"
                >
                  <el-option
                    v-for="item in NodeDesignates"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                v-if="currentSelect.setInfo.NodeDesignate ==='SPECIAL_USER'"
                :label="'指定用户'"
                prop="NodeDesignateUsers"
                size="small"
              >
                <select-users
                  :group-names.sync="currentSelect.setInfo.NodeDesignateData.groupText"
                  :user-names.sync="currentSelect.setInfo.NodeDesignateData.usersText"
                  :role-names.sync="currentSelect.setInfo.NodeDesignateData.rolesText"
                  :department-names.sync="currentSelect.setInfo.NodeDesignateData.departmentsText"
                  :group="currentSelect.setInfo.NodeDesignateData.group"
                  :users="currentSelect.setInfo.NodeDesignateData.users"
                  :roles="currentSelect.setInfo.NodeDesignateData.roles"
                  :departments="currentSelect.setInfo.NodeDesignateData.departments"
                  @users-change="usersChange"
                  @roles-change="rolesChange"
                  @group-change="groupChange"
                  @departments-change="departmentsChange"
                />
              </el-form-item>

              <!--              <el-form-item-->
              <!--                v-if="currentSelect.setInfo.NodeDesignate ==='SPECIAL_ROLE'"-->
              <!--                :label="'指定角色'"-->
              <!--                prop="NodeDesignateUsers"-->
              <!--                size="small"-->
              <!--              >-->
              <!--                <select-roles-->
              <!--                  :roles="currentSelect.setInfo.NodeDesignateData.roles"-->
              <!--                  :user-names.sync="currentSelect.setInfo.NodeDesignateData.Texts"-->
              <!--                  @roles-change="rolesChange"-->
              <!--                />-->
              <!--              </el-form-item>-->

              <!--              <el-form-item v-if="currentSelect.setInfo.NodeDesignate ==='SUBMIT_DEPARTMENT'">-->
              <!--                -->
              <!--              </el-form-item>-->

              <el-form-item
                v-if="currentSelect.type==='fork'"
                :label="'会签类型'"
                prop="NodeConfluenceType"
                size="small"
              >
                <el-select
                  v-model="currentSelect.setInfo.NodeConfluenceType"
                  :popper-append-to-body="false"
                  class="filter-item"
                  placeholder="请选择"
                  style="width:100%"
                >
                  <el-option
                    v-for="item in NodeConfluenceTypes"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-form>
        </template>
      </el-tab-pane>
      <el-tab-pane disabled name="link-attr">
        <span slot="label">
          <i class="el-icon-share" />
          连线属性
        </span>
        <el-form :label-position="'top'">
          <el-form-item
            :label-col="formItemLayout.labelCol"
            :wrapper-col="formItemLayout.wrapperCol"
            label="id"
            size="small"
          >
            <el-input :value="currentSelect.id" disabled />
          </el-form-item>
          <el-form-item
            :label-col="formItemLayout.labelCol"
            :wrapper-col="formItemLayout.wrapperCol"
            label="源节点"
            size="small"
          >
            <el-input :value="currentSelect.from" disabled />
          </el-form-item>
          <el-form-item
            :label-col="formItemLayout.labelCol"
            :wrapper-col="formItemLayout.wrapperCol"
            label="目标节点"
            size="small"
          >
            <el-input :value="currentSelect.to" disabled />
          </el-form-item>
          <el-form-item
            :label-col="formItemLayout.labelCol"
            :wrapper-col="formItemLayout.wrapperCol"
            label="文本"
            size="small"
          >
            <el-input v-model="currentSelect.label" @change="linkLabelChange" />
          </el-form-item>
          <el-form-item
            :label-col="formItemLayout.labelCol"
            :wrapper-col="formItemLayout.wrapperCol"
            label="表单字段条件"
            size="small"
            style="display: inline-block;"
          >
            <div v-for="(compare, index) in Compares" :key="index" style="margin-bottom: 5px;">
              <el-select
                v-model="compare.FieldName"
                :popper-append-to-body="false"
                placeholder="请选择"
                style="width: 110px;"
              >
                <el-option
                  v-for="item in formTemplate"
                  :key="item.Value"
                  :label="item.Text"
                  :value="item.Value"
                />
              </el-select>
              <el-select
                v-model="compare.Operation"
                :disabled="!compare.FieldName"
                :popper-append-to-body="false"
                placeholder="请选择"
                style="width: 85px;"
              >
                <el-option label=">" value=">" />
                <el-option label=">=" value=">=" />
                <el-option label="<" value="<" />
                <el-option label="<=" value="<=" />
                <el-option label="=" value="=" />
                <el-option label="!=" value="!=" />
              </el-select>
              <el-input
                v-model="compare.Value"
                :disabled="!compare.FieldName"
                placeholder="值"
                style="width: 80px;"
              />

              <el-button
                v-if="index === 0"
                icon="el-icon-plus"
                size="mini"
                title="并且"
                type="primary"
                style="margin-left: 5px"
                @click="btnAddCompare"
              />
              <el-button
                v-if="index !== 0"
                icon="el-icon-delete"
                size="mini"
                title="删除"
                type="danger"
                @click="btnDelCompare(index)"
              />
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// import jsplumb from 'jsplumb'
import SelectUsers from '@/components/SelectUsers'
// import SelectRoles from '@/components/SelectRoles'
import { mapGetters, mapActions } from 'vuex'

export default {
  components: {
    // jsplumb,
    SelectUsers
    // SelectRoles
  },
  props: ['plumb', 'flowData', 'formTemplate'],
  data() {
    return {
      currentSelect: '',
      Compares: '',
      flag: false,
      formItemLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 16 }
      },
      NodeConfluenceTypes: [
        { id: 'all', name: '全部通过' },
        { id: 'one', name: '至少一个通过' }
      ],
      NodeDesignates: [
        { id: 'SPECIAL_USER', name: '审核者' },
        { id: 'ALL_USER', name: '所有人' },
        // { id: 'SPECIAL_ROLE', name: '指定角色' },
        { id: 'SUBMIT_DEPARTMENT', name: '发起者部门领导' }
      ],
      NodeRejectTypes: [
        { id: '0', name: '前一步' },
        { id: '1', name: '第一步' },
        { id: '2', name: '某一步' },
        { id: '3', name: '用户指定' },
        { id: '4', name: '不处理' }
      ],
      rules: {
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }]
      },
      activeKey: 'flow-attr'
    }
  },
  computed: {
    ...mapGetters({
      currentSelectData: 'currentSelect'
    })
  },
  mounted() {
    this.currentSelect = Object.assign({}, this.currentSelectData)
    this.Compares = this.currentSelect.Compares
  },
  methods: {
    ...mapActions({
      saveCurrentSelect: 'saveCurrentSelect'
    }),
    nodeNameChange(e) {
      this.currentSelect.name = e.target.value
    },
    btnAddCompare() {
      this.Compares.push({
        FieldName: '',
        Operation: '',
        Value: ''
      })
    },
    btnDelCompare(e) {
      this.Compares.splice(e, 1)
    },
    linkLabelChange(value) {
      const that = this
      const label = value

      that.currentSelect.label = label
      const conn = that.plumb.getConnections({
        source: that.currentSelect.from,
        target: that.currentSelect.to
      })[0]
      if (label !== '') {
        conn.setLabel({
          label: label,
          cssClass: 'linkLabel'
        })
      } else {
        const labelOverlay = conn.getLabelOverlay()
        if (labelOverlay) conn.removeOverlay(labelOverlay.id)
      }
    },
    groupChange(name, val) {
      // 可执行用户
      this.currentSelect.setInfo.NodeDesignateData[name] = val
    },
    usersChange(name, val) {
      // 可执行用户
      this.currentSelect.setInfo.NodeDesignateData[name] = val
    },
    rolesChange(name, val) {
      // 可执行角色
      this.currentSelect.setInfo.NodeDesignateData[name] = val
    },
    departmentsChange(name, val) {
      // 部门
      this.currentSelect.setInfo.NodeDesignateData[name] = val
    },
    handleChangeRoles(v) {
      this.currentSelect.setInfo.NodeDesignateData.Texts = ''
      this.currentSelect.setInfo.NodeDesignateData.roles = []
      this.currentSelect.setInfo.NodeDesignateData.group = []
      this.currentSelect.setInfo.NodeDesignateData.users = []
      this.currentSelect.setInfo.NodeDesignateData.departments = []
      this.currentSelect.setInfo.NodeDesignateData.departmentsText = ''
      this.currentSelect.setInfo.NodeDesignateData.groupText = ''
      this.currentSelect.setInfo.NodeDesignateData.usersText = ''
      this.currentSelect.setInfo.NodeDesignateData.rolesText = ''
    }
  },
  watch: {
    currentSelectData: {
      deep: true,
      handler(val) {
        if (this.flag) {
          this.flag = false
          return
        }
        this.currentSelect = Object.assign({}, { ...val })
        if (this.currentSelectData.type === 'sl') {
          this.activeKey = 'link-attr'
        } else if (!this.currentSelectData.type) {
          this.activeKey = 'flow-attr'
        } else {
          this.activeKey = 'node-attr'
        }
      }
    },
    currentSelect: {
      handler(val) {
        this.Compares = val.Compares
        this.saveCurrentSelect(val)
        this.flag = true
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/flow-attr.scss';

.flow-attr-box {
  ::v-deep {
    .el-tabs__header {
      margin-bottom: 0;
    }

    .el-tabs__item.is-active {
      color: #409EFF !important;
    }

    .el-tabs__item.is-disabled {
      color: #333;
    }

    .el-tabs__content {
      height: calc(100% - 40px);
      overflow: auto;
      padding: 0 10px;
    }
  }
}
</style>
