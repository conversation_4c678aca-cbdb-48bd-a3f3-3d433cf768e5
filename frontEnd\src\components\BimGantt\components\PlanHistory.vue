<template>
  <div class="plan-history">
    <div class="scroll">
      <ul>
        <template v-for="(h, i) in histories">
          <li :key="h.Id" @click="view(h)">
            <span>{{ i + 1 }}</span>
            <div>{{ moment(h.Data_Date).format('YYYY-MM-DD') }}</div>
            <i class="el-icon-arrow-right" />
          </li>
        </template>
      </ul>
      <div
        v-if="histories.length <= 0"
        style="text-align:center;border:1px solid #F2F2F2;color:#999;padding:8px;"
      >
        暂无数据
      </div>
    </div>
  </div>
</template>
<script>
import { GetPlanUpdateList } from '@/api/plan/index'
import * as moment from 'moment'
export default {
  name: 'PlanHistory',
  props: {
    plan: {
      type: Object,
      default: () => ({})
    },
    begin_date: {
      type: String,
      default: ''
    },
    end_date: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      histories: []
    }
  },
  created() {
    GetPlanUpdateList(this.plan.Id).then(res => {
      if (res.IsSucceed) {
        this.histories = res.Data
      }
    })
  },
  methods: {
    moment(v) {
      return moment(v)
    },
    view(ver) {
      this.$router.push({
        path: `/plan/history/${ver?.Id}`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.plan-history {
  height: 100%;
  padding: 0;
  display: flex;
  flex-direction: column;
  flex-flow: column;
  .scroll {
    margin: 0 10px;
    padding: 10px;
    height: 0;
    flex: auto;
    overflow: auto;
    > ul {
      display: block;
    }
    li {
      margin: 8px 0;
      padding: 5px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      display: flex;
      flex-direction: row;
      align-items: center;
      > span {
        padding: 0 12px;
        font-style: italic;
        font-size: 1.2em;
        font-weight: bold;
        color: #298dff;
      }
      > div {
        flex: 1;
      }
      &:hover {
        background: #fcfcfc;
        cursor: pointer;
      }
    }
  }
}
</style>
