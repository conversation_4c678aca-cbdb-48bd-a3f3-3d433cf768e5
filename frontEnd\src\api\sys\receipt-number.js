import request from '@/utils/request'

export function SaveBillSetup(data) {
  return request({
    method: 'post',
    url: '/Platform/BillSetup/SaveBillSetup',
    data
  })
}

export function GetBillSetupEntity(data) {
  return request({
    method: 'post',
    url: '/Platform/BillSetup/GetBillSetupEntity',
    data
  })
}

export function GetBillSetupPageList(data) {
  return request({
    method: 'post',
    url: '/SYS​/BillSetup​/GetBillSetupPageList',
    data
  })
}

export function GetBillSetupList(data) {
  return request({
    method: 'post',
    url: '/Platform/BillSetup/GetBillSetupList',
    data
  })
}
// 恢复初始配置
export function RecoveryBillSetup(data) {
  return request({
    method: 'post',
    url: '/Platform/BillSetup/RecoveryBillSetup',
    data,
  })
}
