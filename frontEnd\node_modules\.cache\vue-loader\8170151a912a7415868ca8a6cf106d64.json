{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\index.vue?vue&type=style&index=0&id=2017323a&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\gas\\index.vue", "mtime": 1754613278767}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5jb250YWluZXJCb3ggewogIC5lbC1jb2wgewogICAgbWFyZ2luLWJvdHRvbTogMTZweDsKICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiqBA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis/gas", "sourcesContent": ["<template>\n  <el-row v-loading=\"loading\" :gutter=\"20\" class=\"containerBox\">\n    <template v-for=\"(item, index) in componentsData\">\n      <el-col :key=\"`gasUsed-${index}`\" :span=\"10\">\n        <gasUsed :custom-gas-used-config=\"item\" :is-photovoltaic=\"Is_Photovoltaic\" />\n      </el-col>\n      <el-col :key=\"`barChat-${index}`\" :span=\"14\">\n        <barChat\n          :custom-bar-chat-config=\"item\"\n          @radioChange=\"radioChange\"\n          @gasFlow=\"gasFlow\"\n        />\n      </el-col>\n    </template>\n    <gasFlowDialog\n      ref=\"gasFlowDialog\"\n      :custom-bar-chat-config=\"dialogObj\"\n      @radioChangeDialog=\"gasFlow\"\n    />\n  </el-row>\n</template>\n\n<script>\nimport gasUsed from './components/gasUsed'\nimport barChat from './components/barChat'\nimport gasFlowDialog from './components/dialog'\nimport {\n  GetGasTimePeriodDosageBarDiagram,\n  GetGasEachNodeDosageTreeDiagram,\n  GetGasLiquidPercent\n} from '@/api/business/pJEnergyAnalysis'\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\n\nexport default {\n  components: {\n    gasUsed,\n    barChat,\n    gasFlowDialog\n  },\n  props: {\n    componentsConfig: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  data() {\n    return {\n      componentsData: [\n        {\n          baseData: {\n            title: '氧气',\n            color: '#00A8FE',\n            unit: '吨',\n            Total: 0,\n            DataType: '全部'\n          },\n          gasData: {\n            tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',\n            showTotal: true,\n            fillHeight: '100%',\n            residue: {\n              code: 'Oxygen',\n              value: 0,\n              percentage: '0%'\n            },\n            colData: [\n              {\n                title: '一车间',\n                value: 0,\n                percentage: '80%',\n                iconName: 'workshop'\n              },\n              {\n                title: '二车间',\n                value: 0,\n                percentage: '60%',\n                iconName: 'workshop'\n              },\n              {\n                title: '配送中心',\n                value: 456,\n                percentage: '40%',\n                iconName: 'delivery'\n              }\n            ]\n          },\n          radioGroupData: [],\n          barData: {\n            tooltip: {\n              trigger: 'axis'\n            },\n            xAxis: {\n              type: 'category',\n              data: [],\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              }\n            },\n            grid: {\n              left: '2%',\n              right: '0%',\n              bottom: '10%',\n              // top: '20%',\n              containLabel: true\n            },\n            yAxis: {\n              type: 'value',\n              nameTextStyle: {\n                color: '#888888'\n              }\n            },\n            series: [\n              {\n                data: [],\n                type: 'bar',\n                symbol: 'emptyCircle',\n                barWidth: 10,\n                itemStyle: {\n                  color: '#00A8FE'\n                },\n                tooltip: {\n                  valueFormatter: function(value) {\n                    return value + ' 吨'\n                  }\n                }\n              }\n            ]\n          }\n        },\n        {\n          baseData: {\n            title: '二氧化碳',\n            color: '#3BC9FF',\n            unit: '吨',\n            Total: 0\n          },\n          gasData: {\n            tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',\n            showTotal: true,\n            fillHeight: '100%',\n            residue: {\n              code: 'Carbon',\n              value: 0,\n              percentage: '0%'\n            },\n            colData: [\n              {\n                title: '一车间',\n                value: 0,\n                percentage: '80%',\n                iconName: 'workshop'\n              },\n              {\n                title: '二车间',\n                value: 0,\n                percentage: '60%',\n                iconName: 'workshop'\n              },\n              {\n                title: '配送中心',\n                value: 456,\n                percentage: '40%',\n                iconName: 'delivery'\n              }\n            ]\n          },\n          radioGroupData: [],\n          barData: {\n            tooltip: {\n              trigger: 'axis'\n            },\n            xAxis: {\n              type: 'category',\n              data: [],\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              }\n            },\n            grid: {\n              left: '2%',\n              right: '0%',\n              bottom: '10%',\n              // top: '20%',\n              containLabel: true\n            },\n            yAxis: {\n              type: 'value',\n              nameTextStyle: {\n                color: '#888888'\n              }\n            },\n            series: [\n              {\n                data: [],\n                type: 'bar',\n                symbol: 'emptyCircle',\n                barWidth: 10,\n                itemStyle: {\n                  color: '#3BC9FF'\n                },\n                tooltip: {\n                  valueFormatter: function(value) {\n                    return value + ' 吨'\n                  }\n                }\n              }\n            ]\n          }\n        }\n        // {\n        //   baseData: {\n        //     title: '氩气',\n        //     unit: '吨',\n        //     color: '#A190FC',\n        //     Total: 0\n        //   },\n        //   gasData: {\n        //     tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',\n        //     showTotal: true,\n        //     fillHeight: '100%',\n        //     residue: {\n        //       code: 'Argon',\n        //       value: 0,\n        //       percentage: '0%'\n        //     },\n        //     colData: [\n        //       {\n        //         title: '一车间',\n        //         value: 0,\n        //         percentage: '80%',\n        //         iconName: 'workshop'\n        //       },\n        //       {\n        //         title: '二车间',\n        //         value: 0,\n        //         percentage: '60%',\n        //         iconName: 'workshop'\n        //       }\n        //     ]\n        //   },\n        //   radioGroupData: [],\n        //   barData: {\n        //     tooltip: {\n        //       trigger: 'axis'\n        //     },\n        //     xAxis: {\n        //       type: 'category',\n        //       data: [],\n        //       axisTick: {\n        //         show: false\n        //       },\n        //       axisLine: {\n        //         show: false\n        //       }\n        //     },\n        //     grid: {\n        //       left: '2%',\n        //       right: '0%',\n        //       bottom: '10%',\n        //       // top: '20%',\n        //       containLabel: true\n        //     },\n        //     yAxis: {\n        //       type: 'value',\n        //       nameTextStyle: {\n        //         color: '#888888'\n        //       }\n        //     },\n        //     series: [\n        //       {\n        //         data: [],\n        //         type: 'bar',\n        //         symbol: 'emptyCircle',\n        //         barWidth: 10,\n        //         itemStyle: {\n        //           color: '#A190FC'\n        //         },\n        //         tooltip: {\n        //           valueFormatter: function(value) {\n        //             return value + ' 吨'\n        //           }\n        //         }\n        //       }\n        //     ]\n        //   }\n        // },\n        // {\n        //   baseData: {\n        //     title: '丙烷',\n        //     unit: '立方',\n        //     color: '#F86161',\n        //     Total: 0\n        //   },\n        //   gasData: {\n        //     tooltip: '假定罐内气压约为1.6兆帕，温度约为-193度',\n        //     showTotal: false,\n        //     fillHeight: '100%',\n        //     residue: {\n        //       value: 0,\n        //       percentage: '80%'\n        //     },\n        //     colData: [\n        //       {\n        //         title: '一车间',\n        //         value: 0,\n        //         percentage: '80%',\n        //         iconName: 'workshop'\n        //       },\n        //       {\n        //         title: '二车间',\n        //         value: 0,\n        //         percentage: '60%',\n        //         iconName: 'workshop'\n        //       },\n        //       {\n        //         title: '配送中心',\n        //         value: 456,\n        //         percentage: '40%',\n        //         iconName: 'delivery'\n        //       }\n        //     ]\n        //   },\n        //   radioGroupData: [],\n        //   barData: {\n        //     tooltip: {\n        //       trigger: 'axis'\n        //     },\n        //     xAxis: {\n        //       type: 'category',\n        //       data: [],\n        //       axisTick: {\n        //         show: false\n        //       },\n        //       axisLine: {\n        //         show: false\n        //       }\n        //     },\n        //     grid: {\n        //       left: '2%',\n        //       right: '0%',\n        //       bottom: '10%',\n        //       // top: '20%',\n        //       containLabel: true\n        //     },\n        //     yAxis: {\n        //       type: 'value',\n        //       nameTextStyle: {\n        //         color: '#888888'\n        //       }\n        //     },\n        //     series: [\n        //       {\n        //         data: [],\n        //         type: 'bar',\n        //         symbol: 'emptyCircle',\n        //         barWidth: 10,\n        //         itemStyle: {\n        //           color: '#F86161'\n        //         },\n        //         tooltip: {\n        //           valueFormatter: function(value) {\n        //             return value + ' 立方'\n        //           }\n        //         }\n        //       }\n        //     ]\n        //   }\n        // }\n      ],\n      componentsDialogData: [\n        {\n          radioGroupData: [],\n          barData: {\n            tooltip: {\n              trigger: 'axis'\n            },\n            xAxis: {\n              type: 'category',\n              data: [],\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              }\n            },\n            grid: {\n              left: '2%',\n              right: '0%',\n              bottom: '10%',\n              // top: '20%',\n              containLabel: true\n            },\n            yAxis: {\n              type: 'value',\n              nameTextStyle: {\n                color: '#888888'\n              }\n            },\n            series: [\n              {\n                data: [],\n                type: 'bar',\n                symbol: 'emptyCircle',\n                barWidth: 10,\n                itemStyle: {\n                  color: '#00A8FE'\n                },\n                tooltip: {\n                  valueFormatter: function(value) {\n                    return value + ' 立方'\n                  }\n                }\n              }\n            ]\n          },\n          GasType: 1\n        },\n        {\n          radioGroupData: [],\n          barData: {\n            tooltip: {\n              trigger: 'axis'\n            },\n            xAxis: {\n              type: 'category',\n              data: [],\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              }\n            },\n            grid: {\n              left: '2%',\n              right: '0%',\n              bottom: '10%',\n              // top: '20%',\n              containLabel: true\n            },\n            yAxis: {\n              type: 'value',\n              nameTextStyle: {\n                color: '#888888'\n              }\n            },\n            series: [\n              {\n                data: [],\n                type: 'bar',\n                symbol: 'emptyCircle',\n                barWidth: 10,\n                itemStyle: {\n                  color: '#3BC9FF'\n                },\n                tooltip: {\n                  valueFormatter: function(value) {\n                    return value + ' 立方'\n                  }\n                }\n              }\n            ]\n          },\n          GasType: 2\n        },\n        {\n          radioGroupData: [],\n          barData: {\n            tooltip: {\n              trigger: 'axis'\n            },\n            xAxis: {\n              type: 'category',\n              data: [],\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              }\n            },\n            grid: {\n              left: '2%',\n              right: '0%',\n              bottom: '10%',\n              // top: '20%',\n              containLabel: true\n            },\n            yAxis: {\n              type: 'value',\n              nameTextStyle: {\n                color: '#888888'\n              }\n            },\n            series: [\n              {\n                data: [],\n                type: 'bar',\n                symbol: 'emptyCircle',\n                barWidth: 10,\n                itemStyle: {\n                  color: '#A190FC'\n                },\n                tooltip: {\n                  valueFormatter: function(value) {\n                    return value + ' 立方'\n                  }\n                }\n              }\n            ]\n          },\n          GasType: 3\n        }\n      ],\n      dialogObj: {},\n      loading: false,\n      Is_Photovoltaic: false\n    }\n  },\n  // inject: ['DateType', 'StartTime', 'EndTime'],\n  watch: {\n    componentsConfig: {\n      handler(nv, ov) {\n        console.log('watch')\n        this.fetchData()\n      }\n    }\n  },\n  async created() {\n    await this.getPreferenceSettingValue()\n    this.fetchData()\n    this.getGasLiquidPercent()\n  },\n  mounted() {\n\n  },\n  // computed: {\n  //   parentData() {\n  //     return {\n  //       DateType: this.DateType(),\n  //       StartTime: this.StartTime(),\n  //       EndTime: this.EndTime(),\n  //     }\n  //   }\n  // },\n  methods: {\n    async getPreferenceSettingValue() {\n      const res = await GetPreferenceSettingValue({ Code: 'Is_Photovoltaic' })\n      if (res && res.IsSucceed) {\n        this.Is_Photovoltaic = res.Data === 'true'\n        this.componentsData.splice(2)\n      }\n    },\n\n    async fetchData() {\n      const promises = ['1', '2', '3', '4'].map(async(i) => {\n        const [leftResData, rightResData] = await Promise.all([\n          this.getGasEachNodeDosageTreeDiagram({\n            ...this.componentsConfig,\n            GasType: i\n          }),\n          this.getGasTimePeriodDosageBarDiagram({\n            ...this.componentsConfig,\n            GasType: i,\n            IsTotalNode: true\n          })\n        ])\n\n        this.componentsData[i - 1].gasData.colData = leftResData.Nodes\n        // this.componentsData[i - 1].gasData.showTotal = true;\n        this.componentsData[i - 1].gasData.tooltip =\n          '假定罐内气压约为1.6兆帕，温度约为-193度'\n        this.componentsData[i - 1].baseData.Total = rightResData.Total\n        this.componentsData[i - 1].baseData.GasType = i\n        if (i !== 4) {\n          this.componentsDialogData[i - 1].GasType = i\n          this.componentsDialogData[i - 1].radioGroupData = [\n            '全部',\n            ...leftResData.Nodes.map((item) => item.Key)\n          ]\n        }\n        this.componentsData[i - 1].radioGroupData = [\n          '全部',\n          ...leftResData.Nodes.map((item) => item.Key)\n        ]\n\n        const xAxisData = (rightResData.List ?? []).map((item) => item.Key)\n        const seriesData = (rightResData.List ?? []).map((item) => item.Value)\n\n        this.componentsData[i - 1].barData.xAxis.data = xAxisData\n        this.componentsData[i - 1].barData.series[0].data = seriesData\n      })\n\n      await Promise.all(promises)\n    },\n\n    async getGasLiquidPercent(data) {\n      const res = await GetGasLiquidPercent(data)\n      this.componentsData.forEach((element) => {\n        if (res.Data.length > 0) {\n          const obj = res.Data.find(\n            (item) => element.gasData.residue.code === item.Code\n          )\n          element.gasData.residue = {\n            value: obj.Volume,\n            percentage: `${obj.Percent}%`\n          }\n          element.gasData.fillHeight = `${obj.Percent}%`\n        }\n      })\n    },\n    async getGasTimePeriodDosageBarDiagram(data) {\n      const res = await GetGasTimePeriodDosageBarDiagram(data)\n      return res.Data\n    },\n    async getGasEachNodeDosageTreeDiagram(data) {\n      const res = await GetGasEachNodeDosageTreeDiagram(data)\n      return res.Data\n    },\n    async radioChange(data) {\n      const i = data.GasType\n      const IsTotalNode = data.val === '全部'\n      const params = {\n        ...this.componentsConfig,\n        GasType: i,\n        IsTotalNode,\n        NodeName: data.val\n      }\n      if (data.val === '全部') {\n        delete params.NodeName\n      }\n      const rightResData = await this.getGasTimePeriodDosageBarDiagram(params)\n      const xAxisData = (rightResData.List ?? []).map((item) => item.Key)\n      const seriesData = (rightResData.List ?? []).map((item) => item.Value)\n      this.componentsData[i - 1].barData.xAxis.data = xAxisData\n      this.componentsData[i - 1].barData.series[0].data = seriesData\n      this.componentsData[i - 1].baseData.Total = rightResData.Total\n    },\n    async gasFlow(data) {\n      this.loading = true\n      this.$refs.gasFlowDialog.handleOpen()\n      const i = data.GasType\n      const IsTotalNode = data.val === '全部'\n      const params = {\n        ...this.componentsConfig,\n        GasType: i,\n        IsTotalNode,\n        NodeName: data.val,\n        IsCube: true\n      }\n      if (data.val === '全部') {\n        delete params.NodeName\n      }\n      const rightResData = await this.getGasTimePeriodDosageBarDiagram(params)\n      const xAxisData = (rightResData.List ?? []).map((item) => item.Key)\n      const seriesData = (rightResData.List ?? []).map((item) => item.Value)\n      this.componentsDialogData[i - 1].barData.xAxis.data = xAxisData\n      this.componentsDialogData[i - 1].barData.series[0].data = seriesData\n      this.dialogObj = this.componentsDialogData[i - 1]\n      this.loading = false\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.containerBox {\n  .el-col {\n    margin-bottom: 16px;\n  }\n}\n</style>\n"]}]}