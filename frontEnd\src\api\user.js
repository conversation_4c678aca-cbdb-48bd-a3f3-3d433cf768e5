import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/Platform/Login/Login',
    method: 'post',
    data
  })
}

export function getInfo(data) {
  return request({
    url: '/Platform/User/GetUserEntity',
    method: 'post',
    data
  })
}

export function logout() {
  return request({
    url: '/Platform/Login/OutLogin',
    method: 'post'
  })
}

// 获取授权内容 （groupId/workObjId/userId三者均为空则获取当前登录用户的对应roleType权限） (Auth)
export function RoleAuthorization(data) {
  return request({
    url: '/Platform/Role/RoleAuthorization',
    method: 'post',
    data
  })
}

// 获取最后一次登录的数据权限
export function UpdateLastWorkingObj(data) {
  return request({
    url: '/Platform/User/UpdateLastWorkingObj',
    method: 'post',
    data
  })
}
// 更新用户信息
export function UpdateUserInfo(data) {
  return request({
    url: '/Platform/User/UpdateUserInfo',
    method: 'post',
    data
  })
}
// 修改密码
export function UpdateUserPwd(data) {
  return request({
    url: '/Platform/User/UpdateUserPwd',
    method: 'post',
    data
  })
}
// 获取偏好设置
export function getConfigure(data) {
  return request({
    url: '/Platform/PreferenceSetting/GetPreferenceSettingValue',
    method: 'post',
    data
  })
}

// 用户注册申请-租户手机号码验证
export function TenantAuthentication(data) {
  return request({
    url: '/Platform/UserApplication/TenantAuthentication',
    method: 'post',
    data
  })
}

// 用户注册申请-用户申请新增
export function AddUserApplication(data) {
  return request({
    url: '/Platform/UserApplication/AddUserApplication',
    method: 'post',
    data
  })
}

// 用户注册申请-修改当前节点的审核人员
export function UpdateCurrentNodeNoCheck(data) {
  return request({
    url: '/Platform/FlowInstances/UpdateCurrentNodeNoCheck',
    method: 'post',
    data
  })
}

// 用户注册申请-修改下一级节点的审核人员
export function UpdateNextNodeNoCheck(data) {
  return request({
    url: '/Platform/FlowInstances/UpdateNextNodeNoCheck',
    method: 'post',
    data
  })
}

// 用户注册申请-修改当前节点的审核人员
export function GetFactoryList(data) {
  return request({
    url: '/Platform/UserApplication/GetFactoryList',
    method: 'post',
    data
  })
}

// 校验验证码
export function CheckSMS(data) {
  return request({
    url: '/Platform/User/CheckSMS',
    method: 'post',
    data
  })
}
// 发送验证码
export function SendRetrievePwdSMS(data) {
  return request({
    url: '/Platform/User/SendRetrievePwdSMS',
    method: 'post',
    data
  })
}
// 读取手机下用户账号列表
export function GetRetrieveUserList(data) {
  return request({
    url: '/Platform/User/GetRetrieveUserList',
    method: 'post',
    data
  })
}
// 忘记密码修改（租户/手机）
export function UpdatePwd(data) {
  return request({
    url: '/Platform/User/UpdatePwd',
    method: 'post',
    data
  })
}

// 查看是否已经映射到租户表
export function NeedSaveUserTenant(data) {
  return request({
    url: '/Master/TenantUser/NeedSaveUserTenant',
    method: 'post',
    data
  })
}

// 把用户中的账号映射到租户表中
export function SaveUserTenant(data) {
  return request({
    url: '/Platform/User/SaveUserTenant',
    method: 'post',
    data
  })
}

// 根据手机号获取用户的租户号
export function GetTenantCodeByMobile(data) {
  return request({
    url: '/Master/TenantUser/GetTenantCodeByMobile',
    method: 'post',
    data
  })
}

// 获取用户拥有的产品列表
export function GetUserProductList(data) {
  return request({
    url: '/Platform/SysProduct/GetUserProductList',
    method: 'post',
    data
  })
}

// 获取用户拥有的产品列表
export function UpdateUserPwdByAccount(data) {
  return request({
    url: '/Platform/User/UpdateUserPwdByAccount',
    method: 'post',
    data
  })
}
