{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\realVideo\\Screen.vue?vue&type=template&id=19275a03&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\realVideo\\Screen.vue", "mtime": 1754618172895}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1724304688265}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}