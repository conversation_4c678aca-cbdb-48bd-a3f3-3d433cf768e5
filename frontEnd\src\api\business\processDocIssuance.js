import request from '@/utils/request'
// 工艺下发模块

export function GetPageList(data) {
  return request({
    method: 'post',
    url: '/DF/TechnologyFile/GetPageList',
    data
  })
}

export function GetDetail(data) {
  return request({
    method: 'post',
    url: '/DF/TechnologyFile/GetDetail',
    data
  })
}

export function Distribute(data) {
  return request({
    method: 'post',
    url: '/DF/TechnologyFile/Distribute',
    data
  })
}

export function ExportData(data) {
  return request({
    method: 'post',
    url: '/DF/TechnologyFile/ExportData',
    data,
    responseType: 'blob'
  })
}
