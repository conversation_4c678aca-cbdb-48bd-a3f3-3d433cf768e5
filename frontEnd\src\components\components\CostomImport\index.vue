<template>
  <bimdialog
    dialog-title="导入"
    dialog-width="450px"
    :visible.sync="dialogVisible"
    @submitbtn="handleSubmit"
    @cancelbtn="handleClose"
    @handleClose="handleClose"
    hidebtn
  >
    <div class="box">
    <upload-excel ref="upload" :before-upload="beforeUpload" :limit="2" :file-list="fileList" :on-change="onChange" accept=".xlsx" />
    <footer class="cs-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button
        type="primary"
        :loading="btnLoading"
        @click="handleSubmit"
      >确 定</el-button>
    </footer>
  </div>
  </bimdialog>
</template>
<script>
import bimdialog from '../dialog'
import UploadExcel from '@/components/UploadExcel'
export default {
  components: {
    UploadExcel,
    bimdialog
  },
  props: {
    componentsConfig: {
      type: Object,
      default: () => { }
    },
    componentsFuns: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      btnLoading: false,
      fileList: [],
      dialogVisible:false,
    }
  },
  mounted() {
  },
  methods: {
    handleOpen(type){
      this.dialogVisible=true
      this.type=type
    },
    onChange(file, fileList) {
      console.log(fileList)
      this.fileList = fileList.slice(-1)
      // if(fileList.length>1) {
      //   this.attachments.splice(-1)
      //   this.form.Doc_File = ''
      //   this.form.Doc_Content = ''
      //   this.form.Doc_Title = ''
      // }
    },
    async beforeUpload(file) {

      const fileFormData = new FormData()
      fileFormData.append('file', file)
      this.btnLoading = true
      console.log(file,'file')
      console.log(fileFormData,'fileFormData')
      await this.importData(fileFormData, this.componentsConfig.interfaceName)
    },
    handleSubmit() {
      this.$refs.upload.handleSubmit()
    },
    closeLoading() {
      this.btnLoading = false
    },
    handleClose() {
      this.fileList=[]
      this.dialogVisible = false
    },
    importData(val, interfaceName) {
      console.log(val)
      interfaceName(val).then((res) => {
        console.log(res)
        if (res.IsSucceed) {
          this.$message({
            type: 'success',
            message: '导入成功'
          })
          this.$emit('success')
          this.handleClose()
        } else {
          if (res.Data) {
            this.$message({
              type: 'error',
              message: res.Message
            })
            const file = res.Data.FileContents
            var byteCharacters = atob(file)
            var byteNumbers = new Array(byteCharacters.length)
            for (var i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i)
            }
            var byteArray = new Uint8Array(byteNumbers)

            var blob = new Blob([byteArray], { type: 'application/vnd.ms-excel' })
            var url = URL.createObjectURL(blob)
            var link = document.createElement('a')
            link.href = url
            link.download = res.Data.FileDownloadName
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)

            // 清除临时下载链接
            URL.revokeObjectURL(url)
            this.handleClose()
            this.componentsFuns.closeAndFresh()
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
            this.handleClose()
          }
        }
      }).finally(() => {
        this.closeLoading()
      })
    },
     resetForm() {
      this.componentsFuns.closeAndFresh()
    },
  }
}
</script>

  <style lang="scss" scoped>
.upload-box {
  background-color: #f7f8f9;
  border: 1px dashed #d9dbe2;
  margin-top: 16px;
  padding: 16px;
}

.cs-footer {
  margin-top: 10px;
  text-align: center;
}
</style>
