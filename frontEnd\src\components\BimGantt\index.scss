ul.gantt-leggend {
  padding: 0;
  list-style: none;
  li {
    display: flex;
    flex-direction: row;
    margin: 4px 0;
    span {
      flex: 1;
      margin: 2px 0;
    }
    label {
      flex: 1.6;
      font-weight: normal;
      text-align: left;
      padding-left: 12px;
    }
    span.rect {
      background: #dcf8f7;
      border: 1px solid #85e6e3;
    }
    span.rect.project {
      background: #d6eafc;
      border: 1px solid #70b6f5;
    }
    span.rect.actual {
      background: rgba(38, 210, 204, 0.32);
      border: 1px solid #70b6f5;
    }
    span.rect.project.actual {
      background: rgba(0, 126, 237, 0.32);
      border: 1px solid #70b6f5;
    }
    span.rect.key {
      background: #e63030;
      border: 1px solid #9d3a3a;
    }
    span.rect.target {
      background: rgba(255, 194, 72, 0.6);
      border: 1px solid rgba(255, 194, 72, 0.8);
    }
    span.diamond {
      display: flex;
      flex-direction: row;
      justify-content: center;
    }
    span.diamond {
      i {
        display: inline-block;
        transform: rotate(45deg);
        background: #ff99ad;
        width: 14px;
        height: 14px;
        flex-shrink: 0;
        border: 1px solid #ff0034;
      }
      &.target {
        i {
          background: rgba(255, 194, 72, 0.6);
          border: 1px solid rgba(255, 194, 72, 0.8);
        }
      }
    }
  }
}
.bim-gantt {
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    // box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ddd;
  }
  ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background: #ededed;
  }
  .el-badge {
    .el-link {
      color: rgba(34, 40, 52, 0.45);
    }

    &.hasfilter {
      .el-link {
        color: #298dff;
      }
    }
  }
  .el-badge__content.is-fixed.is-dot {
    top: 10px;
    right: 8px;
  }
  $toolbarHeight: 56px;

  height: 100%;
  position: relative;
  border-radius: 4px;
  border: 1px solid #e6ebf5;
  background-color: #ffffff;
  > header {
    height: $toolbarHeight;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    .el-row {
      flex: auto;
    }
    .proj-title {
      display: flex;
      padding-left: 20px;
      flex-direction: row;
      align-items: center;
      .flag {
        width: 24px;
        height: 24px;
        border: 1px solid #ededed;
        margin-right: 12px;
        text-align: center;
        line-height: 24px;
      }
      h3 {
        padding: 0;
        margin: 0;
        font-size: 1.4em;
      }
      .set-icon {
        font-size: 1.2em;
        margin-left: 12px;
      }
    }
  }
  > .toolbar {
    height: $toolbarHeight;
    position: relative;
    z-index: 1;
    background: #f6f6f6;
    .flex-toolbar {
      height: 56px;
      display: flex;
      flex-direction: row;
      align-items: center;
      .toolbar-group {
        flex: auto;
        padding: 0 12px;
        display: flex;
        flex-direction: row;
        height: 28px;
        border-right: 1px solid #eee;
        align-items: center;
        justify-content: center;
        .tool-item {
          flex: auto;
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 0 4px;
          > *:nth-child(odd) {
            margin: 0 14px 0 0;
          }
          .el-link:hover {
            background: #fff;
          }
          > span {
            font-size: 13px;
            color: #bbb;
          }
        }
      }
      .toolbar-group:last-child {
        border: none;
      }
    }
    .gantt-toolbar {
      flex-shrink: 0;
      height: 56px;
      .grouparea {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-around;
        border-right: 1px solid #eee;
        margin-top: 12px;
        height: 32px;
        &:last-child {
          border-right: none;
        }
        .el-link {
          padding: 4px;
        }
        .el-link:hover {
          background: #fff;
        }
        > span {
          font-size: 13px;
          color: #bbb;
        }
      }
    }
  }
  > .gantt-container {
    position: absolute;
    padding-top: 2 * $toolbarHeight;
    top: 0;
    height: 100%;
    width: 100%;
  }
  #gantt-chart {
    height: 100%;
    &.has-bottom-drawer {
      height: calc(100% - 250px);
    }
    .gantt_overlay_area {
      pointer-events: initial;
    }
    &.hasfowardline {
      .gantt_overlay_area {
        pointer-events: none;
      }
    }
    &.hasbaseline .gantt_task_line,
    &.hasbaseline .gantt_line_wrapper {
      margin-top: -10px;
    }
    &.hasbaseline .gantt_task_link .gantt_link_arrow {
      margin-top: -12px;
    }
    .baseline {
      position: absolute;
      border-radius: 2px;
      opacity: 0.6;
      margin-top: -6px;
      height: 16px;
      background: rgba(255, 194, 72, 0.6);
      border: 1px solid rgba(255, 194, 72, 0.8);
      > span {
        display: block;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        background: rgb(236, 159, 16);
        border-right: 1px solid rgb(235, 132, 42);
      }
    }
    .basediamond {
      position: absolute;
      margin-top: -5px;
      margin-left: -7px;
      transform: rotate(45deg);
      width: 14px;
      height: 14px;
      overflow: hidden;
      background: rgba(255, 194, 72, 0.6);
      border: 1px solid rgba(255, 194, 72, 0.8);
    }
    .gantt_row_task {
      .gantt_cell {
        border-right: 1px solid #ddd;
        &:last-child {
          border-right: none;
        }
      }
    }
    .gantt_row.spotted,
    .gantt_task_row.spotted {
      background: rgba(255, 198, 28, 0.1);
    }
    .gantt_row.gantt_selected {
      background: #fff3a1;
    }
    .gantt_row_project {
      background: rgba(241, 241, 244, 1);
      &.spotted {
        background: #f7f5ea;
      }
    }
    #wbs2task {
      position: relative;
      left: 10px;
      span.tag {
        display: inline-block;
        background: #ccc;
        height: 12px;
        width: 32px;
        border-radius: 6px;
        margin-right: 24px;
        position: absolute;
        left: 10px;
        top: 22px;
        cursor: pointer;
        span {
          display: inline-block;
          width: 20px;
          height: 20px;
          border-radius: 12px;
          position: absolute;
          top: -4px;
          z-index: 99;
          font-size: 10px;
          line-height: 20px;
          text-align: center;
          color: #fff;
        }
        &.task span {
          left: -4px;
          background: #999;
        }
        &.wbs span {
          right: -4px;
          background: #298dff;
        }
      }
    }
    .spot-marker {
      // width: 100px !important;
      position: relative;
      background: rgba(255, 198, 28, 0.1);
      resize: horizontal;
      cursor: move;
      .gantt_marker_content {
        color: rgba(255, 255, 255, 0);
        background: none;
      }
      .l-bar,
      .r-bar {
        position: absolute;
        width: 16px;
        height: 100%;
        cursor: ew-resize;
        top: 0;
        z-index: 1;
      }
      .l-bar {
        left: 0;
        border-left: 2px dotted rgba(255, 198, 28, 0.5);
      }
      .r-bar {
        right: 0;
        border-right: 2px dotted rgba(255, 198, 28, 0.5);
      }
    }
    .custom-gantt-bar-milestone {
      background: #ff99ad;
      border-color: #ff0034;
    }
    .custom-gantt-bar-project {
      background: #d6eafc;
      border: 1px solid #70b6f5;
      .lbag,
      .rbag {
        background: none;
        display: inline-block;
        width: 8px;
        height: 8px;
        position: absolute;
        z-index: 1;
        bottom: -8px;
        font-size: 0;
      }
      .lbag {
        left: -1px;
        width: 0;
        height: 0;
        border-top: 8px solid rgba(0, 126, 237, 0.32);
        border-right: 8px solid transparent;
      }
      .rbag {
        right: -1px;
        width: 0;
        height: 0;
        border-top: 8px solid rgba(0, 126, 237, 0.32);
        border-left: 8px solid transparent;
      }
      .gantt_task_content {
        color: #004f94;
      }
      &.gantt_selected {
        box-shadow: 0 0 5px #70b6f5;
      }
      .gantt_task_progress {
        background: rgba(0, 126, 237, 0.32);
      }
    }
    .custom-gantt-bar-task {
      background: #dcf8f7;
      border: 1px solid #85e6e3;
      .gantt_task_content {
        color: #00807b;
      }
      &.gantt_selected {
        box-shadow: 0 0 5px #85e6e3;
      }
      .gantt_task_progress {
        background: rgba(38, 210, 204, 0.32);
      }
    }
    .custom-gantt-row-project {
      background: rgba(241, 241, 244, 1);
    }
    .gantt_bar_task.gantt_critical_task {
      background-color: #e63030;
      border-color: #9d3a3a;
      .gantt_task_content {
        color: #fff;
      }
      .gantt_task_progress {
        background: #a2edea;
      }
    }
    .gantt_task_content {
      display: flex;
      overflow: visible;
      justify-content: center;
    }
  }
  .gantdrawer {
    position: fixed;
    bottom: -15px !important;
    top: initial;
    width: initial;
    left: initial;
    box-shadow: #ccc 0px 0px 12px;
    &.el-drawer-fade-leave-active,
    &.el-drawer-fade-enter-active {
      box-shadow: none;
    }
    &.taskdetail,
    &.analyzer-detail {
      position: absolute;
      left: 0px;
      bottom: -15px !important;
    }

    &.history {
      position: fixed;
      bottom: 0 !important;
      left: initial;
    }
    .el-drawer__container {
    }
    .el-drawer__body {
    }
    .task-detail {
      height: 100%;
      position: relative;
      .closeme {
        position: absolute;
        right: 20px;
        top: 14px;
        cursor: pointer;
        z-index: 1;
      }
      .el-tabs {
        height: 100%;
        display: flex;
        flex-direction: column;
        flex-flow: column;
        .el-tabs__header {
          border-bottom: none;
        }
        .el-tabs__content {
          flex: auto;
          .el-tab-pane {
            height: 100%;
            > .el-row {
              height: 100%;
              > .el-col {
                height: 100%;
              }
            }
          }
          .el-form-item.el-form-item--mini {
            margin-bottom: 12px;
            &:first-child {
              margin-top: 4px;
            }
            label {
              font-weight: normal;
            }
          }
        }
        &.el-tabs--border-card {
          box-shadow: none !important;
          border-left: none;
          border-right: none;
          border-bottom: none;
        }
        .el-tabs__item {
          outline: none;
          box-shadow: none !important;
          border-bottom-width: 2px;
          border-left: none;
          border-right: none;
          &:hover {
            color: rgba(34, 40, 52, 0.8);
          }
          &.is-active {
            color: rgba(34, 40, 52, 0.8);
            font-weight: bold;
            border-image: linear-gradient(
                90deg,
                rgba(255, 0, 0, 0) 0%,
                rgba(255, 0, 0, 0) 40%,
                #298dff 40%,
                #298dff 60%,
                rgba(255, 0, 0, 0) 60%,
                rgba(255, 0, 0, 0) 100%
              )
              30 30;
          }
        }
        .el-tabs__nav {
          margin-left: 0px;
        }
        .col-block {
          display: flex;
          flex-direction: row;
          height: 100%;
          .el-table::before {
            height: 0;
          }
          textarea.el-textarea__inner {
            height: 100%;
          }
          .rlist {
            flex: auto;
            margin-left: 16px;
          }
          .rfix {
            width: 60px;
            flex-shrink: 0;
            margin-left: 16px;
          }
          .head {
            writing-mode: vertical-lr;
            text-align: center;
            width: 28px;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #298dff22;
            font-size: 13px;
            font-weight: bold;
            color: #298dff;
            letter-spacing: 8px;
          }
        }
      }
    }
  }
}
.gantt_tooltip,
.custom-plan-tooltip {
  background: #434f71 !important;
  color: #ddd;
  border-radius: 4px;
}
.gantt_tooltip {
  background: #434f71 !important;
}
.custom-plan-tooltip {
  min-width: 240px;
  position: relative;
  .cross-icon {
    position: absolute;
    right: 4px;
    top: 2px;
    cursor: pointer;
  }
  header {
    padding: 0px;
    p,
    h3 {
      padding: 4px 0;
      margin: 2px 0;
    }
  }
  b {
    color: #eee;
  }
  > div {
    margin-top: 8px;
    line-height: 1.8em;
  }
}
.gantt_message_area {
  display: none !important;
}
