<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <title>
    <%= webpackConfig.name %>
  </title>
  <script type="text/javascript" src="./static/config/base64.min.js"></script>
  <script src="./static/player/liveplayer-lib.min.js"></script>
  <script type="text/javascript">//360兼容模式下提示切换内核方法
    function whatBrowser() {
      console.log(window.navigator.userAgent)
      if(document.documentMode === 11 || window.navigator.userAgent.indexOf('compatible') != -1){
        alert('请切换极速模式浏览!')
      }
    }
    whatBrowser()
  </script>
</head>

<body>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>
