<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60">
  <defs>
    <style>
      .cls-1 {
        fill: rgba(41,141,255,0.16);
      }

      .cls-2 {
        fill: #298dff;
        font-size: 30px;
        font-family: MicrosoftYaH<PERSON>-Bold, Microsoft YaHei;
        font-weight: 700;
      }
    </style>
  </defs>
  <g id="组_8045" data-name="组 8045" transform="translate(-1134 -135)">
    <rect id="矩形_3002" data-name="矩形 3002" class="cls-1" width="60" height="60" rx="20" transform="translate(1134 135)"/>
    <text id="_" data-name="％" class="cls-2" transform="translate(1179 177)"><tspan x="-30" y="0">％</tspan></text>
  </g>
</svg>
