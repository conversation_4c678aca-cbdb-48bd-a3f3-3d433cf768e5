import request from '@/utils/request'
// 获取原料分页列表
export function RawGetRawPageList(data) {
  return request({
    url: '/DF/Raw/GetRawPageList',
    method: 'post',
    data
  })
}
// 删除原料入库
export function RawDeleteRaw(data) {
  return request({
    url: '/DF/Raw/DeleteRaw',
    method: 'post',
    data
  })
}
// 获取原料汇总信息
export function GetSummaryRawData(data) {
  return request({
    url: '/DF/Raw/GetSummaryRawData',
    method: 'post',
    data
  })
}
// 更新原料状态
export function ChangeRawStatus(data) {
  return request({
    url: '/DF/Raw/ChangeRawStatus',
    method: 'post',
    data
  })
}
// 保存批次原料入库
export function SaveBatchRaw(data) {
  return request({
    url: '/DF/Raw/SaveBatchRaw',
    method: 'post',
    data
  })
}
// 获取原料明细
export function GetRawEntity(data) {
  return request({
    url: '/DF/Raw/GetRawEntity',
    method: 'post',
    data
  })
}
// 导出原料列表
export function ExportRawList(data) {
  return request({
    url: '/DF/Raw/ExportRawList',
    method: 'post',
    data
  })
}
// 获取日志分页列表
export function GetMaterialLogPageList(data) {
  return request({
    url: '/DF/Raw/GetMaterialLogPageList',
    method: 'post',
    data
  })
}
// 获取余料分页列表
export function SurplusGetSurplusPageList(data) {
  return request({
    url: '/DF/Surplus/GetSurplusPageList',
    method: 'post',
    data
  })
}
// 删除余料入库
export function SurplusDeleteSurplus(data) {
  return request({
    url: '/DF/Surplus/DeleteSurplus',
    method: 'post',
    data
  })
}
// 修改余料状态
export function ChangeSurplusStatus(data) {
  return request({
    url: '/DF/Surplus/ChangeSurplusStatus',
    method: 'post',
    data
  })
}
// 导出余料列表
export function ExportSurplusList(data) {
  return request({
    url: '/DF/Surplus/ExportSurplusList',
    method: 'post',
    data
  })
}
// 获取余料汇总信息
export function GetSummarySurplusData(data) {
  return request({
    url: '/DF/Surplus/GetSummarySurplusData',
    method: 'post',
    data
  })
}
// 获取余料明细
export function GetSurplusEntity(data) {
  return request({
    url: '/DF/Surplus/GetSurplusEntity',
    method: 'post',
    data
  })
}
// 获取已出库的原料分页下拉框
export function RawGetRawMateiralSelectPageList(data) {
  return request({
    url: '/DF/Raw/GetRawMateiralSelectPageList',
    method: 'post',
    data
  })
}
// 保存余料
export function SaveSurplus(data) {
  return request({
    url: '/DF/Surplus/SaveSurplus',
    method: 'post',
    data
  })
}
// 获取领料单分页列表
export function GetPickingReceiptPageList(data) {
  return request({
    url: '/DF/Pick/GetPickingReceiptPageList',
    method: 'post',
    data
  })
}
// 获取领料单汇总信息
export function GetSummaryPickData(data) {
  return request({
    url: '/DF/Pick/GetSummaryPickData',
    method: 'post',
    data
  })
}
// 获取领料单明细
export function GetPickingReceiptEntity(data) {
  return request({
    url: '/DF/Pick/GetPickingReceiptEntity',
    method: 'post',
    data
  })
}
// 作废领料单
export function DeletePickingReceipt(data) {
  return request({
    url: '/DF/Pick/DeletePickingReceipt',
    method: 'post',
    data
  })
}
// 原料/余料领用
export function PickReceiptMaterial(data) {
  return request({
    url: '/DF/Pick/PickReceiptMaterial',
    method: 'post',
    data
  })
}
// 原料/余料消确
export function PickConfirmReceiptMaterial(data) {
  return request({
    url: '/DF/Pick/PickConfirmReceiptMaterial',
    method: 'post',
    data
  })
}

// 原料导入
export function ImportBatchRaw(data) {
  return request({
    url: '/DF/Raw/ImportBatchRaw',
    method: 'post',
    data
  })
}


