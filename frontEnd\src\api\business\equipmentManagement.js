
import request from '@/utils/request'
// 设备维保 维修
//待办报修
export function GetWorkOrderManageList(data) {
  return request({
    url: '/PFI/Plm_WorkOrder_Setup/GetWorkOrderManageList',
    method: 'post',
    data
  })
}
// 获取工单总览统计
export function GetWorkorderStatistics(data) {
  return request({
    url: '/DF/WorkOrderAnalyse/GetWorkorderStatistics',
    method: 'post',
    data
  })
}
// 获取工单响应超时统计
export function GetTimeoutStatistics(data) {
  return request({
    url: '/DF/WorkOrderAnalyse/GetTimeoutStatistics',
    method: 'post',
    data
  })
}
// 获取工单满意度统计
export function GetSatisfactionStatistics(data) {
  return request({
    url: '/DF/WorkOrderAnalyse/GetSatisfactionStatistics',
    method: 'post',
    data
  })
}

// 获取处理人员完成排名
export function GetProcessedRank(data) {
  return request({
    url: '/DF/WorkOrderAnalyse/GetProcessedRank',
    method: 'post',
    data
  })
}


// 获取各车间工单情况
export function GetWorkShopCase(data) {
  return request({
    url: '/DF/WorkOrderAnalyse/GetWorkShopCase',
    method: 'post',
    data
  })
}

// 获取各车间趋势
export function GetWorkOrderTrend(data) {
  return request({
    url: '/DF/WorkOrderAnalyse/GetWorkOrderTrend',
    method: 'post',
    data
  })
}

// 获取报修工单故障类型
export function GetWorkOrderErrorType(data) {
  return request({
    url: '/DF/WorkOrderAnalyse/GetWorkOrderErrorType',
    method: 'post',
    data
  })
}

// 获取设备完好率
export function GetDeviceServiceabilityRate(data) {
  return request({
    url: '/DF/WorkOrderAnalyse/GetDeviceServiceabilityRate',
    method: 'post',
    data
  })
}

// 获取设备故障率排行
export function GetEquipFailureRateRank(data) {
  return request({
    url: '/DF/WorkOrderAnalyse/GetEquipFailureRateRank',
    method: 'post',
    data
  })
}




// 设备维保 维保
