import request from '@/utils/request'

// 获取安装位置
export function GetTreeAddress(data) {
  return request({
    method: 'post',
    url: '/DF/DFPark/GetTreeAddress',
    data
  })
}

// 根据安装位置获取设备列表
export function GetDeviceOfAddressList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetDeviceOfAddressList',
    data
  })
}


// 获取部门
export function GetDepartmentList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetDepartmentList',
    data
  })
}

// 获取部门下的人员
export function GetUserList(data) {
  return request({
    method: 'post',
    url: '/Platform/User/GetUserList',
    data
  })
}

// 获取人员
export function GetPersonList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetPersonList',
    data
  })
}

// 根据故障类型获取人员
export function GetPersonByTeamList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetPersonByTeamList',
    data
  })
}

// 获取工单类型
export function GetWorkOrderType(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetWorkOrderType',
    data
  })
}

// 工单设置-列表
export function GetWorkOrderSetupList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetWorkOrderSetupList',
    data
  })
}

// 工单设置-删除
export function DeleteWorkOrderSetup(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/DeleteWorkOrderSetup',
    data
  })
}

// 工单设置-查看详情
export function GetWorkOrderSetup(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetWorkOrderSetup',
    data
  })
}

// 工单设置-修改
export function EditeWorkOrderSetup(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/EditeWorkOrderSetup',
    data
  })
}

// 工单设置-新增
export function AddWorkOrderSetup(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/AddWorkOrderSetup',
    data
  })
}

// 工单管理-列表
export function GetWorkOrderManageList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetWorkOrderManageList',
    data
  })
}

// 维保标准-新增
export function AddMaintStand(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/AddMaintStand',
    data
  })
}
// 维保标准-查看详情
export function GetMaintStand(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetMaintStand',
    data
  })
}
// 维保标准-删除
export function DeleteMaintStand(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/DeleteMaintStand',
    data
  })
}
// 维保标准-列表
export function GetMaintStandList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetMaintStandList',
    data
  })
}
// 维保标准子集-新增
export function AddMaintStandSon(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/AddMaintStandSon',
    data
  })
}
// 维保标准子集-修改
export function EditeMaintStand(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/EditeMaintStand',
    data
  })
}
// 维保标准子集-删除
export function DeleteMaintStandSon(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/DeleteMaintStandSon',
    data
  })
}
// 维保标准导出-设备类型
export function ExportDeviceTypeStandInfo(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/ExportDeviceTypeStandInfo',
    data
  })
}// 维保标准导出-单设备
export function ExportDeviceInfo(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/ExportDeviceInfo',
    data
  })
}
// v2 维保标准导出-设备类型
export function ExportDeviceTypeStandList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/v2/ExportDeviceTypeStandList',
    data
  })
}// v2 维保标准导出-单设备
export function ExportDeviceList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/v2/ExportDeviceList',
    data
  })
}
// 维保标准导入-设备类型
export function ImportDeviceTypeStandInfo(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/ImportDeviceTypeStandInfo',
    data
  })
}// 维保标准导入-单设备
export function ImportDeviceInfo(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/ImportDeviceInfo',
    data
  })
}

// 工单管理-新增
export function AddWorkOrderManage(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/AddWorkOrderManage',
    data
  })
}
// 获取设备列表
export function GetDeviceList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetDeviceList',
    data
  })
}

// 工单管理-关闭
export function CloseWorkOrder(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/CloseWorkOrder',
    data
  })
}

// 工单管理-查看详情
export function GetSendWorkOrder(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetSendWorkOrder',
    data
  })
}

// 工单管理-派单接单
export function SendWorkOrderPerson(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/SendWorkOrderPerson',
    data
  })
}

// 工单管理-工单处理
export function ProcesWorkOrder(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/ProcesWorkOrder',
    data
  })
}

// 工单管理-工单复检
export function ReviewWorkOrder(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/ReviewWorkOrder',
    data
  })
}


// 工单管理-工单评价
export function EvaluateWorkOrder(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/EvaluateWorkOrder',
    data
  })
}

// 工单管理-获取列表
export function GetPlanItemList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetPlanItemList',
    data
  })
}
// 工单管理-获取维保详情列表
export function GetMaintenDetail(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetMaintenDetail',
    data
  })
}

// 维保计划-新增
export function AddMainten_Plan(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/AddMainten_Plan',
    data
  })
}

// 维保计划-编辑
export function EditeMainten_PlanItem(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/EditeMainten_PlanItem',
    data
  })
}
// 维保计划-获取详情
export function GetPlanItemInfo(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetPlanItemInfo',
    data
  })
}

// 工单管理-获取维保详情列表的详情列表
export function GetMaintenItem(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetMaintenItem',
    data
  })
}

// 工单计划 获取编号
export function GetPlanCode(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetPlanCode',
    data
  })
}

// 获取部门
export function GetDepartmentTree(data) {
  return request({
    method: 'post',
    url: '/Platform/Department/GetDepartmentTree',
    data
  })
}
// 删除计划

export function DeletePlanItem(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/DeletePlanItem',
    data
  })
}

// 获取设备列表

export function GetDevicePageList(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/GetDevicePageList',
    data
  })
}

// 模板下载

export function ExportModelType(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/ExportModelType',
    data
  })
}
// 模板下载1

export function ExportModelDevice(data) {
  return request({
    method: 'post',
    url: '/PFI/Plm_WorkOrder_Setup/ExportModelDevice',
    data
  })
}


export function GetEquipDropList(data) {
  return request({
    method: 'get',
    url: '/DF/EQPTAsset/GetEquipDropList',
    params:data
  })
}

