import { Message } from 'element-ui'
import moment from 'moment'
import { CONSTRAINT_TYPES } from '@/api/plan/index'
import { toDateStr } from './util'
/**
 *
 * @param {*} gantt
 * @param {*} plan
 * @param {*} opts
 */
export function createNewTask(
  gantt,
  plan,
  opts = { parent: '0', type: 'task' }
) {
  const parent = gantt.getTask(opts.parent)
  if (parent && ['task', 'milestone'].indexOf(parent.type) > -1) {
    return Message({
      type: 'warning',
      message: '不允许在非WBS作业下新建子作业'
    })
  }
  const t = {}
  Object.keys(TASK_FIELDS).forEach(k => {
    t[k] = TASK_FIELDS[k]
  })
  // 覆盖默认值
  const d = new Date()
  const type = opts.type ?? 'task'
  let text =
    type === 'project'
      ? '新建WBS'
      : type === 'milestone'
      ? '里程碑'
      : '新建作业'
  if (opts.text) text = opts.text
  const start_date =
    parent?.start_date ||
    (plan?.Plan_Start_Date ? plan?.Plan_Start_Date : null) ||
    new Date()
  t.id = d.getTime()
  t.type = opts.type ?? 'task'
  t.parent = opts.parent ?? '0'
  t.text = text
  t.start_date = moment(start_date)
    .startOf('date')
    .toDate()
  console.log(start_date, t.start_date)
  t.duration = 1
  t.end_date = moment(start_date)
    .add(1, 'days')
    .startOf('date')
    .toDate()
  t.Dynamic_Start_Date = moment(t.start_date).format('YYYY-MM-DD')
  t.Dynamic_End_Date = moment(t.start_date).format('YYYY-MM-DD')
  t.Dynamic_Duration = t.duration
  t.Plan_Start_Date = t.start_date
  t.Plan_End_Date = t.start_date
  t.Plan_Duration = 1
  t.Needed_Start_Date = t.start_date
  t.Needed_End_Date = t.start_date
  t.Needed_Duration = 1
  t.Plan_Resources = 0
  t.Actual_Resources = 0
  t.Plan_Id = plan.Id
  gantt.addTask(t)
  gantt.render()
  return gantt.getTask(t.id)
}

/**
 *借助gantt插件数据递归修改上级wbs扩展字段
 * @param {*} gantt
 */
export function updateParentWBS(gantt) {
  // 计算前暂不递归修改WBS属性
}
const DIRECTS = [
  'text',
  'Responsible_User',
  'Plan_Resources',
  'Actual_Resources',
  'Remark',
  'Existing_Problems',
  'Solutions',
  'Need_Coordinate',
  'Coordinate_Department'
]
/**
 * 任务属性修改
 * @param {*} opts， 类型为 {gantt, task, field, value}
 * @returns
 */
export function updateGanttTask({ gantt, task, field, value }) {
  // 没有其它影响可以直接修改的属性，所有类型
  if (DIRECTS.indexOf(field) > -1 || !TASK_FIELDS.hasOwnProperty(field)) {
    task[field] = value
    return gantt.updateTask(task.id)
  }
  if (field === 'constraint_type') {
    setTaskConstraintType(task, value, gantt)
  }
  if (field === 'constraint_date') {
    setTaskConstraintDate(task, value, gantt)
  }
  if (field === 'Plan_Start_Date') {
    setPlanStartDate(task, value, gantt)
  }
  if (field === 'Plan_End_Date') {
    setPlanEndDate(task, value, gantt)
  }
  if (field === 'Plan_Duration') {
    setPlanDuration(task, value, gantt)
  }
  if (field === 'Actual_Start_Date') {
    setActualStartDate(task, value, gantt)
  }
  if (field === 'Actual_End_Date') {
    setActualEndDate(task, value, gantt)
  }
  if (field === 'Actual_Progress') {
    setActualProgress(task, value, gantt)
  }
  if (field === 'type') {
    setTaskType(task, value, gantt)
  }
}
/**
 * 任务是否实际开始
 * @param {*} t
 */
function iast(t) {
  return t.Actual_Start_Date || t.Actual_End_Date
}
/**
 * 设置限制类型
 */
export function setTaskConstraintType(task, type, gantt) {
  if (task.type === 'milestone') return
  task.constraint_type = type
  // 修改 date
  if (['asap', 'alap'].indexOf(type) > -1) {
    // 越早越好，越晚越好，清空日期
    task.constraint_date = ''
  } else if (['snet', 'snlt', 'mso'].indexOf(type) > -1) {
    // 限制开始日
    if (!task.constraint_date) {
      task.constraint_date = task.Plan_Start_Date
    }
  } else if (['mfo', 'fnet', 'fnlt'].indexOf(type) > -1) {
    // 限制完成日
    if (!task.constraint_date) {
      task.constraint_date = task.Plan_End_Date
    }
  }
}
/**
 * 设置限制日期
 */
export function setTaskConstraintDate(task, date, gantt) {
  const ias = iast(task)
  if (task.type === 'milestone' && !ias) {
    task.start_date = task.Plan_Start_Date = task.Needed_Start_Date = date
    task.end_date = task.Plan_End_Date = task.End_Start_Date = date
    task.duration = task.Plan_Duration = task.Needed_Duration = task.Dynamic_Duration = 0
    task.Dynamic_Start_Date = moment(task.start_date).format('YYYY-MM-DD')
    task.Dynamic_End_Date = moment(task.end_date).format('YYYY-MM-DD')
    return
  }
  task.constraint_date = date
  if (!date || ['asap', 'alap'].indexOf(task.constraint_type) > -1) return
  if (!ias) {
    // 未实际开始
    if (['snet', 'snlt', 'mso'].indexOf(task.constraint_type) > -1) {
      // 限制开始日
      task.start_date = task.Plan_Start_Date = task.Needed_Start_Date = date
      task.Dynamic_Start_Date = moment(task.Plan_Start_Date).format(
        'YYYY-MM-DD'
      )
      task.end_date = gantt.calculateEndDate({
        start_date: date,
        duration: task.Plan_Duration
      })
      task.Plan_End_Date = task.Needed_End_Date = moment(task.end_date)
        .add(-1, 'days')
        .startOf('date')
        .toDate()
      task.Dynamic_End_Date = moment(task.Plan_End_Date).format('YYYY-MM-DD')
    } else if (['mfo', 'fnet', 'fnlt'].indexOf(task.constraint_type) > -1) {
      // 限制完成日
      task.Plan_End_Date = task.Needed_End_Date = date
      task.Dynamic_End_Date = moment(task.Plan_End_Date).format('YYYY-MM-DD')
      task.end_date = moment(date)
        .add(1, 'days')
        .startOf('date')
        .toDate()
      task.start_date = task.Plan_Start_Date = task.Needed_Start_Date = gantt.calculateEndDate(
        { start_date: task.end_date, duration: -task.Plan_Duration }
      )
      task.Dynamic_Start_Date = moment(task.Plan_Start_Date).format(
        'YYYY-MM-DD'
      )
    }
  }
}
/**
 * 拖动设置开始结束
 * 针对未实际开始的作业，可以拖动
 */
export function setTaskSize(task) {
  const ias = iast(task)
  console.log(task)
  if (ias) return
  task.Plan_Start_Date = task.Needed_Start_Date = task.start_date
  task.Dynamic_Start_Date = moment(task.Plan_Start_Date).format('YYYY-MM-DD')
  task.Plan_Duration = task.Dynamic_Duration = task.Needed_Duration =
    task.duration
  task.Plan_End_Date = task.Needed_End_Date = moment(task.end_date)
    .add(-1, 'days')
    .startOf('date')
    .toDate()
  task.Dynamic_End_Date = moment(task.Plan_End_Date).format('YYYY-MM-DD')
}
/**
 * 修改作业类型
 */
function setTaskType(task, type, gantt) {
  if (task.Actual_Start_Date) return
  task.type = type
  if (type === 'milestone') {
    task.start_date = task.Needed_Start_Date = task.Plan_Start_Date
    task.Dynamic_Start_Date = moment(task.Plan_Start_Date).format('YYYY-MM-DD')
    task.duration = task.Plan_Duration = task.Needed_Duration = task.Actual_Progress = task.progress = 0
    task.end_date = task.Plan_End_Date = task.Needed_End_Date = task.start_date
    task.Dynamic_End_Date = moment(task.Plan_End_Date).format('YYYY-MM-DD')
    task.constraint_type = ''
    task.constraint_date = ''
    task.Dynamic_Duration = 0
  }
  if (type === 'task') {
    task.start_date = task.Needed_Start_Date = task.Plan_Start_Date
    task.Dynamic_Start_Date = moment(task.Plan_Start_Date).format('YYYY-MM-DD')
    task.duration = task.Plan_Duration = task.Needed_Duration = task.Dynamic_Duration = 1
    task.end_date = gantt.calculateEndDate({
      start_date: task.start_date,
      duration: task.Plan_Duration
    })
    task.Plan_End_Date = task.Needed_End_Date = gantt.calculateEndDate({
      start_date: task.start_date,
      duration: task.Plan_Duration - 1
    })
    task.Dynamic_End_Date = moment(task.Plan_End_Date).format('YYYY-MM-DD')
    task.constraint_type = 'asap'
    task.constraint_date = ''
  }
}
/**
 * 修改任务计划开始日期
 */
function setPlanStartDate(task, date, gantt) {
  if (!date) return
  if (task.type === 'milestone') {
    task.Needed_Start_Date = task.Plan_Start_Date = task.start_date = date
    task.Dynamic_Start_Date = moment(task.Plan_Start_Date).format('YYYY-MM-DD')
    task.duration = task.Plan_Duration = task.Needed_Duration = 0
    task.end_date = task.Plan_End_Date = task.Needed_End_Date = task.start_date
    task.Dynamic_End_Date = moment(task.Plan_End_Date).format('YYYY-MM-DD')
    task.constraint_type = ''
    task.constraint_date = ''
    return
  }
  task.Plan_Start_Date = date
  task.Plan_End_Date = gantt.calculateEndDate({
    start_date: date,
    duration: task.Plan_Duration - 1
  })
  const ias = iast(task)
  if (!ias) {
    task.start_date = task.Needed_Start_Date = date
    task.Dynamic_Start_Date = moment(task.start_date).format('YYYY-MM-DD')
    task.Needed_End_Date = task.Plan_End_Date
    task.Dynamic_End_Date = moment(task.Plan_End_Date).format('YYYY-MM-DD')
    task.end_date = gantt.calculateEndDate({
      start_date: date,
      duration: task.Plan_Duration
    })
  }
}
/**
 * 修改任务计划结束日期
 */
function setPlanEndDate(task, date, gantt) {
  if (!date) return
  if (task.type === 'milestone') {
    task.Needed_Start_Date = task.Plan_Start_Date = task.start_date = date
    task.Dynamic_Start_Date = moment(task.Plan_Start_Date).format('YYYY-MM-DD')
    task.duration = task.Plan_Duration = task.Needed_Duration = 0
    task.end_date = task.Plan_End_Date = task.Needed_End_Date = task.start_date
    task.Dynamic_End_Date = moment(task.Plan_End_Date).format('YYYY-MM-DD')
    task.constraint_type = ''
    task.constraint_date = ''
    return
  }
  task.Plan_End_Date = date
  task.Plan_Duration =
    gantt.calculateDuration({
      start_date: task.Plan_Start_Date,
      end_date: task.Plan_End_Date
    }) + 1
  const ias = iast(task)
  if (!ias) {
    task.Needed_End_Date = date
    task.Dynamic_End_Date = moment(task.Needed_End_Date).format('YYYY-MM-DD')
    task.Needed_Duration = task.Dynamic_Duration = task.duration =
      task.Plan_Duration
    task.end_date = gantt.calculateEndDate({
      start_date: task.start_date,
      duration: task.duration
    })
  }
}
/**
 * 修改任务计划工期
 */
function setPlanDuration(task, days, gantt) {
  if (!days) return
  if (task.type === 'milestone') return
  task.Plan_Duration = Number(days)
  task.Plan_End_Date = gantt.calculateEndDate({
    start_date: task.Plan_Start_Date,
    duration: task.Plan_Duration - 1
  })
  const ias = iast(task)
  if (!ias) {
    task.duration = task.Needed_Duration = task.Dynamic_Duration =
      task.Plan_Duration
    task.end_date = gantt.calculateEndDate({
      start_date: task.start_date,
      duration: task.duration
    })
    task.Needed_End_Date = task.Plan_End_Date = moment(task.end_date)
      .add(-1, 'days')
      .startOf('date')
      .toDate()
    task.Dynamic_End_Date = moment(task.Plan_End_Date).format('YYYY-MM-DD')
  }
}
/**
 * 修改任务实际开始
 */
function setActualStartDate(task, date, gantt) {
  if (task.type === 'milestone' && !date) return
  if (task.type === 'milestone' && date) {
    task.Actual_End_Date = task.Actual_Start_Date = date
    task.Dynamic_Start_Date = task.Dynamic_End_Date = moment(
      task.Actual_End_Date
    ).format('YYYY-MM-DD[A]')
    task.Actual_Progress = task.progress = 1
    return
  }
  if (!date) {
    task.Actual_Progress = task.progress = 0
    task.Actual_End_Date = ''
    task.Actual_Duration = 0
    task.Actual_Start_Date = date
    task.Needed_Start_Date = task.start_date = task.Plan_Start_Date
    task.Dynamic_Start_Date = moment(task.Plan_Start_Date).format('YYYY-MM-DD')
    task.Needed_Duration = task.Dynamic_Duration = task.duration =
      task.Plan_Duration
    task.end_date = gantt.calculateEndDate({
      start_date: task.start_date,
      duration: task.duration
    })
    task.Needed_End_Date = task.Plan_End_Date
    task.Dynamic_End_Date = moment(task.Plan_End_Date).format('YYYY-MM-DD')
    return
  }
  task.Actual_Start_Date = date
  task.start_date = date
  task.Dynamic_Start_Date = moment(task.start_date).format('YYYY-MM-DD[A]')
  if (!task.Actual_End_Date) {
    task.duration = task.Dynamic_Duration = task.Plan_Duration
    task.end_date = gantt.calculateEndDate({
      start_date: task.start_date,
      duration: task.Plan_Duration
    })
  }
  task.Dynamic_End_Date = moment(task.end_date)
    .add(-1, 'days')
    .startOf('date')
    .toDate()
  task.Dynamic_Duration = task.duration
  task.Dynamic_End_Date = moment(task.Dynamic_End_Date).format('YYYY-MM-DD')
}
/**
 * 修改任务实际结束
 */
function setActualEndDate(task, date, gantt) {
  if (task.type === 'milestone' && !date) {
    task.Actual_End_Date = task.Actual_Start_Date = date
    task.Actual_Progress = task.progress = 0
    task.Dynamic_End_Date = task.Plan_Start_Date
    task.Dynamic_Start_Date = task.Plan_Start_Date
    task.Dynamic_Duration = task.duration = 0
    return
  }
  if (task.type === 'milestone' && date) {
    task.Actual_End_Date = task.Actual_Start_Date = date
    task.Dynamic_Start_Date = task.Dynamic_End_Date = moment(
      task.Actual_End_Date
    ).format('YYYY-MM-DD[A]')
    task.Actual_Progress = task.progress = 1
    return
  }
  if (!date) {
    task.Actual_Duration = 0
    task.duration = task.Dynamic_Duration = task.Plan_Duration
    task.end_date = gantt.calculateEndDate({
      start_date: task.start_date,
      duration: task.duration
    })
    task.Dynamic_End_Date = moment(task.end_date)
      .add(-1, 'days')
      .startOf('date')
      .toDate()
    task.Dynamic_End_Date = moment(task.Dynamic_End_Date).format('YYYY-MM-DD')
    task.Actual_End_Date = date
    return
  }
  task.Actual_End_Date = date
  task.end_date = moment(date)
    .add(1, 'days')
    .startOf('date')
    .toDate()
  task.Dynamic_End_Date = moment(task.Actual_End_Date).format('YYYY-MM-DD[A]')
  task.progress = task.Actual_Progress = 1
  task.Actual_Duration = task.Dynamic_Duration =
    gantt.calculateDuration({
      start_date: task.Actual_Start_Date,
      end_date: task.Actual_End_Date
    }) + 1
  task.duration = task.Actual_Duration
  task.Needed_Duration = 0
  task.Needed_Start_Date = task.Needed_End_Date = ''
}
/**
 * 修改任务实际进度
 */
function setActualProgress(task, value, gantt) {
  if (value >= 1) value = 1
  if (!value || value < 0) value = 0
  task.Actual_Progress = task.progress = Number(value)
  setActualEndDate(
    task,
    task.Actual_Progress === 1
      ? moment(new Date())
          .startOf('date')
          .toDate()
      : '',
    gantt
  )
}
export const DATE_FIELDS = [
  'Data_Date',
  'Actual_End_Date',
  'Actual_Start_Date',
  'Cur_Data_Date',
  'Plan_End_Date',
  'Plan_Start_Date',
  'Control_Plan_Start_Date',
  'Control_Plan_End_Date',
  'Needed_Start_Date',
  'Needed_End_Date',
  'Target_Start_Date',
  'Target_End_Date',
  'Early_Start_Date',
  'Laster_Start_Date',
  'Early_End_Date',
  'Laster_End_Date',
  'start_date',
  'end_date',
  'constraint_date'
]
export function parseServeTask(task) {
  const o = {}
  Object.keys(task).forEach(k => {
    if (DATE_FIELDS.indexOf(k) > -1) {
      o[k] = task[k]
        ? moment(task[k])
            .startOf('date')
            .toDate()
        : ''
    } else if (k === 'type') {
      o[k] = task[k].toLowerCase()
    } else if (k.toLowerCase().indexOf('progress') > -1) {
      o[k] = task[k] && Number(task[k]) > 1 ? Number(task[k]) / 100 : task[k]
    } else {
      o[k] = task[k]
    }
  })
  return o
}
export const TASK_FIELDS = {
  id: '',
  type: '', //
  parent: '0',
  text: '',
  start_date: '',
  end_date: '',
  duration: 0,
  progress: 0,
  Dynamic_Start_Date: '',
  Dynamic_End_Date: '',
  Dynamic_Duration: '',
  constraint_type: 'asap',
  constraint_date: '',
  Plan_Start_Date: '',
  Plan_End_Date: '',
  Plan_Duration: 0,
  Actual_Start_Date: '',
  Actual_End_Date: '',
  Actual_Duration: 0,
  Actual_Progress: 0,
  Needed_Start_Date: '',
  Needed_End_Date: '',
  Needed_Duration: 0,
  Target_Start_Date: '',
  Target_End_Date: '',
  Target_Duration: 0,
  Start_Difference: 0,
  End_Difference: 0,
  Duration_Difference: 0,
  Free_Float: 0,
  Total_Float: 0,
  Early_Start_Date: '',
  Laster_Start_Date: '',
  Laster_End_Date: '',
  Early_End_Date: '',
  Plan_Resources: 0,
  Actual_Resources: 0,
  Target_Resources: 0,
  Laster_Progress_Date: '',
  Remark: '',
  Plan_Id: '',
  Control_Plan_Start_Date: '',
  Control_Plan_End_Date: '',
  Existing_Problems: '',
  Solutions: '',
  Need_Coordinate: '',
  Coordinate_Department: '',
  Responsible_User: ''
}
export const ALL_TASK_COLUMNS = [
  {
    label: '作业名称',
    width: 200,
    min_width: 200,
    align: 'left',
    name: 'text',
    hide: false,
    resize: true,
    tree: true
  },
  {
    label: '开始时间',
    width: 120,
    align: 'center',
    name: 'Dynamic_Start_Date',
    hide: false,
    resize: true
  },
  {
    label: '完成时间',
    width: 120,
    align: 'center',
    name: 'Dynamic_End_Date',
    hide: false,
    resize: true
  },
  {
    label: '工期',
    width: 90,
    align: 'center',
    name: 'Dynamic_Duration',
    hide: false,
    resize: true
  },
  {
    label: '进度%',
    width: 90,
    align: 'center',
    name: 'Actual_Progress',
    hide: false,
    resize: true,
    template: t => {
      return `${((t.Actual_Progress || 0) * 100).toFixed(1)}`
    }
  },
  {
    label: '责任人',
    width: 100,
    align: 'center',
    name: 'Responsible_UserName',
    hide: false,
    resize: true
  },
  {
    label: '作业类型',
    width: 100,
    align: 'center',
    name: 'type',
    hide: false,
    resize: true,
    template: t => {
      return t.type === 'project'
        ? 'WBS'
        : t.type === 'milestone'
        ? '里程碑'
        : '作业'
    }
  },
  {
    label: '备注',
    width: 240,
    align: 'center',
    name: 'Remark',
    hide: false,
    resize: true
  },
  {
    label: '限制条件',
    width: 160,
    align: 'center',
    name: 'constraint_type',
    hide: false,
    resize: true,
    template: t => {
      return CONSTRAINT_TYPES.find(c => c.value === t.constraint_type).label
    }
  },
  {
    label: '限制日期',
    width: 120,
    align: 'center',
    name: 'constraint_date',
    hide: false,
    resize: true,
    template: t => {
      return toDateStr(t.constraint_date)
    }
  },
  {
    label: '计划工期',
    width: 80,
    align: 'center',
    name: 'Plan_Duration',
    hide: false,
    resize: true
  },
  {
    label: '尚需工期',
    width: 80,
    align: 'center',
    name: 'Needed_Duration',
    hide: false,
    resize: true
  },
  {
    label: '实际工期',
    width: 80,
    align: 'center',
    name: 'Actual_Duration',
    hide: false,
    resize: true
  },
  {
    label: '自由时差',
    width: 80,
    align: 'center',
    name: 'Free_Float',
    hide: false,
    resize: true
  },
  {
    label: '总时差',
    width: 80,
    align: 'center',
    name: 'Total_Float',
    hide: false,
    resize: true
  },
  {
    label: '计划开始',
    width: 120,
    align: 'center',
    name: 'Plan_Start_Date',
    hide: false,
    resize: true,
    template: t => {
      return toDateStr(t.Plan_Start_Date)
    }
  },
  {
    label: '计划完成',
    width: 120,
    align: 'center',
    name: 'Plan_End_Date',
    hide: false,
    resize: true,
    template: t => {
      return toDateStr(t.Plan_End_Date)
    }
  },
  {
    label: '实际开始',
    width: 120,
    align: 'center',
    name: 'Actual_Start_Date',
    hide: false,
    resize: true,
    template: t => {
      return toDateStr(t.Actual_Start_Date)
    }
  },
  {
    label: '实际完成',
    width: 120,
    align: 'center',
    name: 'Actual_End_Date',
    hide: false,
    resize: true,
    template: t => {
      return toDateStr(t.Actual_Start_Date)
    }
  },
  {
    label: '目标开始',
    width: 120,
    align: 'center',
    name: 'Actual_End_Date',
    hide: false,
    resize: true,
    template: t => {
      return toDateStr(t.Actual_Start_Date)
    }
  }
]
