import request from '@/utils/request'
// 今、本、累告警次数
export function GetDateAlarmCount(data) {
  return request({
    method: 'get',
    url: '/DF/BroadcastBehaviorAnalyse/GetDateAlarmCount',
    params: data
  })
}
// 告警类型告警次数
export function GetAlarmTypeCount(data) {
  return request({
    method: 'get',
    url: '/DF/BroadcastBehaviorAnalyse/GetAlarmTypeCount',
    params: data
  })
}
// 告警轮播
export function GetAlarmCarousel(data) {
  return request({
    method: 'get',
    url: '/DF/BroadcastBehaviorAnalyse/GetAlarmCarousel',
    params: data
  })
}
// 当日告警维度分析
export function GetDayAlarmTypeAnalyse(data) {
  return request({
    method: 'get',
    url: '/DF/BroadcastBehaviorAnalyse/GetDayAlarmTypeAnalyse',
    params: data
  })
}
// 今日告警位置排行
export function GetDayAlarmOrderBy(data) {
  return request({
    method: 'get',
    url: '/DF/BroadcastBehaviorAnalyse/GetDayAlarmOrderBy',
    params: data
  })
}
// 当月累计告警类型排行
export function GetMonthAlarmTypeCount(data) {
  return request({
    method: 'get',
    url: '/DF/BroadcastBehaviorAnalyse/GetMonthAlarmTypeCount',
    params: data
  })
}
// 历史告警趋势
export function GetHistoryAlarmCount(data) {
  return request({
    method: 'get',
    url: '/DF/BroadcastBehaviorAnalyse/GetHistoryAlarmCount',
    params: data
  })
}
// 告警区域统计
export function GetAlarmAreaCount(data) {
  return request({
    method: 'get',
    url: '/DF/BroadcastBehaviorAnalyse/GetAlarmAreaCount',
    params: data
  })
}
// 查看更多跳转链接
export function GetJumpUrl(data) {
  return request({
    method: 'get',
    url: '/DF/BroadcastBehaviorAnalyse/GetJumpUrl',
    params: data
  })
}