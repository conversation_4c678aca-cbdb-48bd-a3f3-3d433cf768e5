import request from '@/utils/request'

// 获取角色树
export function GetRoleTree() {
  return request({
    url: '/Platform/Role/GetRoleTree',
    method: 'post'
  })
}

// 添加/更新角色
export function SaveRole(data) {
  return request({
    url: '/Platform/Role/SaveRole',
    method: 'post',
    data
  })
}
// 删除角色
export function DeleteRole(data) {
  return request({
    url: '/Platform/Role/DeleteRole',
    method: 'post',
    data
  })
}

// 获取角色下用户数据权限列表
export function GetUserListByRole(data) {
  return request({
    url: `/Platform/Role/GetUserListByRole`,
    method: 'post',
    data
  })
}

export function SaveUserAuthorize(data) {
  return request({
    url: '/Platform/Role/SaveUserAuthorize',
    method: 'post',
    data
  })
}

export function GetRoleWorkingObjListByUser(data) {
  return request({
    url: '/Platform/Role/GetRoleWorkingObjListByUser',
    method: 'post',
    data
  })
}

// 保存角色菜单授权
export function SaveRoleMenu(data) {
  return request({
    url: '/Platform/Role/SaveRoleMenu',
    method: 'post',
    data
  })
}
// 获取已授权功能树(角色授权页用)
export function GetRoleMenusObj(data) {
  return request({
    url: '/Platform/Role/GetRoleMenusObj',
    method: 'post',
    data
  })
}

// 获取用户角色（项目无关）
export function GetUserRoleTreeWithoutObject(data) {
  return request({
    url: '/Platform/User/GetUserRoleTreeWithoutObject',
    method: 'post',
    data
  })
}
// 获取用户数据权限(新)
export function GetWorkingObjTree(data) {
  return request({
    url: '/Platform/User/GetWorkingObjTree',
    method: 'post',
    data
  })
}
// 用户数据权限保存
export function SaveUserObject(data) {
  return request({
    url: '/Platform/User/SaveUserObject',
    method: 'post',
    data
  })
}
// 部门数据权限保存
export function SaveDepartmentObject(data) {
  return request({
    url: '/Platform/User/SaveDepartmentObject',
    method: 'post',
    data
  })
}

export function GetRoleList(data) {
  return request({
    url: '/Platform/Role/GetRoleList',
    method: 'post',
    data
  })
}
