import request from '@/utils/request'

// 获取菜单列表
export function GetMenuList(data) {
  return request({
    url: '/Platform/Menu/GetMenuList',
    method: 'post',
    data
  })
}

// 根据条件获取菜单的树形结构
export function GetTreeMenus(data) {
  return request({
    url: '/Platform/Menu/GetTreeMenus',
    method: 'post',
    data
  })
}

// 获取菜单实体
export function GetMenuEntity(data) {
  return request({
    url: '/Platform/Menu/GetMenuEntity',
    method: 'post',
    data
  })
}

// 获取菜单按钮列表
export function GetButtonList(data) {
  return request({
    url: '/Platform/Menu/GetButtonList',
    method: 'post',
    data
  })
}
// 更新按钮实体
export function UpdateButton(data) {
  return request({
    url: '/Platform/Menu/UpdateButton',
    method: 'post',
    data
  })
}

// 新增按钮实体
export function AddButton(data) {
  return request({
    url: '/Platform/Menu/AddButton',
    method: 'post',
    data
  })
}

// 更新菜单
export function UpdateMenu(data) {
  return request({
    url: '/Platform/Menu/UpdateMenu',
    method: 'post',
    data
  })
}

// 获取系统表格列表
export function GetGridList(data) {
  return request({
    url: '/Platform/Grid/GetGridList',
    method: 'post',
    data
  })
}

// 更新系统表格
export function UpdateGrid(data) {
  return request({
    url: '/Platform/Grid/UpdateGrid',
    method: 'post',
    data
  })
}

// 获取系统表格列列表
export function GetColumnList(data) {
  return request({
    url: '/Platform/Grid/GetColumnList',
    method: 'post',
    data
  })
}
// 获取系统表格列树形列表
export function GetColumnTreeList(data) {
  return request({
    url: '/Platform/Grid/GetColumnTreeList',
    method: 'post',
    data
  })
}
// 下载导入模板
export function GridColumnemplate(data) {
  return request({
    url: '/Platform/Grid/GridColumnemplate',
    method: 'post',
    data
  })
}
// 导入列表信息
export function ImportGridColumn(data) {
  return request({
    url: '/Platform/Grid/ImportGridColumn',
    method: 'post',
    data
  })
}
// 获取model列表 (Auth)
export function GetModelList(data) {
  return request({
    url: '/Platform/Grid/GetModelList',
    method: 'post',
    data
  })
}
// 获取model字段明细 (Auth)
export function GetColumnFromModel(data) {
  return request({
    url: '/Platform/Grid/GetColumnFromModel',
    method: 'post',
    data
  })
}
// 获取系统表格样式(code)
export function GetGridByCode(data) {
  return request({
    url: '/Platform/Grid/GetGridByCode',
    method: 'post',
    data
  })
}

export function GetColumnListByCode(data) {
  return request({
    url: '/Platform/Grid/GetColumnListByCode',
    method: 'post',
    data
  })
}

// 获取数据库表清单
export function GetTableList(data) {
  return request({
    url: '/Platform/Grid/GetTableList',
    method: 'post',
    data
  })
}

// 获取数据库表字段明细
export function GetColumnFromTable(data) {
  return request({
    url: '/Platform/Grid/GetColumnFromTable',
    method: 'post',
    data
  })
}

// 保存所选字段到字段配置表
export function SaveGridColumn(data) {
  return request({
    url: '/Platform/Grid/SaveGridColumn',
    method: 'post',
    data
  })
}

// 保存所选字段配置
export function SaveGridColumnFromTable(data) {
  return request({
    url: '/Platform/Grid/SaveGridColumnFromTable',
    method: 'post',
    data
  })
}

// 获取字典分类树
export function GetTreeDictionary() {
  return request({
    url: '/Platform/Dictionary/GetTreeDictionary',
    method: 'post'
  })
}

// 获取项目可配置字典
export function GetDictionaryListProjectModify() {
  return request({
    url: '/Platform/Dictionary/GetDictionaryListProjectModify',
    method: 'post'
  })
}

// 获取字典值表单
export function GetEntity(data) {
  return request({
    url: '/Platform/Dictionary/GetEntity',
    method: 'post',
    data
  })
}

// 保存字典分类
export function SaveDictionary(data) {
  return request({
    url: '/Platform/Dictionary/SaveDictionary',
    method: 'post',
    data
  })
}

// 获取字典分类表单
export function GetDictionaryEntity(data) {
  return request({
    url: '/Platform/Dictionary/GetDictionaryEntity',
    method: 'post',
    data
  })
}

// 获取字典详情列表
export function GetDictionaryDetailList(data) {
  return request({
    url: '/Platform/Dictionary/GetDictionaryDetailList',
    method: 'post',
    data
  })
}
// 获取字典详情列表
export function GetDictionaryTreeDetailList(data) {
  return request({
    url: '/Platform/Dictionary/GetDictionaryTreeDetailList',
    method: 'post',
    data
  })
}
// 通过分类code获取字典详情树结构
export function GetDictionaryTreeDetailListByCode(data) {
  return request({
    url: '/Platform/Dictionary/GetDictionaryTreeDetailListByCode',
    method: 'post',
    data
  })
}
// 获取字典详情列表
export function GetDictionaryDetailEntity(data) {
  return request({
    url: '/Platform/Dictionary/GetDictionaryDetailEntity',
    method: 'post',
    data
  })
}

// 通过分类code获取字典详情
export function GetDictionaryDetailListByCode(data) {
  return request({
    url: '/Platform/Dictionary/GetDictionaryDetailListByCode',
    method: 'post',
    data
  })
}
// 匿名通过分类code获取字典详情
export function GetDictionaryDetailListByCodeAnonymous(data) {
  return request({
    url: '/Platform/Dictionary/GetDictionaryDetailListByCodeAnonymous',
    method: 'post',
    data
  })
}
// 根据父节点获取字典配置详情列表
export function GetDictionaryDetailListByParentId(parentId) {
  return request({
    url: '/Platform/Dictionary/GetDictionaryDetailListByParentId',
    method: 'post',
    params: {
      parentId
    }
  })
}
export function GetDetailListDictionaryByCode(data) {
  return request({
    url: '/Platform/Dictionary/GetDetailListDictionaryByCode',
    method: 'post',
    data
  })
}

// 保存字典明细
export function SaveDictionaryDetail(data) {
  return request({
    url: '/Platform/Dictionary/SaveDictionaryDetail',
    method: 'post',
    data
  })
}

// 修改字典值有效
export function UpdateDictionaryDetailIsValid(data) {
  return request({
    url: '/Platform/Dictionary/UpdateDictionaryDetailIsValid',
    method: 'post',
    data
  })
}

// 获取字典列表
export function GetDictionaryList(data) {
  return request({
    url: '/Platform/Dictionary/GetDictionaryList',
    method: 'post',
    data
  })
}

// 添加/更新实体
export function UpdateDepartment(data) {
  return request({
    url: '/Platform/Department/UpdateDepartment',
    method: 'post',
    data
  })
}

// 获取部门列表
export function GetDepartmentList(data) {
  return request({
    url: '/Platform/Department/GetDepartmentList',
    method: 'post',
    data
  })
}

// 获取部门树
export function GetDepartmentTree(data) {
  return request({
    url: '/Platform/Department/GetDepartmentTree',
    method: 'post',
    data
  })
}
// 获取部门树
export function GetDepartmentTreebyId(data) {
  return request({
    url: '/Platform/Department/GetDepartmentTreebyId',
    method: 'post',
    data
  })
}

// 获取部门实体
export function GetDepartmentEntity(data) {
  return request({
    url: '/Platform/Department/GetDepartmentEntity',
    method: 'post',
    data
  })
}

// 删除部门
export function DeleteDepartment(data) {
  return request({
    url: '/Platform/Department/DeleteDepartment',
    method: 'post',
    data
  })
}

// 获取用户
export function GetUserList(data) {
  return request({
    url: '/Platform/User/GetUserList',
    method: 'post',
    data
  })
}

// 添加用户
export function SaveUser(data) {
  return request({
    url: '/Platform/User/SaveUser',
    method: 'post',
    data
  })
}

// 删除用户
export function DeleteUser(data) {
  return request({
    url: '/Platform/User/DeleteUser',
    method: 'post',
    data
  })
}
// 停用状态
export function UpdateUserStates(data) {
  return request({
    method: 'post',
    url: '/Platform/User/UpdateUserStates',
    data
  })
}
// 批量授权
export function BatchSaveAuthorize(data) {
  return request({
    method: 'post',
    url: '/Platform/Role/BatchSaveAuthorize',
    data
  })
}
// 重置密码
export function ResetPassword(data) {
  return request({
    method: 'post',
    url: '/Platform/User/ResetPassword',
    data
  })
}
// 设为负责人
export function UpdateDepartmentLeader(data) {
  return request({
    url: '/Platform/Department/UpdateDepartmentLeader',
    method: 'post',
    data
  })
}

// 获取用户所属群组
export function GetUserGroup(data) {
  return request({
    method: 'post',
    url: '/SYS​/User​/GetUserGroup',
    data
  })
}

// 获取用户实体
export function GetUserEntity(data) {
  return request({
    method: 'post',
    url: '/Platform/User/GetUserEntity',
    data
  })
}

// 获取用户授权
export function GetAuthObjByUserId(data) {
  return request({
    url: '/Platform/User/GetAuthObjByUserId',
    method: 'post',
    data
  })
}

// 用户导入
export function ImportUser(data) {
  return request({
    url: '/Platform/User/ImportUser',
    method: 'post',
    data
  })
}

// 用户权限导入
export function ImportUserRole(data) {
  return request({
    url: '/Platform/User/ImportUserRole',
    method: 'post',
    data
  })
}

// 附件上传
export function uploader(data) {
  return request({
    url: this.$store.state.uploadUrl,
    method: 'post',
    data
  })
}

// 删除表格
export function DeleteGrid(data) {
  return request({
    method: 'post',
    url: '/Platform/Grid/DeleteGrid',
    data
  })
}

// 删除表格某些列
export function DeleteColumn(data) {
  return request({
    method: 'post',
    url: '/Platform/Grid/DeleteColumn',
    data
  })
}
// 获取表格配置树 (Auth)
export function GetVXEGridByCode(data) {
  return request({
    method: 'post',
    url: '/Platform/Grid/GetVXEGridByCode',
    data
  })
}

// 获取用户列表(分页) (Auth)
export function GetUserPage(data) {
  return request({
    url: '/Platform/User/GetUserPage',
    method: 'post',
    data
  })
}

// 获取用户数据权限弹框
export function GetLogOnUserWorkingObjectTreeList(data) {
  return request({
    url: '/Platform/WorkingObject/GetLogOnUserWorkingObjectTreeList',
    method: 'post',
    data
  })
}

export function GetWorkingObjectProject(data = {}) {
  return request({
    url: '/EPC/Project/GetWorkingObjectProject',
    method: 'post',
    data
  })
}

export function GetAuthFactoryList(data = {}) {
  return request({
    url: '/pro/factory/GetAuthFactoryList',
    method: 'post',
    data
  })
}

export function GetWorking_ObjectList(data) {
  return request({
    url: '/PLM/Plm_Projects/GetWorking_ObjectList',
    method: 'post',
    data
  })
}

export function GetReportDataTable(data) {
  return request({
    url: '/PRO/PrintTemplate/GetReportDataTable',
    method: 'post',
    data
  })
}
// 数据权限弹框记录点击次数
export function RecordLastLoginProject(data) {
  return request({
    url: '/Platform/User/RecordLastLoginProject',
    method: 'post',
    data
  })
}

export function SecurityToken(data) {
  return request({
    url: '/Platform/Sys_File/SecurityToken',
    method: 'post',
    data
  })
}

export function UserChangeWorking(data) {
  return request({
    url: '/Platform/User/UserChangeWorking',
    method: 'post',
    data
  })
}

export function GetTenantCodeByMobile(data) {
  return request({
    url: '/Master/TenantUser/GetTenantCodeByMobile',
    method: 'post',
    data
  })
}

export function ChangeTenant(data) {
  return request({
    url: '/Platform/Login/ChangeTenant',
    method: 'post',
    data
  })
}

// 公司产品列表
export function GetAllProduct(data) {
  return request({
    url: '/Platform/SysProduct/GetAllProduct',
    method: 'post',
    data
  })
}

// 申请试用
export function AddCustomProduct(data) {
  return request({
    url: '/Platform/SysProduct/AddCustomProduct',
    method: 'post',
    data
  })
}

// 购买产品
export function BuyCustomProduct(data) {
  return request({
    url: '/Platform/SysProduct/BuyCustomProduct',
    method: 'post',
    data
  })
}

export function GetPermittedWorkingObjCompanyTrees(data) {
  return request({
    url: '/Platform/WorkingObject/GetPermittedWorkingObjCompanyTrees',
    method: 'post',
    data
  })
}

export function GetFileCatalog(data) {
  return request({
    url: '/Platform/Sys_FileType/GetFileCatalog',

    method: 'post',
    data
  })
}

export function GetFileType(data) {
  return request({
    url: '/Platform/Sys_FileType/GetFileType',
    method: 'post',
    data
  })
}

export function GetPermittedBusinessRange(data) {
  return request({
    url: '/Platform/WorkingObject/GetPermittedBusinessRange',
    method: 'post',
    data
  })
}
// 获取oss 加密链接
export function GetOssUrl(data) {
  return request({
    url: '/Platform/Sys_File/GetOssUrl',
    method: 'post',
    data
  })
}
// 获取租户系统设置
export function GetPreferencessettingsByKey(data) {
  return request({
    url: '/Master/Tenant/GetPreferencessettingsByKey',
    method: 'post',
    data
  })
}

// 获取租户
export function GetListByGroupTenant(data) {
  return request({
    url: '/Master/Tenant/GetListByGroupTenant',
    method: 'post',
    data
  })
}
// 删除工厂字典
export function DeleteDictionaryDetail(data) {
  return request({
    url: '/Platform/Dictionary/DeleteDictionaryDetail',
    method: 'post',
    data
  })
}

export function GetCompanyUnionProjectUnionFactoryObjByUserId(data) {
  return request({
    url: '/Platform/WorkingObject/GetCompanyUnionProjectUnionFactoryByUserId',
    method: 'post',
    data
  })
}

export function GetSelectedPermissionByDepartmentId(data) {
  return request({
    url: '/Platform/WorkingObject/GetSelectedPermissionByDepartmentId',
    method: 'post',
    data
  })
}

export function GetSelectedPermissionByUserId(data) {
  return request({
    url: '/Platform/WorkingObject/GetSelectedPermissionByUserId',
    method: 'post',
    data
  })
}

export function GetAllocationAppMessage(data) {
  return request({
    url: '/PRO/ProductionPDA/GetAllocationAppMessage',
    method: 'post',
    data
  })
}

export function GetAllocationLogPageList(data) {
  return request({
    url: '/PRO/ProductionPDA/GetAllocationLogPageList',
    method: 'post',
    data
  })
}
/***2023-08-07 add **/
//岗位类型
export function GetRole(data) {
  return request({
    url: '/DF/Entrance_PullDown/GetRole',
    method: 'post',
    data
  })
}
//所属部门
export function GetDepartment(data) {
  return request({
    url: '/DF/Entrance_PullDown/GetDepartment',
    method: 'post',
    data
  })
}
//所属单位
export function GetCompany(data) {
  return request({
    url: '/DF/Entrance_PullDown/GetCompany',
    method: 'post',
    data
  })
}
