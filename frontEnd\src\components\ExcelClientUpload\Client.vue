<template>
  <div style="display: inline-block;margin:0 10px">
    <el-button @click="visible=true">导入</el-button>
    <bimdialog
      dialog-title="导入"
      :visible="visible"
      hidebtn
      :dialog-width="width"
      @handleClose="visible=false"
    >
      <input
        v-show="false"
        ref="files"
        accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        class="input-file"
        type="file"
        @change="importData"
      >
      <div style="display: flex;justify-content: space-around">
        <slot name="diyTemplate"></slot>
        <el-button type="success" @click="$refs.downloadUrl.click()" v-if="!diyTemplate">模板下载</el-button>
        <el-button type="primary" @click="$refs.files.click()">上传导入</el-button>
      </div>
      <a ref="downloadUrl" :href="templateUrl" target="_blank" />
    </bimdialog>
  </div>
</template>

<script>
import bimdialog from '@/views/plm/components/dialog'
import XLSX from 'xlsx'
import { GetPreferenceSettingValue } from '@/api/sys/system-setting'

export default {
  name: 'Client',
  components: { bimdialog },
  props: {
    templateName: '', // 下载的文件名称
    range: {
      type: Number,
      default: 0 // 从第index行开始读数据
    },
    width:{
      type:String,
      default:'300px'
    },
    diyTemplate:{
      type: Boolean,
      default:false
    }
  },
  data() {
    return {
      visible: false,
      baseUrl: ''
    }
  },
  computed: {
    templateUrl() {
      return this.baseUrl + this.templateName
    }
  },
  mounted() {
    // 判断是否是下载地址，如果不是http开头，获取默认下载前缀地址
    if (!this.templateName.startsWith('http')) {
      this.getPreUrl()
    }
  },
  methods: {
    getPreUrl() {
      GetPreferenceSettingValue({ Code: 'TemplateDownloadUrl' }).then((res) => {
        if (res.IsSucceed) {
          this.baseUrl = res.Data
        }
      })
    },
    importData(e) {
      const file = e.target.files[0]
      this.$emit('fileSuccess', file)
      this.readWorkbookFromLocalFile(file, (workbook) => {
        this.readWorkbook(workbook)
      })
      e.target.value = '' // 读取文件后，需要把值清空，不然再次上传同一文件，不会触发onchange
    },
    readWorkbookFromLocalFile(file, callback) {
      const reader = new FileReader()
      reader.onload = function(e) {
        const data = e.target.result
        const workbook = XLSX.read(data, { type: 'binary' })
        if (callback) callback(workbook)
      }
      reader.readAsBinaryString(file)
    },
    async readWorkbook(workbook) {
      const sheetNames = workbook.SheetNames // 工作表名称集合
      const jsonArr = sheetNames.map((item, index) => {
        const worksheet = workbook.Sheets[sheetNames[index]] // 读取每一张表的数据
        return XLSX.utils.sheet_to_json(worksheet, { defval: '', range: this.range })
      })
      this.$emit('success', jsonArr,sheetNames) // 返回一个二维数组
      this.closeDialog()
    },
    closeDialog() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
