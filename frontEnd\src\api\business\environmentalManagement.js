//
import request from '@/utils/request'

//  监测档案
// 分页查询设备详情
export function GetEquipmentList(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/GetEquipmentList',
    data
  })
}

// 删除设备
export function DeleteEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/DeleteEquipment',
    data
  })
}
// 新增/编辑设备
export function EditEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/EditEquipment',
    data
  })
}
// 查询设备详情
export function GetEquipmentEntity(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/GetEquipmentEntity',
    data
  })
}

// 导出数据
export function ExportEnvironmentEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/ExportEnvironmentEquipment',
    data
  })
}
// v2 版本导出
export function ExportEquipmentList(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/v2/ExportEquipmentList',
    data
  })
}
// 告警配置
// 获取告警配置列表
export function GetQuotaList(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/GetQuotaList',
    data
  })
}
// 获取告警配置详情
export function GetQuotaEntity(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/GetQuotaEntity',
    data
  })
}

// 新增/编辑告警配置
export function EditQuota(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/EditQuota',
    data
  })
}

// 删除告警配置
export function DeleteQuota(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/DeleteQuota',
    data
  })
}
// 获取设备配置项信息
export function GetEquipmentItemList(data) {
  return request({
    method: 'post',
    url: '/DF/Equipment/GetEquipmentItemList',
    data
  })
}

export function DeleteAllQuota(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/DeleteAllQuota',
    data
  })
}

// 告警信息

// 获取设备配置项信息
export function GetWarningList(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/GetWarningList',
    data
  })
}
// 告警信息类型列表
export function GetWarningType(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/GetWarningType',
    data
  })
}
// 导出告警
export function ExportWarning(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/ExportWarning',
    data
  })
}

// 监测数据
// 获取监测数据
export function GetDataList(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/GetDataList',
    data
  })
}

// 导出设备监测数据
export function ExportDataList(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/ExportDataList',
    data
  })
}

// v2 导出设备监测数据
export function ExportEnvironmentDataList(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/v2/ExportEnvironmentDataList',
    data
  })
}


// 查询历史监测数据
export function GetHistoryDataList(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/GetHistoryDataList',
    data
  })
}

// 查询设备类型
export function GetDictionaryDetailListByCode(data) {
  return request({
    method: 'post',
    url: '/Platform/Dictionary/GetDictionaryDetailListByCode',
    data
  })
}
// 获取园区地址
export function GetParkArea(data) {
  return request({
    method: 'post',
    url: '/DF/DFPark/GetParkArea',
    data
  })
}
// 新增编辑设备类型配置数据
export function EditEnvironmentDTC(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/EditEnvironmentDTC',
    data
  })
}
// 获取设备类型配置数据
export function GetEnviromentDTCList(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/GetEnvironmentDTCList',
    data
  })
}
// 删除设备类型配置数据
export function DeleteEnvironmentDTC(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/DeleteEnvironmentDTC',
    data
  })
}
// 关闭告警
export function UpdateWarningStatus(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/UpdateWarningStatus',
    data
  })
}

// 下载模板
export function EnvironmentImportTemplate(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/EnvironmentImportTemplate',
    data
  })
}
// 批量导入
export function EnvironmentEquipmentImport(data) {
  return request({
    method: 'post',
    url: '/DF/Environment/EnvironmentEquipmentImport',
    data
  })
}

// 环境监测分析
// 获取监测告警类型统计
export function GetWarningTypeAnalyses(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetWarningTypeAnalyses',
    data
  })
}

// 获取车间告警趋势统计
export function GetSceneWarningTrend(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetSceneWarningTrend',
    data
  })
}

// 获取告警趋势统计
export function GetWarningTrend(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetWarningTrend',
    data
  })
}

// 获取最新告警信息
export function GetWarningAnalysesList(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetWarningList',
    data
  })
}

// 获取查看更多跳转模块地址
export function GetJumpUrl(data) {
  return request({
    method: 'get',
    url: '/DF/EnvironmentAnalyses/GetJumpUrl',
    params: data,
  })
}


// 获取车间环境监测分析平均值
export function GetSceneAnalyses(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetSceneAnalyses',
    data
  })
}

// 获取车间环境监测类型
export function GetSceneTypes(data) {
  return request({
    method: 'get',
    url: '/DF/EnvironmentAnalyses/GetSceneTypes',
    params: data,
  })
}

// 获取车间指定监测类型近24小时趋势图
export function GetSceneTrendAnalyses(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetSceneTrendAnalyses',
    data
  })
}

// 获取天气信息
export function GetWeatherInfo(data) {
  return request({
    method: 'get',
    url: '/DF/EnvironmentAnalyses/GetWeatherInfo',
    params: data,
  })
}



