import * as moment from 'moment'
import { Gantt } from 'dhtmlx-gantt'
import { Message } from 'element-ui'
import { getDatesRange } from './gplan'
/**
 * 打开视图中的对话框
 * @param {*} opts 设置项
 * @param {*} mod dialog所在的视图组件
 */
export function openDialog(opts, mod) {
  if (!opts || Object.prototype.toString.call(opts) !== '[object Object]') {
    opts = {}
  }
  mod.dialogCfgs = Object.assign(
    {},
    { component: '', title: '', width: '360px' },
    opts
  )
  mod.dialogShow = true
}
export const ZOOM_LEVELS = [
  // day
  {
    name: 'day',
    scale_height: 60,
    scales: [
      { unit: 'month', format: '<strong>%Y年%m月</strong>', step: 1 },
      {
        unit: 'day',
        step: 1,
        format: '<span data-date=%Y-%m-%d>%d</span>'
      }
    ]
  },
  // week
  {
    name: 'week',
    scale_height: 60,
    scales: [
      { unit: 'year', format: '<strong>%Y年</strong>', step: 1 },
      {
        unit: 'week',
        step: 1,
        format: date => {
          const md = moment(date)
          return `<span>第${md.format('W')}周(${md.format('M/D')}~${moment(md)
            .add(6, 'days')
            .format('M/D')})</span>`
        }
      }
    ]
  },
  // month
  {
    name: 'month',
    scale_height: 60,
    scales: [
      { unit: 'year', format: '<strong>%Y年</strong>', step: 1 },
      {
        unit: 'month',
        step: 1,
        format: '<span data-date=%Y-%m-%d>%m月</span>'
      }
    ]
  },
  // quarter
  {
    name: 'quarter',
    scale_height: 60,
    scales: [
      { unit: 'year', format: '<strong>%Y年</strong>', step: 1 },
      {
        unit: 'quarter',
        step: 1,
        format: date => {
          const md = moment(date)
          return md.format(
            `第Q季度(M-${moment(date)
              .add(2, 'months')
              .format('M')}月)`
          )
        }
      }
    ]
  },
  // year
  {
    name: 'year',
    scale_height: 60,
    scales: [
      {
        unit: 'year',
        step: 5,
        format: date => {
          const md = moment(date)
          return `<strong>${md.format('YYYY')} - ${moment(date)
            .add(5, 'years')
            .format('YYYY')}</strong>`
        }
      },
      {
        unit: 'year',
        step: 1,
        format: '<span data-date=%Y-%m-%d>%Y</span>'
      }
    ]
  }
]
/**
 * 创建甘特实例
 * @param {*} mod 甘特实例所在视图组件
 * @param {*} opts 配置项
 * @param {*} reshecdeled 计算后的新数据
 * @returns {*} 返回视图组件自身
 */
export function createGanttInstance(
  mod,
  opts = {
    el: 'gantt-chart',
    locale: 'cn',
    filters: null,
    editMode: true,
    showBaseLine: false
  },
  reshecdeled
) {
  console.log('gantt edit mode enabled:' + opts.editMode, opts)
  console.log(reshecdeled)
  if (mod.gantt) mod.gantt.destructor()
  mod.gantt = Gantt.getGanttInstance()
  mod.gantt.config.readonly = !opts.editMode // 是否只读
  mod.gantt.i18n.setLocale(opts.locale ?? 'cn') // 本地化
  mod.gantt.config.date_format = '%Y-%m-%d' // 设置用于从数据集解析日期和向服务器发还日期的格式
  mod.gantt.config.drag_progress = false // 禁用进度拖放
  mod.gantt.config.scale_height = 60 // 设置时间缩放条和表格头的高度
  mod.gantt.config.show_task_cells = true
  mod.gantt.config.min_duration = 24 * 60 * 60 * 1000 // 最小 duration 单位
  if (opts.start) {
    mod.gantt.config.start_date = opts.start
  }

  // 里程碑文字
  mod.gantt.templates.rightside_text = (start, end, task) => {
    if (task.type === mod.gantt.config.types.milestone) {
      return task.text
    }
    return ''
  }
  // 树图标
  mod.gantt.templates.grid_folder = item => {
    // 关联运营总控的WBS，图标区分
    return item.Is_Sync && item.Is_Sync != '0'
      ? '<i class="el-icon-link" style="margin-top:10px;margin-right:4px;color:#298DFF;"></i>'
      : ''
  }
  // 叶节点图标
  mod.gantt.templates.grid_file = item => {
    // 关联运营总控的WBS，图标区分
    return item.Is_Sync && item.Is_Sync != '0' && item.type === 'project'
      ? '<i class="el-icon-link" style="margin-top:10px;margin-right:4px;color:#298DFF;"></i>'
      : ''
  }
  const colWbs = {
    align: 'center',
    hide: false,
    label: `<a id="column-set" class="el-link el-link--primary">
      <span class="el-link--inner" style="pointer-events: none;">
        <i style="cursor:pointer;font-size:1.2em;pointer-events: none;" title="栏位设置" class="el-icon-set-up"></i>
      </span>
    </a>`,
    min_width: 80,
    max_width: 80,
    resize: false,
    template: mod.gantt.getWBSCode
  }
  // 列配置
  mod.gantt.config.columns = (opts.gridColumns
    ? [colWbs].concat(opts.gridColumns)
    : [colWbs]
  ).map(c => {
    const cfg = { ...c }
    if (
      cfg.name &&
      (cfg.name.indexOf('Float') > -1 || cfg.name.indexOf('Difference') > -1)
    ) {
      cfg.template = function(obj) {
        const isLay = Number(obj[cfg.name]) && Number(obj[cfg.name]) < 0
        return `<span style="color:${isLay ? '#FB6B7F' : ''};font-weight:${
          isLay ? 'bold' : 'normal'
        };">${
          obj[cfg.name] === undefined || obj[cfg.name] === null
            ? ''
            : obj[cfg.name]
        }</span>`
      }
    }
    return cfg
  })
  // 忽略时间
  mod.gantt.config.work_time = true // 从计算中移除非工作时间
  mod.gantt.config.skip_off_time = true // 隐藏图表中的非工作时间
  mod.gantt.config.duration_unit = 'day'
  mod.gantt.config.inherit_calendar = true
  // 取消默认周六日周末
  mod.gantt.setWorkTime({ day: 6, hours: ['0:00-23:59'] })
  mod.gantt.setWorkTime({ day: 0, hours: ['0:00-23:59'] })
  // 应用默认日历
  if (opts.calendar) {
    opts.calendar.week_calendar.forEach((v, i) => {
      const day = i
      const hours = v === 0 ? false : ['0:00-23:59']
      mod.gantt.setWorkTime({ day: day, hours: hours })
    })
    opts.calendar.out_calendar.forEach(ex => {
      const hours = ex.type === 0 ? false : ['0:00-23:59']
      const dates = getDatesRange(ex)
      dates.forEach(d => {
        mod.gantt.setWorkTime({
          date: moment(d)
            .startOf('date')
            .toDate(),
          hours: hours
        })
      })
    })
  }
  // 忽略日期
  mod.gantt.ignore_time = date => {
    if (!mod.gantt.isWorkTime(date)) return true
  }

  // 插件支持
  mod.gantt.plugins({
    marker: true,
    tooltip: true,
    fullscreen: true,
    drag_timeline: true, // 允许拖动任务甘特图区域
    auto_scheduling: true, // 启用插件
    critical_path: true, // 启用关键路径插件
    overlay: true,
    click_drag: false // 拖动绘制作业
  })
  mod.gantt.config.highlight_critical_path = true
  // 禁用自动排程
  mod.gantt.config.auto_scheduling = false
  //
  mod.gantt.config.drag_timeline = {
    ignore: '.gantt_task_line, .gantt_task_link, .gantt_marker',
    useKey: false
  }
  // 拖动改变顺序
  mod.gantt.config.sort = true
  mod.gantt.config.order_branch = 'marker'
  mod.gantt.config.order_branch_free = false
  mod.gantt.attachEvent('onBeforeTaskMove', (id, parent, tindex) => {
    const to = mod.gantt.getTask(parent)
    if (to && to.type !== 'project') return false
    return true
  })
  mod.gantt.attachEvent('onAfterTaskMove', (id, parent, tindex) => {
    if (
      mod.onAfterTaskMove &&
      Object.prototype.toString.call(mod.onAfterTaskMove) ===
        '[object Function]'
    ) {
      mod.onAfterTaskMove(id, parent, tindex)
    }
  })

  mod.gantt.attachEvent('onGridResizeEnd', () => {
    if (
      mod.onGridResizeEnd &&
      Object.prototype.toString.call(mod.onGridResizeEnd) ===
        '[object Function]'
    ) {
      mod.onGridResizeEnd()
    }
  })
  mod.gantt.attachEvent('onGanttScroll', (left, top) => {
    if (
      mod.onGanttScroll &&
      Object.prototype.toString.call(mod.onGanttScroll) === '[object Function]'
    ) {
      mod.onGanttScroll(left, top)
    }
  })
  // 拖动绘制作业
  mod.gantt.config.click_drag = {
    callback: mod.onClickDragEnd ?? (() => {}),
    singleRow: true
  }
  // 目标横道
  if (opts?.showBaseLine) {
    mod.gantt.config.row_height = 48
    mod.gantt.config.bar_height = mod.gantt.config.row_height / 2 - 4
    mod.gantt.addTaskLayer({
      renderer: {
        render: task => {
          if (task.Target_Start_Date && task.Target_End_Date) {
            var sizes = mod.gantt.getTaskPosition(
              task,
              moment(task.Target_Start_Date)
                .startOf('date')
                .toDate(),
              moment(task.Target_End_Date)
                .add(1, 'days')
                .startOf('date')
                .toDate()
            )
            var el = document.createElement('div')
            const actProgressBar = document.createElement('span')
            el.appendChild(actProgressBar)
            actProgressBar.style.width = task.Actual_Progress * 100 + '%'
            el.className =
              task.type === 'milestone' ? 'basediamond' : 'baseline'
            el.style.left = sizes.left + 'px'
            if (task.type !== 'milestone') {
              el.style.width = sizes.width + 'px'
            }

            el.style.top = sizes.top + mod.gantt.config.bar_height + 13 + 'px'
            return el
          }
          return false
        }
      }
    })
  }
  // 设置 tooltip
  mod.gantt.attachEvent('onGanttReady', () => {
    const tooltips = mod.gantt.ext.tooltips
    mod.gantt.templates.tooltip_text = (start, end, task) => {
      if (task.type === 'milestone') {
        return ''
      }
      const Start_Date = toDateStr(start)
      const End_Date = toDateStr(moment(end).add(-1, 'days'))
      const Duration = moment(end).diff(start, 'days')
      let Progress = 0
      if (task.type == 'task') {
        Progress = task.Actual_Progress
      }
      if (task.type == 'project') {
        Progress = task.progress
      }
      return `<div class="custom-plan-tooltip">
        <i class="cross-icon el-icon-close" onclick="this.parentNode.parentNode.remove()" class="el-icon-close"></i>
      <header><h3>${
        task.text
      }</h3><p>${Start_Date} ~ ${End_Date}</p></header><div>
        <b>工期：</b>${Duration} 天<br/>
        <b>进度：</b>${(Progress * 100).toFixed(1)}%<br/>
        <b>备注：</b>${task.Remark ?? ''}</div></div>`
    }
  })

  // 聚光灯框中任务样式
  mod.gantt.templates.grid_row_class = (start, end, task) => {
    const marker = mod.gantt.getMarker('spot-marker')
    if (!marker) return ''
    if (
      (moment(marker.start_date).isSameOrAfter(start) &&
        moment(marker.end_date).isSameOrBefore(end)) ||
      (moment(end).isSameOrAfter(marker.start_date) &&
        moment(end).isSameOrBefore(marker.end_date)) ||
      (moment(start).isSameOrAfter(marker.start_date) &&
        moment(start).isSameOrBefore(marker.end_date))
    ) {
      return 'spotted'
    }
    return ''
  }
  // 类型样式
  mod.gantt.templates.task_class = (start, end, task) => {
    switch (task.type) {
      case 'task':
        return 'custom-gantt-bar-task'
        break
      case 'milestone':
        return 'custom-gantt-bar-milestone'
        break
      case 'project':
        return 'custom-gantt-bar-project'
        break
    }
  }
  // 时间区行样式
  mod.gantt.templates.task_row_class = (start, end, task) => {
    switch (task.type) {
      case 'task':
        return 'custom-gantt-row-task'
        break
      case 'milestone':
        return 'custom-gantt-row-milestone'
        break
      case 'project':
        return 'custom-gantt-row-project'
        break
    }
  }
  // 添加日期线
  const dataDate = mod.plan.Cur_Data_Date
    ? moment(mod.plan.Cur_Data_Date).toDate()
    : moment(mod.plan.Plan_Start_Date).toDate()
  const marker = mod.gantt.addMarker({
    id: 'data-date',
    start_date: dataDate,
    css: 'today',
    text: '数据日期',
    title: `${moment(dataDate).format('YYYY-MM-DD')}`
  })

  mod.gantt.attachEvent('onGanttRender', () => {
    if (
      mod.onGanttRendered &&
      Object.prototype.toString.call(mod.onGanttRendered) ===
        '[object Function]'
    ) {
      mod.onGanttRendered()
    }
  })
  // 显示过滤
  mod.gantt.attachEvent('onBeforeTaskDisplay', (id, task) => {
    if (mod.gantt.isCriticalTask(task)) {
      task.iscriticalPath = 1
    } else {
      delete task.iscriticalPath
    }
    task.Wbs_Code = task.$wbs
    if (task.type === 'project') {
      setTimeout(() => {
        const bar = document.querySelector(
          '.custom-gantt-bar-project[task_id="' + id + '"]'
        )
        if (bar) {
          let lbag = bar.querySelector('.lbag')
          if (!lbag) {
            lbag = document.createElement('span')
            lbag.className = 'lbag'
            bar.appendChild(lbag)
          }

          let rbag = bar.querySelector('.rbag')
          if (!rbag) {
            rbag = document.createElement('span')
            rbag.className = 'rbag'
            bar.appendChild(rbag)
          }
        }
      }, 100)
    }
    if (!opts.filters) return true
    if (opts.filters.check(task)) {
      return true
    }
    return false
  })
  // 设置布局，支持表格列滚动
  mod.gantt.config.layout = {
    css: 'gantt_container',
    cols: [
      {
        width: opts.gridWidth ? opts.gridWidth : 500,
        min_width: opts.gridWidth ? opts.gridWidth : 500,
        rows: [
          {
            view: 'grid',
            scrollX: 'gridScroll',
            scrollable: true,
            scrollY: 'scrollVer'
          },
          { view: 'scrollbar', id: 'gridScroll', group: 'horizontal' }
        ]
      },
      { resizer: true, width: 1 },
      {
        rows: [
          { view: 'timeline', scrollX: 'scrollHor', scrollY: 'scrollVer' },
          { view: 'scrollbar', id: 'scrollHor', group: 'horizontal' }
        ]
      },
      { view: 'scrollbar', id: 'scrollVer' }
    ]
  }
  // 缩放配置
  mod.gantt.ext.zoom.init({
    levels: ZOOM_LEVELS,
    element: function() {
      return mod.gantt.$root.querySelector('.gantt_task')
    }
  })
  mod.gantt.ext.zoom.setLevel('day')
  mod.gantt.$zoomToFit = false
  // gantt 扩大为全屏
  mod.gantt.attachEvent('onExpand', () => {
    setTimeout(() => {
      const wrapper = mod.gantt.ext.fullscreen.getFullscreenElement()
      wrapper.style.zIndex = 9999
    }, 120)
    if (
      mod.onFullScreenChanged &&
      Object.prototype.toString.call(mod.onFullScreenChanged) ===
        '[object Function]'
    ) {
      mod.onFullScreenChanged()
    }
  })
  // gantt 退出全屏时
  mod.gantt.attachEvent('onCollapse', () => {
    setTimeout(() => {
      const wrapper = mod.gantt.ext.fullscreen.getFullscreenElement()
      wrapper.style.zIndex = 1
    }, 120)
    if (
      mod.onFullScreenChanged &&
      Object.prototype.toString.call(mod.onFullScreenChanged) ===
        '[object Function]'
    ) {
      mod.onFullScreenChanged()
    }
  })
  // 空白区域点击取消选择
  mod.gantt.attachEvent('onEmptyClick', e => {
    mod.gantt.unselectTask()
    if (
      mod.onTaskUnselected &&
      Object.prototype.toString.call(mod.onTaskUnselected) ===
        '[object Function]'
    ) {
      mod.onTaskUnselected()
    }
  })
  // 阻止默认的编辑弹出框
  mod.gantt.attachEvent('onTaskDblClick', (id, e) => {
    if (
      mod.onTaskDblClick &&
      Object.prototype.toString.call(mod.onTaskDblClick) === '[object Function]'
    ) {
      mod.onTaskDblClick(id, e)
    }
    return false
  })
  // 拖动之前
  mod.gantt.attachEvent('onBeforeTaskDrag', (id, mode, e) => {
    const task = mod.gantt.getTask(id)
    if (mod.canDragAndResizeTask && !mod.canDragAndResizeTask(task)) {
      return false
    }
    if (
      task.Actual_Progress ||
      task.Actual_Start_Date ||
      task.Actual_End_Date
    ) {
      return false
    }
    return true
  })
  // 拖动结束
  mod.gantt.attachEvent('onAfterTaskDrag', (id, mode, e) => {
    var modes = mod.gantt.config.drag_mode
    switch (mode) {
      case modes.move:
        if (
          mod.onTaskDragMoveEnd &&
          Object.prototype.toString.call(mod.onTaskDragMoveEnd) ===
            '[object Function]'
        ) {
          mod.onTaskDragMoveEnd(id)
        }
        break
      case modes.resize:
        if (
          mod.onTaskResizeEnd &&
          Object.prototype.toString.call(mod.onTaskResizeEnd) ===
            '[object Function]'
        ) {
          mod.onTaskResizeEnd(id)
        }
        break
      case modes.progress:
        break
    }
  })

  // 阻止默认关系线弹出框
  mod.gantt.attachEvent('onLinkDblClick', (id, e) => {
    if (
      mod.onLinkDblClick &&
      Object.prototype.toString.call(mod.onLinkDblClick) === '[object Function]'
    ) {
      if (mod.canAddLink && !mod.canAddLink(id)) {
        mod.$message.warning('无权限')
        return false
      }
      mod.onLinkDblClick(id)
    }
    return false
  })
  // 连接添加前
  mod.gantt.attachEvent('onBeforeLinkAdd', (id, link) => {
    if (mod.canAddLink && !mod.canAddLink(link)) {
      mod.$message.warning('无权限')
      return false
    }
    const source = mod.gantt.getTask(link.source)
    const target = mod.gantt.getTask(link.target)
    if (source.type === 'project' || target.type === 'project') {
      Message({
        type: 'warning',
        message: '不支持在WBS作业上创建连接'
      })
      return false
    }
  })
  // 任务被选择
  mod.gantt.attachEvent('onTaskSelected', function(id) {
    if (
      mod.onTaskSelected &&
      Object.prototype.toString.call(mod.onTaskSelected) === '[object Function]'
    ) {
      mod.onTaskSelected(id)
    }
  })
  // 初始化
  mod.gantt.init(opts.el)
  try {
    mod.gantt.parse(reshecdeled || mod.ganttData)
  } catch (err) {
    console.error(err.message)
  }
  return mod.gantt
}

export class GantFilters {
  checkers
  matchto
  constructor(checkers, matchto) {
    this.checkers = checkers ?? []
    this.matchto = matchto ?? 'ONE'
  }
  check(task) {
    if (this.checkers.length <= 0) return true
    const matched = []
    this.checkers.forEach(chk => {
      if (chk.type == 'field') {
        matched.push(this.fieldMatch(task[chk.field], chk.value))
      } else if (chk.type == 'critical') {
        console.log(chk.field, chk.value)
        matched.push(this.fieldMatch(task[chk.field], chk.value))
      } else if (chk.type == 'daterange' || chk.type === 'threeweeks') {
        matched.push(this.dateRangeMatch(task, chk.value))
      } else if (chk.type == 'keyword') {
        matched.push(this.textMatch(task, chk.value))
      }
    })
    if (this.matchto === 'ALL') {
      return matched.filter(m => m).length === this.checkers.length
    } else {
      return matched.filter(m => m).length > 0
    }
  }
  fieldMatch(v1, v2) {
    return v1 === v2
  }
  textMatch(task, keyword) {
    return (
      task.text.indexOf(keyword) > -1 ||
      (task.Responsible_UserName &&
        task.Responsible_UserName.indexOf(keyword) > -1)
    )
  }
  dateRangeMatch(task, range) {
    console.log(task)
    const s = moment(task.Dynamic_Start_Date.split('A')[0]).toDate()
    const e = moment(task.Dynamic_End_Date.split('A')[0])
    return (
      moment(s).isSameOrAfter(range[0]) && moment(e).isSameOrBefore(range[1])
    )
  }
}
export function openDrawer(opts, mod) {
  mod.$refs.drawer.closeDrawer()
  if (!opts || Object.prototype.toString.call(opts) !== '[object Object]') {
    opts = {}
  }

  const interval = setInterval(() => {
    if (mod.canOpenNewDrawer) {
      mod.drawerCfgs = Object.assign({}, mod.drawerCfgs, opts, {
        props: Object.assign({}, opts.props)
      })
      mod.drawerShow = true
      clearInterval(interval)
    }
  }, 30)
}
export function toDateStr(v) {
  if (!v) return ''
  return moment(v)
    .startOf('date')
    .format('YYYY-MM-DD')
}
/**
 * 计划权限验证辅助类
 */
export class PlanAuth {
  static ACCESSES = {
    /**
     * 查看权限
     */
    READ: 1 << 0,
    /**
     * 作业编辑权限
     */
    EDIT: 1 << 1,
    /**
     * 作业添加权限
     */
    ADD: 1 << 2,
    /**
     * 作业删除权限
     */
    DELETE: 1 << 3,
    /**
     * 计划全局关系管理权限
     */
    RELATION: 1 << 4,
    /**
     * WBS管理权限
     */
    WBS: 1 << 5
  }
  static Roles = {
    负责: ['READ', 'EDIT', 'ADD', 'DELETE', 'RELATION', 'WBS'],
    参与: ['READ', 'EDIT', 'ADD', 'DELETE'],
    查看: ['READ']
  }
  /**
   * @param {*} planAuth plan对象的Plan_Auth属性。
   *  可用值为: 负责/参与/查看
   */
  constructor(planAuth) {
    this.auth = planAuth
    this.access = this.getAuth(this.auth)
  }
  /**
   * 获得当前可用权限
   */
  getAuth() {
    if (!PlanAuth.Roles[this.auth]) return []
    return PlanAuth.Roles[this.auth].map(a => {
      return PlanAuth.ACCESSES[a]
    })
  }
  /**
   * 检查是否具有某个权限
   * @param {*} v 多权限进行位或运算的结果
   */
  check(v) {
    let acc
    this.access.forEach(a => {
      if (acc === undefined) {
        acc = a
      } else {
        acc = acc | a
      }
    })
    return (acc & v) > 0
  }
  /**
   * 获取当前角色的权限数值
   * @returns {number} 权限数值，用于简单比较
   */
  getValue() {
    let acc
    this.access.forEach(a => {
      if (acc === undefined) {
        acc = a
      } else {
        acc = acc | a
      }
    })
    return acc ?? 0
  }
}
