<template>
  <div>
    <!-- <div>
      <el-upload
        class="upload-demo"
        drag
        :http-request="Upload"
        action="string"
        :before-upload="beforeUpload"
        :on-exceed="handleExceed"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :file-list="fileDataWatch"
        :multiple="uploadParams.multiple"
        :limit="uploadParams.fileLimits"
        :disabled="uploadParams.disabled"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          <div>将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip">{{ this.uploadParams.fileTips }}</div>
        </div>
      </el-upload>
    </div> -->
    <div>
      <el-upload
        :http-request="Upload"
        action="string"
        list-type="picture-card"
        :before-upload="beforeUpload"
        :on-preview="handlePictureCardPreview"
        :file-list="fileDataWatch"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :limit="uploadParams.fileLimits"
        :multiple="uploadParams.multiple"
        :disabled="uploadParams.disabled"
      >
        <!-- <i class="el-icon-plus" /> -->
        <i slot="default" class="el-icon-plus" />
        <div slot="file" slot-scope="{file}">
          <img
            class="el-upload-list__item-thumbnail"
            :src="file.url"
            alt=""
          >
          <span class="el-upload-list__item-actions">
            <span
              class="el-upload-list__item-preview"
              @click="handlePictureCardPreview(file)"
            >
              <i class="el-icon-zoom-in" />
            </span>
            <span

              class="el-upload-list__item-delete"
              @click="handlePreview(file)"
            >
              <i class="el-icon-download" />
            </span>
            <span
              class="el-upload-list__item-delete"
              @click="handleRemove(file)"
            >
              <i v-if="!uploadParams.disabled" class="el-icon-delete" />
            </span>
          </span></div></el-upload>
      <el-dialog :visible.sync="dialogVisible" append-to-body>
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { ossClient } from './oss'
import { getToken } from '@/utils/getOssToken'
import { getTenantId } from '@/utils/tenant'
import { GetOssUrl } from '@/api/sys/index'
export default {
  name: 'OssUpload',
  props: {
    uploadParams: {
      type: Object,
      default: {}
    },
    fileData: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      tokenInfo: null,
      fileDataWatch: [],
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  watch: {
    fileData: {
      handler(newValue, oldValue) {
        // if (newValue.length == 0 || newValue == '') {
        //   return false
        // }
        if (newValue.length > 0) {
          let Url = ''
          newValue.map(async(v) => {
            Url = await GetOssUrl({ url: v.url })
            v.url = Url.Data
            return v
          })
        }

        this.fileDataWatch = newValue
        // console.log(newValue)
        // console.log(this.fileDataWatch)
      },
      immediate: true
    }
  },
  mounted() {

  },
  methods: {
    //  没引用
    successUpload(res, file, fileList) {

    },
    //  没引用
    uploadFileProcess(event, file, fileList) {

    },

    // 文件列表移除前时 没引用
    beforeRemove(file, fileList) {

    },

    // 删除文件(基于文件列表的数据进行操作) 没引用
    removeFile(item, index) {

    },

    // 下载文件(基于文件列表的数据进行操作) 没引用
    downLoadFile(item, index) {

    },

    // 文件超出个数限制时
    handleExceed(files, fileList) {
      this.$message.warning('当前限制选择 ' + this.uploadParams.fileLimits + ' 个文件，本次选择了 ' + files.length + ' 个文件，共选择了 ' + parseInt(files.length + fileList.length) + ' 个文件')
    },

    // 上传前处理
    beforeUpload(file) {
      if (this.fileDataWatch.length >= this.uploadParams.fileLimits) {
        this.$message.error(`最多只能上传 ${this.uploadParams.fileLimits} 个文件`)
        return false
      }
      const tmpcnt = file.name.lastIndexOf('.')
      const exname = file.name.substring(tmpcnt + 1)
      let isLtSize = true
      if (this.uploadParams.fileSize) {
        isLtSize = file.size / 1024 / 1024 < this.uploadParams.fileSize
      }

      if (this.uploadParams.fileType.indexOf(exname) == -1) {
        this.$message.error('请上传正确的文件格式')
        return false
      }
      if (!isLtSize) {
        this.$message.error('上传文件大小不能超过' + (this.uploadParams.fileSize) + 'MB哦!')
        return false
      }
    },

    // 移除
    handleRemove(file, fileList) {
      console.log(file)
      const that = this
      let index = 0
      for (let i = 0; i < that.fileDataWatch.length; i++) {
        if (that.fileDataWatch[i].url == file.url) {
          index = i
        }
      }
      if (file && file.status === 'success') {
        that.fileDataWatch.splice(index, 1)
      }
      that.$emit('getFileData', that.fileDataWatch)
    },

    // 点击文件列表中已上传的文件时
    async handlePreview(file) {
      // console.log(file, that.fileDataWatch)
      const that = this
      let url = ''
      for (let i = 0; i < that.fileDataWatch.length; i++) {
        if (that.fileDataWatch[i].url == file.url) {
          url = that.fileDataWatch[i].url
        }
      }
      const newUrl = await GetOssUrl({ url })
      console.log(newUrl)
      window.open(newUrl.Data) // 每次打开都是钱啊
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    async Upload(file) {
      console.log(file)
      const that = this
      const fileRes = file.file
      // 判断扩展名
      const tmpcnt = fileRes.name.lastIndexOf('.')
      const exname = fileRes.name.substring(tmpcnt + 1)
      const realName = fileRes.name.substring(0, tmpcnt)
      const date = new Date()
      const dateFolder = date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate() + '/' + fileRes.uid // 当前时间
      const fileFullName = dateFolder + '/' + fileRes.name
      that.tokenInfo = await getToken()
      ossClient(that.tokenInfo).multipartUpload(fileFullName, fileRes, {
        progress: function(p) {
          that.fileDataWatch.forEach(item => {
            if (item.url == fileRes.url) {
              item.progressFlag = true
              item.progressPercent = Math.floor(p * 100)
            }
          })
        }
      }).then((result, fileList) => {
        // 下面是如果对返回结果再进行处理，根据项目需要
        if (result.res.statusCode === 200) {
          const fileUrl = result.res.requestUrls[0]
          let requestUrlStr = ''
          let requestUrlStrRes = ''
          requestUrlStr = result.res.requestUrls[0].lastIndexOf('?') === -1 ? fileUrl : fileUrl.substr(0, fileUrl.lastIndexOf('?'))
          requestUrlStrRes = requestUrlStr.substr(0, requestUrlStr.lastIndexOf('/')) + '/' + fileRes.name
          that.fileDataWatch.push({
            name: fileRes.name,
            url: requestUrlStrRes,
            uid: fileRes.uid,
            size: fileRes.size,
            suffix: exname
          })
          that.$emit('getFileData', that.fileDataWatch)
        }
        file.onSuccess('配时文件上传成功')
      }).catch((err) => {
        file.onError('配时文件上传失败(' + err + ')，' + err)
        console.log('err:', err)
      })
    }
  }
}
</script>

<style lang="scss" scoped>

    ::v-deep {
        .upload-demo {
            width: 100%;
            .el-upload {
                width: 100%;
                .el-upload-dragger {
                    width: 100%
                }
            }
        }
    }
</style>
