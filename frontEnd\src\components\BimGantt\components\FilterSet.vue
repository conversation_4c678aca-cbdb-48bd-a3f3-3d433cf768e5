<template>
  <div style="margin-top:-16px;">
    <el-form ref="form" :model="form" label-width="100px">
      <el-form-item label="过滤条件">
        <el-checkbox-group
          v-model="form.checkList"
          style="display:flex;flex-direction:column;"
        >
          <el-checkbox
            label="task"
            @change="changeCheckList('task', $event)"
          >一般作业</el-checkbox>
          <el-checkbox
            label="milestone"
            @change="changeCheckList('milestone', $event)"
          >里程碑作业</el-checkbox>
          <el-checkbox
            label="project"
            @change="changeCheckList('project', $event)"
          >WBS</el-checkbox>
          <el-checkbox
            label="critical"
            @change="changeCheckList('critical', $event)"
          >关键作业</el-checkbox>
          <el-checkbox v-if="false" label="keyword">关键字</el-checkbox>
          <el-checkbox
            label="threeweeks"
            @change="changeCheckList('threeweeks', $event)"
          >三周滚动时间</el-checkbox>
          <el-checkbox
            label="daterange"
            @change="changeCheckList('daterange', $event)"
          >时间周期</el-checkbox>
          <div
            v-if="form.checkList.indexOf('daterange') > -1"
            style="background:#EEE;padding:12px;border-radius:4px;margin-top:10px;padding-left:20px;"
          >
            <el-date-picker
              v-model="form.dates"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </div>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="匹配方式">
        <el-radio-group
          v-model="form.type"
          style="display:flex;flex-direction:column;"
        >
          <el-radio
            :label="'ONE'"
            style="margin:8px 0 16px 0;"
          >满足任意一条选择的条件（并集）</el-radio>
          <el-radio :label="'ALL'">满足所有选择的条件（交集）</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item align="right">
        <el-button @click="$emit('dialogCancel')">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import * as moment from 'moment'
import { GantFilters } from '../util'
export default {
  name: 'FilterSet',
  props: {
    plan: {
      type: Object,
      default: () => ({})
    },
    checkers: {
      type: Array,
      default: () => []
    },
    matchto: {
      type: String,
      default: 'ONE'
    }
  },
  data() {
    return {
      form: {
        type: 'ONE',
        checkList: [],
        dates: []
      }
    }
  },
  computed: {
    threeWeekRange() {
      const currentDate = this.plan.Cur_Data_Date
        ? moment(this.plan.Cur_Data_Date)
        : moment(this.plan.Plan_Start_Date)
      const curWeekStart = moment(currentDate)
        .startOf('isoweek')
        .toDate()
      const s = moment(curWeekStart)
        .add(-7, 'days')
        .toDate()
      const e = moment(curWeekStart)
        .add(13, 'days')
        .toDate()
      return [moment(s).format('YYYY-MM-DD'), moment(e).format('YYYY-MM-DD')]
    }
  },
  created() {
    this.checkers.forEach(c => {
      if (c.type === 'field') {
        this.form.checkList.push(c.value)
      } else if (c.type === 'critical') {
        this.form.checkList.push(c.type)
      } else if (c.type === 'daterange' || c.type === 'threeweeks') {
        this.form.checkList.push(c.type)
        this.form.dates = c.value.concat([])
      } else if (c.type === 'keyword') {
        this.form.checkList.push(c.type)
      }
    })
    this.form.type = this.matchto
    console.log(this.form)
  },
  methods: {
    submit() {
      /**
       * FILTERS 例子
        new BGT.GantFilters([{
            type:'field',
            field:'type',
            value:'project'
          },{
            type:'daterange',
            value:['2020-01-01','2021-12-31']
          }], 'ONE')
       */

      console.log(this.checkers)
      console.log(this.form)
      const filterIns = new GantFilters([], this.form.type)
      this.form.checkList.forEach(item => {
        console.log(item)
        if (item === 'milestone' || item === 'project' || item === 'task') {
          filterIns.checkers.push({
            type: 'field',
            field: 'type',
            value: item
          })
        } else if (item === 'critical') {
          filterIns.checkers.push({
            type: 'critical',
            field: 'iscriticalPath',
            value: 1
          })
        } else if (item === 'threeweeks' || item === 'daterange') {
          filterIns.checkers.push({
            type: item,
            value: this.form.dates
          })
        }
      })
      console.log(filterIns)
      this.$emit('dialogFormSubmitSuccess', {
        type: 'setGanttFilters',
        data: filterIns
      })
    },
    changeCheckList(key, evt) {
      if (key === 'daterange' && evt === true) {
        this.form.checkList = this.form.checkList.filter(
          c => c !== 'threeweeks'
        )
      }
      if (key === 'threeweeks' && evt === true) {
        this.form.checkList = this.form.checkList.filter(c => c !== 'daterange')
        this.form.dates = this.threeWeekRange.concat([])
      }
      if ((key === 'threeweeks' || key === 'daterange') && evt === false) {
        this.form.dates = []
      }
    }
  }
}
</script>
