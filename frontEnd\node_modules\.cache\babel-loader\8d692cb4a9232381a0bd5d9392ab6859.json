{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\realVideo\\Screen.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\realVideo\\Screen.vue", "mtime": 1754618172895}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\babel.config.js", "mtime": 1724291900320}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["LivePlayer", "GetEquipmentTree", "LookVideo", "components", "data", "q", "players", "playerIdx", "colSpan", "<PERSON><PERSON><PERSON><PERSON>", "loadedData", "localData", "num1", "num4", "num9", "channelListDlgTitle", "protocol", "showTree", "showGroupTree", "showTip", "treeLoading", "groupTreeLoading", "queryDevTreeLoading", "queryGroupTreeLoading", "defExpandDevs", "dev<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "groupLevelFilter", "fullscreenFlag", "contextMenuTarget", "contextMenuVisible", "contextMenuNodeData", "treeProps", "bSmartStream", "bVideoTitle", "level", "bPlayerFullscreen", "outHevcTipIdx", "activeTab", "filterText", "treeData", "defaultProps", "children", "label", "defaultExpandedKeys", "computed", "playerBtnGroup", "list", "num", "name", "playing", "player", "url", "treeEmptyText", "showQueryDevTree", "showQueryGroupTree", "watch", "newVal", "oldVal", "_this", "_loop", "idx", "_url", "bSmart", "$nextTick", "_ret", "val", "$refs", "tree", "filter", "mounted", "setPlayer<PERSON>ength", "document", "querySelector", "getEquipmentTree", "beforeRouteEnter", "to", "from", "next", "vm", "query", "beforeRouteUpdate", "clearVideos", "beforeRouteLeave", "methods", "closeVideo", "play", "index", "channel", "_this2", "console", "log", "i", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "_player", "value", "err", "e", "f", "$message", "type", "message", "bLoading", "setPlayerIdx", "Node", "node", "$set", "id", "ID", "then", "res", "IsSucceed", "videoUrl", "Data", "code", "title", "$delete", "push", "length", "JSON", "stringify", "setLocalData", "error", "Message", "manual", "arguments", "undefined", "closeTimer", "clearTimeout", "mediaInfo", "bCloseShow", "bControls", "bFullscreen", "poster", "osd", "ptzType", "serial", "len", "closeInterval", "newIdx", "onError", "String", "videoCodec", "startsWith", "onPlay", "t", "onFullscreenChange", "$", "concat", "draggable", "handle", "cancel", "containment", "delay", "fullscreen", "_this3", "$fullscreen", "enter", "$el", "wrap", "callback", "treeNodeClick", "_node$data$ParentId", "_node$data$ParentId2", "_this4", "<PERSON><PERSON><PERSON><PERSON>", "ParentId", "contextMenuNode", "clickCnt", "Id", "Name", "_this5", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_res$Data", "_callee$", "_context", "prev", "sent", "_res$Data$", "_res$Data$2", "_res$Data$3", "_res$Data$4", "_res$Data$5", "_res$Data$6", "_res$Data$7", "Children", "getNode", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "stop", "filterNode", "indexOf"], "sources": ["src/views/business/safetyManagement/realVideo/Screen.vue"], "sourcesContent": ["<template>\n  <div class=\"screenContent app-container abs100\">\n    <el-card class=\"box-card h100\">\n      <div class=\"text-center\">\n        <el-button-group class=\"player-btn-group\">\n          <el-button\n            v-for=\"list in playerBtnGroup\"\n            :key=\"list.num\"\n            type=\"primary\"\n            :class=\"{ active: playerLength == list.num }\"\n            @click.prevent=\"setPlayerLength(list.num)\"\n          >{{ list.name }}</el-button>\n        </el-button-group>\n      </div>\n      <el-container class=\"customContainer\">\n        <el-aside class=\"aside\" width=\"400px\">\n          <el-input v-model=\"filterText\" placeholder=\"输入设备名称搜索\" />\n          <el-tree\n            ref=\"tree\"\n            class=\"filter-tree\"\n            :data=\"treeData\"\n            :props=\"defaultProps\"\n            highlight-current\n            node-key=\"Id\"\n            :filter-node-method=\"filterNode\"\n            :default-expanded-keys=\"defaultExpandedKeys\"\n            @node-click=\"treeNodeClick\"\n          />\n        </el-aside>\n        <el-main>\n          <el-row class=\"video-show\">\n            <el-col\n              v-for=\"(player, index) in players\"\n              :key=\"index\"\n              :span=\"colSpan\"\n              class=\"video\"\n              @click=\"clickPlayer(player, index, $event)\"\n              @touchend=\"clickPlayer(player, index, $event)\"\n            >\n              <LivePlayer\n                :video-url=\"player.url\"\n                :water-mark=\"player.osd\"\n                v-loading=\"player.bLoading\"\n                :smart=\"player.bSmart\"\n                :poster=\"player.poster\"\n                :controls=\"player.bControls && !loopPlaying\"\n                live\n                muted\n                stretch\n                :loading.sync=\"player.bLoading\"\n                element-loading-text=\"加载中...\"\n                element-loading-background=\"#000\"\n                @fullscreen=\"onFullscreenChange(player, index, $event)\"\n                @error=\"onError(player, index, $event)\"\n                @play=\"onPlay(player, index, $event)\"\n                @message=\"$message\"\n              />\n              <div\n                v-if=\"bVideoTitle && player.title\"\n                class=\"video-title\"\n                :title=\"player.title\"\n              >\n                {{ player.title }}\n              </div>\n              <div\n                v-show=\"player.url && player.bCloseShow && !loopPlaying\"\n                class=\"video-close\"\n                @click=\"closeVideo(index, true)\"\n              >\n                关闭\n              </div>\n            </el-col>\n          </el-row>\n        </el-main>\n      </el-container>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport LivePlayer from '@liveqing/liveplayer'\nimport { GetEquipmentTree, LookVideo } from '@/api/business/safetyManagement'\n\nexport default {\n  components: {\n    LivePlayer\n  },\n  data() {\n    return {\n      q: '',\n      players: [],\n      playerIdx: 0,\n      colSpan: 12,\n      playerLength: 1,\n      loadedData: false,\n      localData: {\n        num1: {},\n        num4: {},\n        num9: {}\n      },\n      channelListDlgTitle: '',\n      protocol: '',\n      showTree: true,\n      showGroupTree: false, // lazy load group tree\n      showTip: false,\n      treeLoading: false,\n      groupTreeLoading: false,\n      queryDevTreeLoading: false,\n      queryGroupTreeLoading: false,\n      defExpandDevs: [],\n      devLevelFilter: false,\n      groupLevelFilter: false,\n      fullscreenFlag: false,\n      contextMenuTarget: null,\n      contextMenuVisible: false,\n      contextMenuNodeData: null,\n      treeProps: {\n\n      },\n      bSmartStream: false,\n      bVideoTitle: false,\n      level: 0,\n      bPlayerFullscreen: false, // any player is fullscreen\n      outHevcTipIdx: -1, // idx of player is out hevc stuck\n      activeTab: 'dev',\n      filterText: '',\n      treeData: [],\n      defaultProps: {\n        children: 'Children',\n        label: 'Name'\n      },\n      defaultExpandedKeys: []\n    }\n  },\n  computed: {\n    playerBtnGroup() {\n      var list = [{\n        num: 1,\n        name: '单屏'\n      }, {\n        num: 4,\n        name: '四分屏'\n      }, {\n        num: 9,\n        name: '九分屏'\n      }]\n      return list\n    },\n    playing() {\n      var player = this.players[this.playerIdx] || {}\n      return !!player.url\n    },\n    treeEmptyText() {\n      return this.treeLoading ? '加载中...' : '暂无数据'\n    },\n    showQueryDevTree() {\n      if (!this.q) return false\n      if (this.activeTab === 'dev' && this.devLevelFilter) {\n        this.queryDevTreeLoading = true\n        return true\n      }\n      return false\n    },\n    showQueryGroupTree() {\n      if (!this.q) return false\n      if (this.activeTab === 'group' && this.groupLevelFilter) {\n        this.queryGroupTreeLoading = true\n        return true\n      }\n      return false\n    }\n  },\n  watch: {\n    bSmartStream: function(newVal, oldVal) {\n      for (const idx in this.players) {\n        const player = this.players[idx]\n        if (!player) continue\n        const _url = player.url\n        if (!_url) continue\n        player.url = ''\n        player.bSmart = newVal\n        this.$nextTick(() => {\n          player.url = _url\n        })\n      }\n    },\n    filterText(val) {\n      this.$refs.tree.filter(val)\n    }\n  },\n  mounted() {\n    this.setPlayerLength(this.playerLength)\n    this.contextMenuTarget = document.querySelector('#tab-tree-wrapper')\n    this.getEquipmentTree()\n  },\n  beforeRouteEnter(to, from, next) {\n    next(vm => {\n      if (to.query.protocol) {\n        vm.protocol = to.query.protocol\n      }\n    })\n  },\n  beforeRouteUpdate(to, from, next) {\n    this.clearVideos()\n    if (to.query.protocol) {\n      this.protocol = to.query.protocol\n    }\n    next()\n  },\n  beforeRouteLeave(to, from, next) {\n    this.clearVideos()\n    next()\n  },\n  methods: {\n    clearVideos() {\n      this.outHevcTipIdx = -1\n      for (var idx in this.players) {\n        this.closeVideo(idx)\n      }\n    },\n    play(index, channel, next) {\n      console.log(channel)\n      var i = 0\n      var player = null\n      for (var _player of this.players) {\n        if (index === i) {\n          player = _player\n          break\n        }\n        i++\n      }\n      if (!player) {\n        this.$message({\n          type: 'error',\n          message: '当前播放窗口已被占满！'\n        })\n        return\n      }\n      if (player.bLoading) return\n      player.bLoading = true\n      if (next) {\n        this.setPlayerIdx(index + 1)\n      }\n      if (channel.Node) {\n        player.node = channel.Node\n        this.$set(player.node, 'playing', true)\n        delete channel.Node\n      }\n      LookVideo({ id: channel.ID }).then(res => {\n        if (res.IsSucceed) {\n          const videoUrl = res.Data\n          player.bSmart = this.playerLength > 1 && this.bSmartStream\n          player.url = videoUrl\n          player.code = channel.ID\n          player.protocol = 'HLS'\n          player.title = channel.ID\n          if (player.node) {\n            this.$delete(player.node, 'playing')\n            var play = player.node.play || []\n            if (videoUrl) {\n              play.push(index)\n            }\n            if (play.length) {\n              this.$set(player.node, 'play', play)\n            }\n          }\n          if (!this.loadedData) {\n            this.localData['num' + this.playerLength] = {}\n          }\n          this.localData['num' + this.playerLength]['c' + index] = JSON.stringify(channel)\n          this.setLocalData()\n        } else {\n          this.closeVideo(this.playerIdx)\n          this.$message.error(res.Message)\n        }\n      })\n    },\n    closeVideo(idx, manual = false) {\n      var player = this.players[idx]\n      if (!player) return\n      if (player.closeTimer) {\n        clearTimeout(player.closeTimer)\n        player.closeTimer = 0\n      }\n      if (this.outHevcTipIdx === idx) {\n        this.outHevcTipIdx = -1\n      }\n      if (player.node) {\n        var play = player.node.play || []\n        play = play.filter(val => val !== idx)\n        if (play.length) {\n          this.$set(player.node, 'play', play)\n        } else {\n          this.$delete(player.node, 'play')\n          this.$delete(player.node, 'playing')\n        }\n        delete player.node\n      }\n      player.mediaInfo = null\n      player.bCloseShow = false\n      player.bControls = false\n      player.bLoading = false\n      player.bSmart = false\n      player.bFullscreen = false\n      player.poster = ''\n      player.title = ''\n      player.osd = ''\n      player.url = ''\n      player.ptzType = 0\n      player.serial = ''\n      player.code = ''\n      if (manual) {\n        delete this.localData['num' + this.playerLength]['c' + idx]\n        this.setLocalData()\n      }\n    },\n    setPlayerLength(len) {\n      if (len === this.players.length) return\n      this.clearVideos()\n      this.players = []\n      this.playerLength = len\n      len === 1 ? this.colSpan = 24 : len === 4 ? this.colSpan = 12 : this.colSpan = 8\n      this.loadedData = false\n      this.playerIdx = 0\n      for (var i = 0; i < len; i++) {\n        this.players.push({\n          serial: '',\n          code: '',\n          url: '',\n          ptzType: 0,\n          protocol: '',\n          poster: '',\n          title: '',\n          osd: '',\n          bLoading: false,\n          bCloseShow: false,\n          bControls: false,\n          bSmart: false,\n          bFullscreen: false,\n          closeTimer: 0,\n          closeInterval: 0,\n          mediaInfo: null\n        })\n      }\n    },\n    setPlayerIdx(idx) {\n      var newIdx = idx % this.players.length\n      this.playerIdx = newIdx\n    },\n    onError(player, idx, e) {\n      if (e === 'MediaError' && player.mediaInfo && String(player.mediaInfo.videoCodec).startsWith('hvc')) {\n        this.outHevcTipIdx = idx\n        console.log('提示: 正在播放 H265 FLV 直出流, 确保浏览器版本较新, 并且开启硬件加速')\n      }\n    },\n    onPlay(player, idx, t) {\n      if (this.outHevcTipIdx === idx && t >= 1) {\n        this.outHevcTipIdx = -1\n      }\n    },\n    onFullscreenChange(player, idx, bFullscreen) {\n      if (player) {\n        player.bFullscreen = bFullscreen\n        this.bPlayerFullscreen = bFullscreen\n        if (bFullscreen) {\n          $(`#dev-tree-right .video-show .video:eq(${idx}) .ptz-block-fs`).draggable({\n            handle: '.ptz-center',\n            cancel: '.ptz-cell',\n            containment: `#dev-tree-right .video-show .video:eq(${idx}) .video-js`,\n            delay: 100\n          })\n        } else {\n          $(`#dev-tree-right .video-show .video:eq(${idx}) .ptz-block-fs`).draggable('destroy')\n        }\n      }\n    },\n    fullscreen() {\n      // if (this.isMobile()) {\n      //   this.$message({\n      //     type: \"error\",\n      //     message: \"请在电脑浏览器上使用该功能\"\n      //   });\n      //   return;\n      // }\n      this.$fullscreen.enter(this.$el.querySelector(`.video-show > div`), {\n        wrap: false,\n        callback: f => {\n          this.fullscreenFlag = f\n        }\n      })\n    },\n    treeNodeClick(data, node) {\n      console.log(node.isLeaf && (node.data.ParentId ?? '') != '')\n      if (node.isLeaf && (node.data.ParentId ?? '') != '') {\n        this.contextMenuNodeData = null\n        this.contextMenuNode = null\n        if (node && !node.playing) {\n          if (!node.clickCnt || node.clickCnt > 1) {\n            node.clickCnt = 1\n          } else {\n            node.clickCnt++\n          }\n          var player = this.players[this.playerIdx] || {}\n          if (player.bLoading) return\n          this.closeVideo(this.playerIdx)\n          this.$nextTick(() => {\n            this.play(this.playerIdx, {\n              ID: data.Id,\n              Name: data.Name,\n              Node: node\n            }, true)\n          })\n        }\n      }\n    },\n    async getEquipmentTree() {\n      const res = await GetEquipmentTree()\n      console.log('res', res)\n      if (res.IsSucceed) {\n        this.treeData = res.Data\n        if ((res.Data ?? []).length > 0) {\n          this.$nextTick(() => {\n            this.defaultExpandedKeys = [res.Data[0]?.Id, res.Data[0]?.Children[0]?.Id, res.Data[0]?.Children[0]?.Children[0]?.Id]\n            this.play(this.playerIdx, {\n              ID: res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id,\n              Name: res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Name,\n              Node: this.$refs.tree.getNode(res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id)\n            }, true)\n            this.$refs.tree.setCurrentKey(res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id)\n          })\n        }\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    filterNode(value, data) {\n      if (!value) return true\n      return data.Name.indexOf(value) !== -1\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.screenContent {\n  .text-center {\n    text-align: center;\n  }\n  .player-btn-group {\n    .el-button--primary {\n      color: #567;\n      background: #fff;\n      border: 1px solid #dcdfe6 !important;\n    }\n    .active {\n      background-color: #298dff;\n      border: 1px solid #298dff !important;\n      color: #fff;\n    }\n  }\n  #screen-sticky > #screen-sticky-bottom {\n    display: none;\n  }\n  #screen-sticky-wrapper.sticky > #screen-sticky > #screen-sticky-bottom {\n    display: block;\n  }\n  .customContainer {\n    height: 100%;\n    .aside {\n      display: flex;\n      flex-direction: column;\n      .filter-tree {\n        flex: 1;\n        overflow-y: auto;\n        padding: 20px 0;\n      }\n    }\n  }\n  .video-show {\n    .video {\n      border: 1px solid #fff;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA,OAAAA,UAAA;AACA,SAAAC,gBAAA,EAAAC,SAAA;AAEA;EACAC,UAAA;IACAH,UAAA,EAAAA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,CAAA;MACAC,OAAA;MACAC,SAAA;MACAC,OAAA;MACAC,YAAA;MACAC,UAAA;MACAC,SAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,mBAAA;MACAC,QAAA;MACAC,QAAA;MACAC,aAAA;MAAA;MACAC,OAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,qBAAA;MACAC,aAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,cAAA;MACAC,iBAAA;MACAC,kBAAA;MACAC,mBAAA;MACAC,SAAA,GAEA;MACAC,YAAA;MACAC,WAAA;MACAC,KAAA;MACAC,iBAAA;MAAA;MACAC,aAAA;MAAA;MACAC,SAAA;MACAC,UAAA;MACAC,QAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,mBAAA;IACA;EACA;EACAC,QAAA;IACAC,cAAA,WAAAA,eAAA;MACA,IAAAC,IAAA;QACAC,GAAA;QACAC,IAAA;MACA;QACAD,GAAA;QACAC,IAAA;MACA;QACAD,GAAA;QACAC,IAAA;MACA;MACA,OAAAF,IAAA;IACA;IACAG,OAAA,WAAAA,QAAA;MACA,IAAAC,MAAA,QAAA5C,OAAA,MAAAC,SAAA;MACA,SAAA2C,MAAA,CAAAC,GAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAAhC,WAAA;IACA;IACAiC,gBAAA,WAAAA,iBAAA;MACA,UAAAhD,CAAA;MACA,SAAAgC,SAAA,mBAAAZ,cAAA;QACA,KAAAH,mBAAA;QACA;MACA;MACA;IACA;IACAgC,kBAAA,WAAAA,mBAAA;MACA,UAAAjD,CAAA;MACA,SAAAgC,SAAA,qBAAAX,gBAAA;QACA,KAAAH,qBAAA;QACA;MACA;MACA;IACA;EACA;EACAgC,KAAA;IACAvB,YAAA,WAAAA,aAAAwB,MAAA,EAAAC,MAAA;MAAA,IAAAC,KAAA;MAAA,IAAAC,KAAA,YAAAA,MAAA,EACA;UACA,IAAAT,MAAA,GAAAQ,KAAA,CAAApD,OAAA,CAAAsD,GAAA;UACA,KAAAV,MAAA;UACA,IAAAW,IAAA,GAAAX,MAAA,CAAAC,GAAA;UACA,KAAAU,IAAA;UACAX,MAAA,CAAAC,GAAA;UACAD,MAAA,CAAAY,MAAA,GAAAN,MAAA;UACAE,KAAA,CAAAK,SAAA;YACAb,MAAA,CAAAC,GAAA,GAAAU,IAAA;UACA;QACA;QAAAG,IAAA;MAVA,SAAAJ,GAAA,SAAAtD,OAAA;QAAA0D,IAAA,GAAAL,KAAA;QAAA,IAAAK,IAAA,QAEA;MAAA;IASA;IACA1B,UAAA,WAAAA,WAAA2B,GAAA;MACA,KAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAH,GAAA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,eAAA,MAAA7D,YAAA;IACA,KAAAmB,iBAAA,GAAA2C,QAAA,CAAAC,aAAA;IACA,KAAAC,gBAAA;EACA;EACAC,gBAAA,WAAAA,iBAAAC,EAAA,EAAAC,IAAA,EAAAC,IAAA;IACAA,IAAA,WAAAC,EAAA;MACA,IAAAH,EAAA,CAAAI,KAAA,CAAA/D,QAAA;QACA8D,EAAA,CAAA9D,QAAA,GAAA2D,EAAA,CAAAI,KAAA,CAAA/D,QAAA;MACA;IACA;EACA;EACAgE,iBAAA,WAAAA,kBAAAL,EAAA,EAAAC,IAAA,EAAAC,IAAA;IACA,KAAAI,WAAA;IACA,IAAAN,EAAA,CAAAI,KAAA,CAAA/D,QAAA;MACA,KAAAA,QAAA,GAAA2D,EAAA,CAAAI,KAAA,CAAA/D,QAAA;IACA;IACA6D,IAAA;EACA;EACAK,gBAAA,WAAAA,iBAAAP,EAAA,EAAAC,IAAA,EAAAC,IAAA;IACA,KAAAI,WAAA;IACAJ,IAAA;EACA;EACAM,OAAA;IACAF,WAAA,WAAAA,YAAA;MACA,KAAA7C,aAAA;MACA,SAAAwB,GAAA,SAAAtD,OAAA;QACA,KAAA8E,UAAA,CAAAxB,GAAA;MACA;IACA;IACAyB,IAAA,WAAAA,KAAAC,KAAA,EAAAC,OAAA,EAAAV,IAAA;MAAA,IAAAW,MAAA;MACAC,OAAA,CAAAC,GAAA,CAAAH,OAAA;MACA,IAAAI,CAAA;MACA,IAAAzC,MAAA;MAAA,IAAA0C,SAAA,GAAAC,0BAAA,CACA,KAAAvF,OAAA;QAAAwF,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAC,OAAA,GAAAJ,KAAA,CAAAK,KAAA;UACA,IAAAb,KAAA,KAAAK,CAAA;YACAzC,MAAA,GAAAgD,OAAA;YACA;UACA;UACAP,CAAA;QACA;MAAA,SAAAS,GAAA;QAAAR,SAAA,CAAAS,CAAA,CAAAD,GAAA;MAAA;QAAAR,SAAA,CAAAU,CAAA;MAAA;MACA,KAAApD,MAAA;QACA,KAAAqD,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;QACA;MACA;MACA,IAAAvD,MAAA,CAAAwD,QAAA;MACAxD,MAAA,CAAAwD,QAAA;MACA,IAAA7B,IAAA;QACA,KAAA8B,YAAA,CAAArB,KAAA;MACA;MACA,IAAAC,OAAA,CAAAqB,IAAA;QACA1D,MAAA,CAAA2D,IAAA,GAAAtB,OAAA,CAAAqB,IAAA;QACA,KAAAE,IAAA,CAAA5D,MAAA,CAAA2D,IAAA;QACA,OAAAtB,OAAA,CAAAqB,IAAA;MACA;MACA1G,SAAA;QAAA6G,EAAA,EAAAxB,OAAA,CAAAyB;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAC,QAAA,GAAAF,GAAA,CAAAG,IAAA;UACAnE,MAAA,CAAAY,MAAA,GAAA0B,MAAA,CAAA/E,YAAA,QAAA+E,MAAA,CAAAxD,YAAA;UACAkB,MAAA,CAAAC,GAAA,GAAAiE,QAAA;UACAlE,MAAA,CAAAoE,IAAA,GAAA/B,OAAA,CAAAyB,EAAA;UACA9D,MAAA,CAAAlC,QAAA;UACAkC,MAAA,CAAAqE,KAAA,GAAAhC,OAAA,CAAAyB,EAAA;UACA,IAAA9D,MAAA,CAAA2D,IAAA;YACArB,MAAA,CAAAgC,OAAA,CAAAtE,MAAA,CAAA2D,IAAA;YACA,IAAAxB,IAAA,GAAAnC,MAAA,CAAA2D,IAAA,CAAAxB,IAAA;YACA,IAAA+B,QAAA;cACA/B,IAAA,CAAAoC,IAAA,CAAAnC,KAAA;YACA;YACA,IAAAD,IAAA,CAAAqC,MAAA;cACAlC,MAAA,CAAAsB,IAAA,CAAA5D,MAAA,CAAA2D,IAAA,UAAAxB,IAAA;YACA;UACA;UACA,KAAAG,MAAA,CAAA9E,UAAA;YACA8E,MAAA,CAAA7E,SAAA,SAAA6E,MAAA,CAAA/E,YAAA;UACA;UACA+E,MAAA,CAAA7E,SAAA,SAAA6E,MAAA,CAAA/E,YAAA,QAAA6E,KAAA,IAAAqC,IAAA,CAAAC,SAAA,CAAArC,OAAA;UACAC,MAAA,CAAAqC,YAAA;QACA;UACArC,MAAA,CAAAJ,UAAA,CAAAI,MAAA,CAAAjF,SAAA;UACAiF,MAAA,CAAAe,QAAA,CAAAuB,KAAA,CAAAZ,GAAA,CAAAa,OAAA;QACA;MACA;IACA;IACA3C,UAAA,WAAAA,WAAAxB,GAAA;MAAA,IAAAoE,MAAA,GAAAC,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAA/E,MAAA,QAAA5C,OAAA,CAAAsD,GAAA;MACA,KAAAV,MAAA;MACA,IAAAA,MAAA,CAAAiF,UAAA;QACAC,YAAA,CAAAlF,MAAA,CAAAiF,UAAA;QACAjF,MAAA,CAAAiF,UAAA;MACA;MACA,SAAA/F,aAAA,KAAAwB,GAAA;QACA,KAAAxB,aAAA;MACA;MACA,IAAAc,MAAA,CAAA2D,IAAA;QACA,IAAAxB,IAAA,GAAAnC,MAAA,CAAA2D,IAAA,CAAAxB,IAAA;QACAA,IAAA,GAAAA,IAAA,CAAAjB,MAAA,WAAAH,GAAA;UAAA,OAAAA,GAAA,KAAAL,GAAA;QAAA;QACA,IAAAyB,IAAA,CAAAqC,MAAA;UACA,KAAAZ,IAAA,CAAA5D,MAAA,CAAA2D,IAAA,UAAAxB,IAAA;QACA;UACA,KAAAmC,OAAA,CAAAtE,MAAA,CAAA2D,IAAA;UACA,KAAAW,OAAA,CAAAtE,MAAA,CAAA2D,IAAA;QACA;QACA,OAAA3D,MAAA,CAAA2D,IAAA;MACA;MACA3D,MAAA,CAAAmF,SAAA;MACAnF,MAAA,CAAAoF,UAAA;MACApF,MAAA,CAAAqF,SAAA;MACArF,MAAA,CAAAwD,QAAA;MACAxD,MAAA,CAAAY,MAAA;MACAZ,MAAA,CAAAsF,WAAA;MACAtF,MAAA,CAAAuF,MAAA;MACAvF,MAAA,CAAAqE,KAAA;MACArE,MAAA,CAAAwF,GAAA;MACAxF,MAAA,CAAAC,GAAA;MACAD,MAAA,CAAAyF,OAAA;MACAzF,MAAA,CAAA0F,MAAA;MACA1F,MAAA,CAAAoE,IAAA;MACA,IAAAU,MAAA;QACA,YAAArH,SAAA,cAAAF,YAAA,QAAAmD,GAAA;QACA,KAAAiE,YAAA;MACA;IACA;IACAvD,eAAA,WAAAA,gBAAAuE,GAAA;MACA,IAAAA,GAAA,UAAAvI,OAAA,CAAAoH,MAAA;MACA,KAAAzC,WAAA;MACA,KAAA3E,OAAA;MACA,KAAAG,YAAA,GAAAoI,GAAA;MACAA,GAAA,cAAArI,OAAA,QAAAqI,GAAA,cAAArI,OAAA,aAAAA,OAAA;MACA,KAAAE,UAAA;MACA,KAAAH,SAAA;MACA,SAAAoF,CAAA,MAAAA,CAAA,GAAAkD,GAAA,EAAAlD,CAAA;QACA,KAAArF,OAAA,CAAAmH,IAAA;UACAmB,MAAA;UACAtB,IAAA;UACAnE,GAAA;UACAwF,OAAA;UACA3H,QAAA;UACAyH,MAAA;UACAlB,KAAA;UACAmB,GAAA;UACAhC,QAAA;UACA4B,UAAA;UACAC,SAAA;UACAzE,MAAA;UACA0E,WAAA;UACAL,UAAA;UACAW,aAAA;UACAT,SAAA;QACA;MACA;IACA;IACA1B,YAAA,WAAAA,aAAA/C,GAAA;MACA,IAAAmF,MAAA,GAAAnF,GAAA,QAAAtD,OAAA,CAAAoH,MAAA;MACA,KAAAnH,SAAA,GAAAwI,MAAA;IACA;IACAC,OAAA,WAAAA,QAAA9F,MAAA,EAAAU,GAAA,EAAAyC,CAAA;MACA,IAAAA,CAAA,qBAAAnD,MAAA,CAAAmF,SAAA,IAAAY,MAAA,CAAA/F,MAAA,CAAAmF,SAAA,CAAAa,UAAA,EAAAC,UAAA;QACA,KAAA/G,aAAA,GAAAwB,GAAA;QACA6B,OAAA,CAAAC,GAAA;MACA;IACA;IACA0D,MAAA,WAAAA,OAAAlG,MAAA,EAAAU,GAAA,EAAAyF,CAAA;MACA,SAAAjH,aAAA,KAAAwB,GAAA,IAAAyF,CAAA;QACA,KAAAjH,aAAA;MACA;IACA;IACAkH,kBAAA,WAAAA,mBAAApG,MAAA,EAAAU,GAAA,EAAA4E,WAAA;MACA,IAAAtF,MAAA;QACAA,MAAA,CAAAsF,WAAA,GAAAA,WAAA;QACA,KAAArG,iBAAA,GAAAqG,WAAA;QACA,IAAAA,WAAA;UACAe,CAAA,0CAAAC,MAAA,CAAA5F,GAAA,sBAAA6F,SAAA;YACAC,MAAA;YACAC,MAAA;YACAC,WAAA,2CAAAJ,MAAA,CAAA5F,GAAA;YACAiG,KAAA;UACA;QACA;UACAN,CAAA,0CAAAC,MAAA,CAAA5F,GAAA,sBAAA6F,SAAA;QACA;MACA;IACA;IACAK,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAAC,WAAA,CAAAC,KAAA,MAAAC,GAAA,CAAA1F,aAAA;QACA2F,IAAA;QACAC,QAAA,WAAAA,SAAA9D,CAAA;UACAyD,MAAA,CAAApI,cAAA,GAAA2E,CAAA;QACA;MACA;IACA;IACA+D,aAAA,WAAAA,cAAAjK,IAAA,EAAAyG,IAAA;MAAA,IAAAyD,mBAAA;QAAAC,oBAAA;QAAAC,MAAA;MACA/E,OAAA,CAAAC,GAAA,CAAAmB,IAAA,CAAA4D,MAAA,MAAAH,mBAAA,GAAAzD,IAAA,CAAAzG,IAAA,CAAAsK,QAAA,cAAAJ,mBAAA,cAAAA,mBAAA;MACA,IAAAzD,IAAA,CAAA4D,MAAA,MAAAF,oBAAA,GAAA1D,IAAA,CAAAzG,IAAA,CAAAsK,QAAA,cAAAH,oBAAA,cAAAA,oBAAA;QACA,KAAAzI,mBAAA;QACA,KAAA6I,eAAA;QACA,IAAA9D,IAAA,KAAAA,IAAA,CAAA5D,OAAA;UACA,KAAA4D,IAAA,CAAA+D,QAAA,IAAA/D,IAAA,CAAA+D,QAAA;YACA/D,IAAA,CAAA+D,QAAA;UACA;YACA/D,IAAA,CAAA+D,QAAA;UACA;UACA,IAAA1H,MAAA,QAAA5C,OAAA,MAAAC,SAAA;UACA,IAAA2C,MAAA,CAAAwD,QAAA;UACA,KAAAtB,UAAA,MAAA7E,SAAA;UACA,KAAAwD,SAAA;YACAyG,MAAA,CAAAnF,IAAA,CAAAmF,MAAA,CAAAjK,SAAA;cACAyG,EAAA,EAAA5G,IAAA,CAAAyK,EAAA;cACAC,IAAA,EAAA1K,IAAA,CAAA0K,IAAA;cACAlE,IAAA,EAAAC;YACA;UACA;QACA;MACA;IACA;IACApC,gBAAA,WAAAA,iBAAA;MAAA,IAAAsG,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAjE,GAAA,EAAAkE,SAAA;QAAA,OAAAH,mBAAA,GAAAd,IAAA,UAAAkB,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAzG,IAAA;YAAA;cAAAyG,QAAA,CAAAzG,IAAA;cAAA,OACA5E,gBAAA;YAAA;cAAAiH,GAAA,GAAAoE,QAAA,CAAAE,IAAA;cACA/F,OAAA,CAAAC,GAAA,QAAAwB,GAAA;cACA,IAAAA,GAAA,CAAAC,SAAA;gBACA4D,MAAA,CAAAxI,QAAA,GAAA2E,GAAA,CAAAG,IAAA;gBACA,MAAA+D,SAAA,GAAAlE,GAAA,CAAAG,IAAA,cAAA+D,SAAA,cAAAA,SAAA,OAAA1D,MAAA;kBACAqD,MAAA,CAAAhH,SAAA;oBAAA,IAAA0H,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;oBACAhB,MAAA,CAAApI,mBAAA,KAAA8I,UAAA,GAAAvE,GAAA,CAAAG,IAAA,iBAAAoE,UAAA,uBAAAA,UAAA,CAAAZ,EAAA,GAAAa,WAAA,GAAAxE,GAAA,CAAAG,IAAA,iBAAAqE,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAM,QAAA,iBAAAN,WAAA,uBAAAA,WAAA,CAAAb,EAAA,GAAAc,WAAA,GAAAzE,GAAA,CAAAG,IAAA,iBAAAsE,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAK,QAAA,iBAAAL,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAK,QAAA,iBAAAL,WAAA,uBAAAA,WAAA,CAAAd,EAAA;oBACAE,MAAA,CAAA1F,IAAA,CAAA0F,MAAA,CAAAxK,SAAA;sBACAyG,EAAA,GAAA4E,WAAA,GAAA1E,GAAA,CAAAG,IAAA,iBAAAuE,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAI,QAAA,iBAAAJ,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAI,QAAA,iBAAAJ,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAI,QAAA,iBAAAJ,WAAA,uBAAAA,WAAA,CAAAf,EAAA;sBACAC,IAAA,GAAAe,WAAA,GAAA3E,GAAA,CAAAG,IAAA,iBAAAwE,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAG,QAAA,iBAAAH,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAG,QAAA,iBAAAH,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAG,QAAA,iBAAAH,WAAA,uBAAAA,WAAA,CAAAf,IAAA;sBACAlE,IAAA,EAAAmE,MAAA,CAAA7G,KAAA,CAAAC,IAAA,CAAA8H,OAAA,EAAAH,WAAA,GAAA5E,GAAA,CAAAG,IAAA,iBAAAyE,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAE,QAAA,iBAAAF,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAE,QAAA,iBAAAF,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAE,QAAA,iBAAAF,WAAA,uBAAAA,WAAA,CAAAjB,EAAA;oBACA;oBACAE,MAAA,CAAA7G,KAAA,CAAAC,IAAA,CAAA+H,aAAA,EAAAH,WAAA,GAAA7E,GAAA,CAAAG,IAAA,iBAAA0E,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAC,QAAA,iBAAAD,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAC,QAAA,iBAAAD,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAC,QAAA,iBAAAD,WAAA,uBAAAA,WAAA,CAAAlB,EAAA;kBACA;gBACA;cACA;gBACAE,MAAA,CAAAxE,QAAA,CAAAuB,KAAA,CAAAZ,GAAA,CAAAa,OAAA;cACA;YAAA;YAAA;cAAA,OAAAuD,QAAA,CAAAa,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IACA;IACAiB,UAAA,WAAAA,WAAAjG,KAAA,EAAA/F,IAAA;MACA,KAAA+F,KAAA;MACA,OAAA/F,IAAA,CAAA0K,IAAA,CAAAuB,OAAA,CAAAlG,KAAA;IACA;EACA;AACA", "ignoreList": []}]}