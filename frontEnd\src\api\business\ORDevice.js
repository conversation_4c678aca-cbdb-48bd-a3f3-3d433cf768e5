import request from "@/utils/request";
// 获取设备列表
export function GetProjectDeviceList(data) {
  return request({
    url: "/DF/ORDevice/GetProjectDeviceList",
    method: "post",
    data,
  });
}
// 删除设备列表
export function DeleteProjectDevice(data) {
  return request({
    url: "/DF/ORDevice/DeleteProjectDevice",
    method: "post",
    data,
  });
}
// 编辑设备列表
export function SubProjectDevice(data) {
  return request({
    url: "/DF/ORDevice/SubProjectDevice",
    method: "post",
    data,
  });
}
// 获取设备阀值配置
export function GetAlarmConfigInfo(data) {
  return request({
    url: "/DF/ORDevice/GetAlarmConfigInfo",
    method: "post",
    data,
  });
}
// 编辑设备阀值配置
export function SyncAlarmConfig(data) {
  return request({
    url: "/DF/ORDevice/SyncAlarmConfig",
    method: "post",
    data,
  });
}

