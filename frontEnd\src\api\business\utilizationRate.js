import request from '@/utils/request'
// 获取设备列表(实时电流)
export function GetProjectDeviceList(data) {
  return request({
    url: '/DF/ORPullDownList/GetProjectDeviceList',
    method: 'get',
    params: data
  })
}
export function ORRealGetProjectDeviceList(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/GetProjectDeviceList',
    method: 'post',
    data
  })
}
//  获取运行设备属性
export function GetDeviceCurrentData(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/GetDeviceCurrentData',
    method: 'post',
    data
  })
}
// 获取指定时间段设备运行数据
export function GetDeviceRangeData(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/GetDeviceRangeData',
    method: 'post',
    data
  })
}
// 设备故障列表
export function GetDeviceCurrentErrorData(data) {
  return request({
    url: '/DF/ORError/GetDeviceCurrentErrorData',
    method: 'post',
    data
  })
}
// 获取负载电流
export function GetBeginI(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/GetBeginI',
    method: 'post',
    data
  })
}
// 获取指定时间段峰谷用电图标数据
export function GetPeakValleyElectricity(data) {
  return request({
    url: '/DF/ORElectricQuantity/GetPeakValleyElectricity',
    method: 'post',
    data
  })
}
// 获取指定时间段的峰谷电列表数据
export function GetElectricQuantityList(data) {
  return request({
    url: '/DF/ORElectricQuantity/GetElectricQuantityList',
    method: 'post',
    data
  })
}
// 导出指定时间段的峰谷电列表数据
export function ExportRequestElectricQuantity(data) {
  return request({
    url: '/DF/ORElectricQuantity/ExportRequestElectricQuantity',
    method: 'post',
    data
  })
}
// 获取体系列表
export function GetPriceList(data) {
  return request({
    url: '/DF/ORPullDownList/GetPriceList',
    method: 'post',
    data
  })
}
// 获取体系分类列表
export function GetSettingList(data) {
  return request({
    url: '/DF/ORPullDownList/GetSettingList',
    method: 'post',
    data
  })
}
// 获取稼动率分析数据汇总
export function GetRangeOEECollect(data) {
  return request({
    url: '/DF/ORAnalyse/GetRangeOEECollect',
    method: 'post',
    data
  })
}
// 获取设备属性
export function GetDevicePropertyList(data) {
  return request({
    url: '/DF/ORPullDownList/GetDevicePropertyList',
    method: 'post',
    data
  })
}
// 导出设备属性
export function ExportCollectionDataOnRange(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/ExportCollectionDataOnRange',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
// 导出设备
export function ExportRangeOEEList(data) {
  return request({
    url: '/DF/ORAnalyse/ExportRangeOEEList',
    method: 'post',
    data
  })
}
// 获取每天数据
export function GetRangeOEE(data) {
  return request({
    url: '/DF/ORAnalyse/GetRangeOEE',
    method: 'post',
    data
  })
}
// 获取表格数据
export function GetRangeOEEList(data) {
  return request({
    url: '/DF/ORAnalyse/GetRangeOEEList',
    method: 'post',
    data
  })
}
// 获取表格数据
export function UpdateErrorStatus(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/UpdateErrorStatus',
    method: 'post',
    data
  })
}
// 导出异常台账
export function ExportRangeDeviceError(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/ExportRangeDeviceError',
    method: 'post',
    data
  })
}
// 异常台账列表
export function GetRangeDeviceError(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/GetRangeDeviceError',
    method: 'post',
    data
  })
}
// 删除电价体系
export function DeletePrice(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/DeletePrice',
    method: 'post',
    data
  })
}
// 删除电价体系设置
export function DeleteSetting(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/DeleteSetting',
    method: 'post',
    data
  })
}
// 删除电价体系设置
export function SavePrice(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/SavePrice',
    method: 'post',
    data
  })
}
// 保存/编辑 类别
export function SaveSetting(data) {
  return request({
    url: '/DF/ORRealTimeCurrent/SaveSetting',
    method: 'post',
    data
  })
}
// 新增/编辑 设备
export function SubProjectDevice(data) {
  return request({
    url: '/DF/ORDevice/SubProjectDevice',
    method: 'post',
    data
  })
}
// 删除 设备
export function DeleteProjectDevice(data) {
  return request({
    url: '/DF/ORDevice/DeleteProjectDevice',
    method: 'post',
    data
  })
}
// 设备采集点列表
export function GetAcquisitionPoint(data) {
  return request({
    url: '/DF/ORDevice/GetAcquisitionPoint',
    method: 'post',
    data
  })
}
// 报存设备采集点列表
export function SubAcquisitionPoint(data) {
  return request({
    url: '/DF/ORDevice/SubAcquisitionPoint',
    method: 'post',
    data
  })
}
// 电价执行体系列表
export function GetProjectSystemList(data) {
  return request({
    url: '/DF/ORSystem/GetProjectSystemList',
    method: 'post',
    data
  })
}
// 电价执行体系新增编辑
export function SubProjectSystem(data) {
  return request({
    url: '/DF/ORSystem/SubProjectSystem',
    method: 'post',
    data
  })
}
// 电价执行体系删除
export function DeleteProjectSystem(data) {
  return request({
    url: '/DF/ORSystem/DeleteProjectSystem',
    method: 'post',
    data
  })
}
// 电价执行体系详情
export function GetProjectSystemInfo(data) {
  return request({
    url: '/DF/ORSystem/GetProjectSystemInfo',
    method: 'post',
    data
  })
}
// 电价执行体系状态
export function UpdateStatus(data) {
  return request({
    url: '/DF/ORSystem/UpdateStatus',
    method: 'post',
    data
  })
}
// 电价执行体系配置列表
export function GetSystemConfigList(data) {
  return request({
    url: '/DF/ORSystem/GetSystemConfigList',
    method: 'post',
    data
  })
}
// 新增编辑电价执行体系配置
export function SubSystemConfig(data) {
  return request({
    url: '/DF/ORSystem/SubSystemConfig',
    method: 'post',
    data
  })
}
// 电价执行体系配置详情
export function GetSystemConfigInfo(data) {
  return request({
    url: '/DF/ORSystem/GetSystemConfigInfo',
    method: 'post',
    data
  })
}
// 删除电价执行体系配置
export function DeleteSystemConfig(data) {
  return request({
    url: '/DF/ORSystem/DeleteSystemConfig',
    method: 'post',
    data
  })
}
// 异常台账列表
export function GetRequestORErrors(data) {
  return request({
    url: '/DF/ORError/GetRequestORErrors',
    method: 'post',
    data
  })
}
// 异常台账复位
export function ORErrorUpdateStatus(data) {
  return request({
    url: '/DF/ORError/UpdateStatus',
    method: 'post',
    data
  })
}
// 导出异常台账
export function ExportRequestORErrors(data) {
  return request({
    url: '/DF/ORError/ExportRequestORErrors',
    method: 'post',
    data
  })
}

// 黑龙江-异常台账
export function GetAlarmDatasAsync(data) {
  return request({
    url: '/DF/ORAlarm/GetAlarmDatasAsync',
    method: 'post',
    data
  })
}


