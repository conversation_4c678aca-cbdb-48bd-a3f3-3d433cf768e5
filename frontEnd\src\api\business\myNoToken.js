import request from '@/utils/requestNoToken'

// 环境监测分析
// 获取监测告警类型统计
export function GetWarningTypeAnalyses(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetWarningTypeAnalyses',
    data
  })
}

// 获取车间告警趋势统计
export function GetSceneWarningTrend(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetSceneWarningTrend',
    data
  })
}
// 获取告警趋势统计
export function GetWarningTrend(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetWarningTrend',
    data
  })
}
// 获取最新告警信息
export function GetWarningAnalysesList(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetWarningList',
    data
  })
}
// 获取查看更多跳转模块地址
export function GetJumpUrl(data) {
  return request({
    method: 'get',
    url: '/DF/EnvironmentAnalyses/GetJumpUrl',
    params: data,
  })
}
// 获取车间环境监测分析平均值
export function GetSceneAnalyses(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetSceneAnalyses',
    data
  })
}
// 获取车间环境监测类型
export function GetSceneTypes(data) {
  return request({
    method: 'get',
    url: '/DF/EnvironmentAnalyses/GetSceneTypes',
    params: data,
  })
}
// 获取车间指定监测类型近24小时趋势图
export function GetSceneTrendAnalyses(data) {
  return request({
    method: 'post',
    url: '/DF/EnvironmentAnalyses/GetSceneTrendAnalyses',
    data
  })
}

// 获取天气信息
export function GetWeatherInfo(data) {
  return request({
    method: 'get',
    url: '/DF/EnvironmentAnalyses/GetWeatherInfo',
    params: data,
  })
}
