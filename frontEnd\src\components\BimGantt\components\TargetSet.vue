<template>
  <div style="margin-top:-16px; padding:0 10px;">
    <el-form ref="form" :model="form" label-width="120px">
      <el-form-item label="当前计划">
        <h2 style="font-size:1.8em; color:#222;">{{ plan.Name }}</h2>
      </el-form-item>
      <el-form-item label="选定目标计划">
        <el-select
          v-if="editmode"
          v-model="form.target"
          placeholder="请选择活动区域"
        >
          <el-option label="未设置" :value="''" />
          <el-option v-if="plan.Status === '2'" label="当前进度计划副本" :value="0" />
          <template v-for="item in sortedList">
            <el-option
              v-if="item.Status === '2' && item.Id != plan.Id"
              :key="item.Id"
              :label="item.Id === plan.Id ? '当前进度计划副本' : item.Name"
              :value="item.Id"
            />
          </template>
        </el-select>
        <span v-else>{{ `-` }}</span>
      </el-form-item>
      <el-form-item label="说明">
        <ol class="my-ol">
          <li>
            设置目标计划后，目标计划以 <span class="square" />
            出现在横道图中，前锋线也会根据计划与目标对比显示折线；
          </li>
          <li>
            选择“当前进度计划副本”，确认后将在进度计划列表创建一个当前进度计划的副本，并标记为目标计划。
          </li>
        </ol>
      </el-form-item>
      <el-form-item align="right">
        <el-button @click="$emit('dialogCancel')">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { GetPlanList } from '@/api/plan/index'
export default {
  name: 'TargetSet',
  props: {
    plan: {
      type: Object,
      default: () => ({})
    },
    editMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: [],
      form: {
        target: ''
      },
      editmode: false
    }
  },
  computed: {
    sortedList() {
      const narr = this.list.concat([])
      narr.sort((a, b) => {
        return a.Id === this.plan.Id ? -1 : 1
      })
      return narr
    }
  },
  created() {
    this.editmode = this.editMode
    this.form.target = this.plan.Target_Plan_Id || ''
    GetPlanList({
      Page: 1,
      PageSize: 200
    }).then(res => {
      if (res.IsSucceed) {
        this.list = res.Data
      }
    })
  },
  methods: {
    submit() {
      if (!this.editmode) return this.$emit('dialogCancel')
      this.$emit('dialogFormSubmitSuccess', {
        type: 'setTargetPlan',
        data: this.form.target
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.my-ol {
  list-style: decimal;
  padding-left: 16px;
}
.square {
  display: inline-block;
  padding: 4px;
  background: rgba(255, 194, 72, 0.6);
  border: 1px solid rgba(255, 194, 72, 0.8);
}
</style>
