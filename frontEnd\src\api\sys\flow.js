import request from '@/utils/request'
import qs from 'qs'

// 得到流程模板 (Auth)
export function FlowSchemesGet(data) {
  return request({
    method: 'get',
    url: '/Platform/FlowSchemes/Get',
    params: data
  })
}

// 添加流程模板 (Auth)
export function FlowSchemesAdd(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowSchemes/Add',
    data
  })
}

// 更新流程模板 (Auth)
export function FlowSchemesUpdate(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowSchemes/Update',
    data
  })
}

// 删除流程模板
export function FlowSchemesDelete(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowSchemes/Delete',
    data
  })
}
// 撤销流程
export function FlowInstancesCancel(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowInstances/CancelFlow',
    data
  })
}
// 流程模板列表
export function FlowSchemesLoad(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowSchemes/Load',
    data
  })
}

// 判断是否包含流程模板
export function CheckExistFlow(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowSchemes/CheckExistFlow',
    data
  })
}

// 流程实例列表 (Auth)
export function FlowInstancesLoad(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowInstances/Load',
    data
  })
}

// 保存表单业务数据 (Auth)
export function SaveBusinessData(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowInstances/SaveBusinessData',
    data
  })
}

// 删除流程实例 (Auth
export function FlowInstancesDelete(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowInstances/Delete',
    data
  })
}

// 已经处理过的流程实例 (Auth)
export function FlowInstancesGet(data) {
  return request({
    method: 'get',
    url: '/Platform/FlowInstances/Get',
    params: data
  })
}

// 流程节点审批 (Auth)
export function Verification(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowInstances/Verification',
    data
  })
}

// 获取一个流程实例的操作历史记录 (Auth)
export function QueryHistories(data) {
  return request({
    method: 'get',
    url: '/Platform/FlowInstances/QueryHistories',
    params: data
  })
}

// 请假分页数据 (Auth)
export function GetLeavePageList(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowInstances/GetLeavePageList',
    data: qs.stringify(data)
  })
}

// 检查人员对业务数据数据的审批权限 (Auth)
export function CheckBusinessVerification(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowInstances/CheckBusinessVerification',
    data: qs.stringify(data)
  })
}

//  发起常规合同流程
export function AddConventionalContractProcess(data) {
  return request({
    method: 'post',
    url: '/EPC/ConventionalContract/AddProcess',
    data
  })
}
//  发起代签合同流程
export function AddOtherSignedContractProcess(data) {
  return request({
    method: 'post',
    url: '/EPC/OtherSignedContract/AddProcess',
    data
  })
}
//  发起合同价格流程
export function AddContractPriceProcess(data) {
  return request({
    method: 'post',
    url: '/EPC/ContractPrice/AddProcess',
    data
  })
}
//  发起特殊采购流程
export function AddSpecialPurchaseProcess(data) {
  return request({
    method: 'post',
    url: '/EPC/SpecialPurchase/AddProcess',
    data
  })
}
//  发起用印申请流程
export function AddSealProcess(data) {
  return request({
    method: 'post',
    url: '/EPC/Seal/AddProcess',
    data
  })
}
//  发起用技术方案审核流程
export function AddTechSolutionProcess(data) {
  return request({
    method: 'post',
    url: '/EPC/TechnicalScheme/AddProcess',
    data
  })
}
// 获取表单模板对应的流程设计模板节点
export function GetFlowSchemeNodeByFromId(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowInstances/GetFlowSchemeNodeByFromId',
    data
  })
}

//  根据webformid获取模板信息
export function GetFlowSchemeByFromId(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowInstances/GetFlowSchemeByFromId',
    data
  })
}

// 获取常规合同记录
export function GetConventionalContractEntity(data) {
  return request({
    method: 'post',
    url: '/EPC/ConventionalContract​/GetEntity',
    data
  })
}
// 获取代签合同记录
export function GetOtherSignedContractEntity(data) {
  return request({
    method: 'post',
    url: '/EPC/OtherSignedContract/GetEntity',
    data
  })
}
// 获取合同价格记录
export function GetContractPriceEntity(data) {
  return request({
    method: 'post',
    url: '/EPC/ContractPrice/GetEntity',
    data
  })
}
// 获取特殊采购记录
export function GetSpecialPurchaseEntity(data) {
  return request({
    method: 'post',
    url: '/EPC/SpecialPurchase/GetEntity',
    data
  })
}
// 获取用印记录
export function GetSealEntity(data) {
  return request({
    method: 'post',
    url: '/EPC/Seal/GetEntity',
    data
  })
}

export function SaveDesignFlow(data) {
  return request({
    method: 'post',
    url: '/Platform/Sys_File/SaveDesignFlow',
    data
  })
}

export function GetOneEntitiesProject(data) {
  return request({
    method: 'post',
    url: '/Platform/Sys_FileType/GetOneEntitiesProject',
    data
  })
}

export function GetListEntitiesProject(data) {
  return request({
    method: 'post',
    url: '/Platform/Sys_FileType/GetListEntitiesProject',
    data
  })
}

export function GetFileInfo(data) {
  return request({
    method: 'post',
    url: '/Platform/Sys_FileType/GetEntity',
    data
  })
}

export function GetSchemeObjectIds(data) {
  return request({
    method: 'post',
    url: '/Platform/FlowSchemes/GetSchemeObjectIds',
    data
  })
}
