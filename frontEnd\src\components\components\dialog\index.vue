<template>
  <el-dialog
    v-dialogDrag
    :append-to-body="appendToBody"
    :title="dialogTitle"
    :diy-name="diyName"
    :visible.sync="dialogVisible"
    :width="dialogWidth"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :top="top"
    class="bim-dialog-default"
    :modal="modal"
    :modal-append-to-body="modalAppendToBody"
  >
    <div
      class="bim-dialogDiv-default"
      @drop.prevent="onDrop"
      @dragover.prevent="onDragover"
    >
      <slot>
        <p>弹框自定义的内容</p>
      </slot>
    </div>
    <slot v-if="!hidebtn" slot="footer" name="btngroup">
      <span class="dialog-footer">
        <el-button @click="Cancel" v-if="!hideCancel">取 消</el-button>
        <!-- <template v-if="diyName">
          <el-button @click="operateDiyButton(diyName)">{{ diyName }}</el-button>
        </template> -->
        <slot name="custombtn" />
        <el-button v-if="!hideComfirm" type="primary" :loading="loading" @click="Submit">{{ confirmBtnText }}</el-button>
      </span>
    </slot>
  </el-dialog>
</template>

<script>
import '@/utils/directives.js'
export default {
  props: {
    dialogTitle: { type: String, default: '' },
    visible: { type: Boolean, default: false },
    dialogWidth: { type: String, default: '960px' },
    appendToBody: { type: Boolean, default: false },
    hidebtn: { type: Boolean, default: false },
    top: { type: String, default: '15vh' },
    confirmBtnText: { type: String, default: '确 定' },
    diyName: { type: String, default: '' },
    modal: { type: Boolean, default: true },
    loading: { type: Boolean, default: false },
    modalAppendToBody:{ type: Boolean, default: true },
    hideCancel:{type: Boolean, default: false},
    hideComfirm:{type: Boolean, default: false}
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        // 当visible改变的时候，触发父组件的 updateVisible方法，在该方法中更改传入子组件的 centerDialogVisible的值
        this.$emit('updateVisible', val)
      }
    }
  },
  methods: {
    onDrop(e) {
      // console.log('11111', e)
      e.preventDefault()
    },
    onDragover(e) {
      // console.log('2222222', e)
      e.preventDefault()
    },
    Cancel() {
      this.$emit('cancelbtn')
    },
    Submit() {
      this.$emit('submitbtn')
    },
    handleClose() {
      this.$emit('handleClose')
    },
    operateDiyButton(name) {
      this.$emit('getdiybutton', name)
    }
  }
}
</script>
<style scoped lang="scss">
.bim-dialogDiv-default {
  // max-height: 590px;
  overflow-x: auto;
}
.bim-dialog-default {
  ::v-deep{
    .el-dialog {
      border-radius: 5px !important;
      .el-dialog__header {
        background-color: #298dff !important;
        border-radius: 5px 5px 0 0 !important;
        height: 33px;
        padding: 10px 15px;
        display: flex;
        align-items: center;
        box-sizing: content-box;
        .el-dialog__title {
          color: white;
          font-family: "微软雅黑";
          font-weight: bold;
          line-height: 24px;
        }
        .el-dialog__headerbtn {
          // top: 15px;
          // height:100%;
          display: flex;
          align-items: center;
          .el-dialog__close {
            color: white;
            font-size: 24px;
          }
        }
      }
      .el-dialog__body{
        padding:0;
        box-sizing: content-box;
      }
      .el-dialog__footer{
        padding-top:0;
      }
    }
  }
  
}
</style>
