
//  模块配置API
import request from '@/utils/request'
// import qs from 'qs'

// 新增模块 (Auth)
export function AddModule(data) {
  return request({
    url: '/Platform/Menu/AddModule',
    method: 'post',
    data
  })
}

// 获取模块列表 (Auth)
export function GetModuleList(data) {
  return request({
    url: '/Platform/Menu/GetModuleList',
    method: 'post',
    data
  })
}

// 删除模块 (Auth)
export function DelModule(data) {
  return request({
    url: '/Platform/Menu/DelModule',
    method: 'post',
    data
  })
}

// 更新模块 (Auth)
export function UpdateModule(data) {
  return request({
    url: '/Platform/Menu/UpdateModule',
    method: 'post',
    data
  })
}

// 获取菜单 (Auth)
export function GetMenuList(data) {
  return request({
    url: '/Platform/Menu/GetMenuList',
    method: 'post',
    data
  })
}

// 新增菜单 (Auth)
export function AddMenu(data) {
  return request({
    url: '/Platform/Menu/AddMenu',
    method: 'post',
    data
  })
}

// 更新菜单 (Auth)
export function UpdateMenu(data) {
  return request({
    url: '/Platform/Menu/UpdateMenu',
    method: 'post',
    data
  })
}

// 删除菜单 (Auth)
export function DelMenu(data) {
  return request({
    url: '/Platform/Menu/DelMenu',
    method: 'post',
    data
  })
}

// 获取菜单详情 (Auth)
export function GetMenuEntity(data) {
  return request({
    method: 'get',
    url: '/Platform/Menu/GetMenuEntity',
    params: data
  })
}

// 设置模块首页菜单 (Auth)
export function SetModuelIndexPage(data) {
  return request({
    url: '/Platform/Menu/SetModuelIndexPage',
    method: 'post',
    data
  })
}
