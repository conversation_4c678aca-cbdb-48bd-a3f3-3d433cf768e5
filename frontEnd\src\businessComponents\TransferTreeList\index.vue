<template>
  <div class="transferTreeBox">
    <!-- 左侧待选内容 -->
    <div class="SelectBox">
      <div class="boxTitle">
        <el-checkbox v-model="checkLeftAll" @change="clickLeftSelect"
          >可用的选项</el-checkbox
        >
        <div>{{ leftCheckLength }} / {{ leftDataLength }}</div>
      </div>
      <div class="filter-search">
        <el-input
          v-model="filterLeftText"
          placeholder="搜索..."
          prefix-icon="el-icon-search"
          v-if="isShowSearchLeftInput"
        />
      </div>
      <div
        :class="['boxCenter', !isShowSearchRightInput ? 'boxCenterHeight' : '']"
      >
        <el-tree
          ref="leftTree"
          :data="leftData"
          :props="defaultProps"
          show-checkbox
          default-expand-all
          :expand-on-click-node="false"
          :check-on-click-node="true"
          node-key="Id"
          :filter-node-method="filterLeftNode"
          @check="handleCheckChangeLeft"
        />
      </div>
    </div>

    <!-- 中间穿梭按钮 -->
    <div class="transferBtn">
      <div class="pickBtn">
        <el-button
          type="primary"
          :disabled="leftCheckLength === 0"
          @click="towardsRight"
          >添加<i class="el-icon-arrow-right el-icon--right"
        /></el-button>
      </div>
      <div class="pickBtn">
        <el-button
          icon="el-icon-arrow-left"
          :disabled="rightCheckLength === 0"
          @click="towardsLeft"
          >移除</el-button
        >
      </div>
    </div>

    <!-- 右侧已选内容 -->
    <div class="SelectBox">
      <div class="boxTitle">
        <el-checkbox v-model="checkRightAll" @change="clickRightSelect"
          >已选的选项</el-checkbox
        >
        <div>{{ rightCheckLength }} / {{ rightDataLength }}</div>
      </div>
      <div class="filter-search">
        <el-input
          v-model="filterRightText"
          placeholder="搜索..."
          prefix-icon="el-icon-search"
          v-if="isShowSearchRightInput"
        />
      </div>
      <div
        :class="['boxCenter', isShowSearchRightInput ? 'boxCenterHeight' : '']"
      >
        <el-tree
          ref="rightTree"
          :data="rightData"
          :props="defaultProps"
          show-checkbox
          default-expand-all
          :expand-on-click-node="false"
          :check-on-click-node="true"
          node-key="Id"
          :filter-node-method="filterRightNode"
          @check="handleCheckChangeRight"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    TransferTitle: {
      type: String,
      default: "",
    },
    DepartmentUnFitList: {
      type: Array,
      default: () => [],
    },
    DepartmentFitList: {
      type: Array,
      default: () => [],
    },
    isShowSearchRightInput: {
      type: Boolean,
      default: true,
    },
    isShowSearchLeftInput: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      defaultProps: {
        label: "Name",
        children: "Child",
        value: "Id",
      },
      leftData: [],
      rightData: [],
      leftDataLength: 0,
      leftCheckLength: 0,
      rightDataLength: 0,
      rightCheckLength: 0,
      checkLeftAll: false,
      checkRightAll: false,

      filterLeftText: "",
      filterRightText: "",
    };
  },
  watch: {
    filterLeftText(val) {
      this.$refs.leftTree.filter(val);
    },
    filterRightText(val) {
      this.$refs.rightTree.filter(val);
    },
    DepartmentUnFitList: {
      handler(newVal, oldVal) {
        console.log(newVal, "===");
        this.leftData = JSON.parse(JSON.stringify(newVal));
        this.actualTimeTotal();
        // this.checkLeftAll = this.leftCheckLength === this.leftDataLength // 判断是否选中全选
      },
      deep: true,
      immediate: true,
    },
    DepartmentFitList: {
      handler(newVal, oldVal) {
        this.rightData = JSON.parse(JSON.stringify(newVal));
        this.actualTimeTotal();
        console.log(this.rightData, "+++");
        console.log(this.rightDataLength, "---");
        // this.checkRightAll = this.rightCheckLength === this.rightDataLength// 判断是否选中全选
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  mounted() {
    this.leftData = JSON.parse(JSON.stringify(this.DepartmentUnFitList));
    this.rightData = JSON.parse(JSON.stringify(this.DepartmentFitList));
    this.actualTimeTotal();
  },
  methods: {
    isNestedArray(arr) {
      return Array.isArray(arr) && arr.some(Array.isArray);
    },
    // 搜索
    filterLeftNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },

    filterRightNode(value, data) {
      if (!value) return true;
      return data.Name.indexOf(value) !== -1;
    },

    // 点击向右穿梭
    towardsRight() {
      // (leafOnly, includeHalfChecked) 接收两个 boolean 类型的参数，
      // 1. 是否只是叶子节点，默认值为 false 2. 是否包含半选节点，默认值为 false
      const checkedNodes = this.$refs.leftTree.getCheckedNodes(false, true); // 包含半选
      const checkedKeys = this.$refs.leftTree.getCheckedKeys(false);

      const copyNodes = JSON.parse(JSON.stringify(checkedNodes));
      copyNodes.forEach((x) => {
        x.Child = [];
        if (!this.$refs.rightTree.getNode(x.Id)) {
          this.$refs.rightTree.append(x, x.ParentId);
        }
      });

      checkedKeys.forEach((x) => {
        this.$refs.leftTree.remove(x);
      });
      this.afterToward();
    },
    // 点击向左穿梭
    towardsLeft() {
      const checkedNodes = this.$refs.rightTree.getCheckedNodes(false, true); // 包含半选
      const checkedKeys = this.$refs.rightTree.getCheckedKeys(false);

      const copyNodes = JSON.parse(JSON.stringify(checkedNodes));
      copyNodes.forEach((x) => {
        x.Child = [];
        if (!this.$refs.leftTree.getNode(x.Id)) {
          this.$refs.leftTree.append(x, x.ParentId);
        }
      });

      checkedKeys.forEach((x) => {
        this.$refs.rightTree.remove(x);
      });

      this.afterToward();
    },

    // 点击全选-左侧
    clickLeftSelect() {
      if (this.checkLeftAll) {
        this.$refs.leftTree.setCheckedNodes(this.leftData);
      } else {
        this.$refs.leftTree.setCheckedKeys([]);
      }
      this.actualTimeCheck();
    },

    // 点击全选-右侧
    clickRightSelect() {
      if (this.checkRightAll) {
        this.$refs.rightTree.setCheckedNodes(this.rightData);
      } else {
        this.$refs.rightTree.setCheckedKeys([]);
      }
      this.actualTimeCheck();
    },

    // 数据穿梭后
    afterToward() {
      this.$refs.leftTree.setCheckedKeys([]);
      this.$refs.rightTree.setCheckedKeys([]);
      this.actualTimeCheck();
      this.actualTimeTotal();
      console.log(this.leftData, this.rightData);
      this.$emit("getTransferData", {
        leftData: this.leftData,
        rightData: this.rightData,
      });
    },

    // 实时计算选中个数
    actualTimeCheck() {
      this.leftCheckLength = this.$refs.leftTree
        .getCheckedNodes(true)
        .filter((item) => item.ParentId)?.length;
      this.rightCheckLength = this.$refs.rightTree
        .getCheckedNodes(true)
        .filter((item) => item.ParentId)?.length;
    },

    // 实时计算总数
    actualTimeTotal() {
      this.leftDataLength = this.countLeafNodes(this.leftData);
      this.rightDataLength = this.countLeafNodes(this.rightData);
    },

    countLeafNodes(nodes) {
      let count = 0;
      for (const node of nodes) {
        if (node.Child && node.Child.length > 0) {
          count += this.countLeafNodes(node.Child);
        } else {
          count += 1;
        }
      }
      return count;
    },
    handleCheckChangeLeft(
      checkedNodes,
      checkedKeys,
      halfCheckedNodes,
      halfCheckedKeys
    ) {
      this.leftCheckLength = this.$refs.leftTree
        .getCheckedNodes(true)
        .filter((item) => item.ParentId)?.length;
    },

    handleCheckChangeRight(
      checkedNodes,
      checkedKeys,
      halfCheckedNodes,
      halfCheckedKeys
    ) {
      this.rightCheckLength = this.$refs.rightTree
        .getCheckedNodes(true)
        .filter((item) => item.ParentId)?.length;
    },
    // 清楚选中
    clearChoose() {
      this.checkRightAll = this.checkLeftAll = false;
      this.leftCheckLength = this.rightCheckLength = 0;
    },
  },
};
</script>
<style lang="scss" scoped>
.transferTreeBox {
  display: flex;
  justify-content: flex-start;

  .SelectBox {
    border: 1px solid #e6ebf5;
    border-radius: 4px;
    overflow: hidden;
    background: #ffffff;
    display: inline-block;
    vertical-align: middle;
    box-sizing: border-box;
    height: 400px;
    width: 240px;
    color: #fff;
    position: relative;

    .boxTitle {
      background-color: #ffffff;
      height: 40px;
      line-height: 40px;
      padding-left: 15px;
      padding-right: 15px;
      cursor: pointer;
      border-bottom: 1px solid #e6ebf5;
      color: rgba(34, 40, 52, 0.85);
      font-size: 14px;
      font-weight: normal;
      display: flex;
      justify-content: space-between;
    }

    .filter-search {
      margin: 15px;
    }

    .boxCenter {
      padding-left: 15px;
      padding-right: 15px;
      width: 100%;
      height: 280px;
      overflow-y: auto;
    }
    .boxCenterHeight {
      height: 340px;
    }
  }

  .transferBtn {
    min-width: 160px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .pickBtn {
      margin-top: 5px;
      margin-bottom: 5px;
    }
  }

  // ::v-deep .el-tree-node__content>.el-tree-node__expand-icon {
  //   display: none;
  // }
  ::v-deep .el-tree-node__content:hover {
    background-color: transparent !important;
  }
  ::v-deep .el-tree-node:focus > .el-tree-node__content {
    background-color: transparent !important;
  }
  ::v-deep
    .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: transparent !important;
  }
}
</style>
