import request from '@/utils/request'

// 表单模板列表 (Auth)
export function FormsLoad(data) {
  return request({
    method: 'post',
    url: '/Platform/Forms/Load',
    data
  })
}

// 添加表单模板 (Auth)
export function FormsAdd(data) {
  return request({
    method: 'post',
    url: '/Platform/Forms/Add',
    data
  })
}

// 得到表单可使用的Model (Auth)
export function FormsGetFromModel() {
  return request({
    method: 'get',
    url: '/Platform/Forms/GetFromModel'
  })
}

// 删除表单模板 (Auth)
export function FormsDelete(data) {
  return request({
    method: 'post',
    url: '/Platform/Forms/Delete',
    data
  })
}

// 修改表单模板 (Auth)
export function FormsUpdate(data) {
  return request({
    method: 'post',
    url: '/Platform/Forms/Update',
    data
  })
}

// 得到表单模板 (Auth)
export function FormsGet(data) {
  return request({
    method: 'get',
    url: '/Platform/Forms/Get',
    params: data
  })
}
