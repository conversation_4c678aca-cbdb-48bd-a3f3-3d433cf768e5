<script>
import OSS from "ali-oss";
import request from "@/utils/request";
import { Upload } from "element-ui";
import { GetOssUrl, SecurityToken } from "@/api/sys/index";
export default {
  name: "OSSUpload",
  mixins: [Upload],
  props: {
    ossOnSuccess: {
      type: Function,
      default: Function,
    },
    piecesize: {
      type: Number,
      default: 2,
    },
    httpRequest: {
      type: Function,
      default: function (params) {
        try {
          request({
            url: "/Platform/Sys_File/SecurityToken",
            method: "post",
          }).then((result) => {
            this.$nextTick(() => {
              var piecesize = this.data?.piecesize
                ? this.data.piecesize * 1
                : 2;
              const ossClient = new OSS({
                region: "oss-" + result.Data.regionId,
                secure: true,
                accessKeyId: result.Data.AccessKeyId,
                accessKeySecret: result.Data.AccessKeySecret,
                stsToken: result.Data.SecurityToken,
                bucket: result.Data.bucket,
                refreshSTSToken: async () => {
                  // 刷新临时访问凭证的回调，在上传大文件的时候会用到
                  const info = await this.securityToken();
                  return {
                    accessKeyId: info.AccessKeyId,
                    accessKeySecret: info.AccessKeySecret,
                    stsToken: info.SecurityToken,
                  };
                },
                refreshSTSTokenInterval: 900000,
              });
              const file = params.file;
              var thisdate = new Date();
              let tenantId = localStorage.getItem("TenantId");
              ossClient
                .multipartUpload(
                  tenantId +
                    "/" +
                    thisdate.getFullYear() +
                    "/" +
                    (thisdate.getMonth() * 1 + 1) +
                    "/" +
                    thisdate.getDate() +
                    "/" +
                    (thisdate.getMinutes() +
                      "_" +
                      thisdate.getSeconds() +
                      "_" +
                      thisdate.getMilliseconds()) +
                    "/" +
                    file.name,
                  file,
                  {
                    progress: function (p, checkpoint) {
                      // checkpoint参数用于记录上传进度，断点续传上传时将记录的checkpoint参数传入即可。浏览器重启后无法直接继续上传，您需要手动触发上传操作。
                      this.process = checkpoint;
                      params.onProgress({ percent: Math.floor(p * 100) }); // 触发el-upload组件的onProgress方法
                    },
                    parallel: 4,
                    // 设置分片大小。默认值为100 MB，最小值为100 KB。
                    partSize: 1024 * 1024 * piecesize,
                    meta: {},
                    // mime: 'text/plain'
                  }
                )
                .then(
                  (val) => {
                    if (val.res.statusCode === 200) {
                      // console.log('val', val)
                      var fileurl =
                        val.res.requestUrls[0] &&
                        val.res.requestUrls[0].split("?")[0];
                      GetOssUrl({ url: fileurl }).then((res) => {
                        // console.log(fileurl, 'fileUrl')
                        params.onSuccess({
                          Data:
                            fileurl +
                            "*" +
                            file.size +
                            "*" +
                            file.name.substr(file.name.lastIndexOf(".")) +
                            "*" +
                            file.name,
                          encryptionUrl: res.Data,
                        });
                      });
                    }
                  },
                  (err) => {
                    console.log("err", err);
                    params.onError(err);
                  }
                );
            });
          });
        } catch (e) {
          console.log("e", e);
          params.onError(e);
        }
      },
    },
  },
  data() {
    return {
      process: null,
    };
  },
  watch: {
    process(newName) {
      this.$emit("getprocess", newName);
    },
  },
  mounted() {
    // this.gettoken()
    // this.client = new OSS(this.ossConfig)
    // this.resumeclient = new OSS(this.ossConfig)
  },
  methods: {
    securityToken() {
      return new Promise((resolve, reject) => {
        SecurityToken({})
          .then((res) => {
            resolve(res.Data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },

    //     async gettoken() {
    //       console.log('sdsdsdsdsds')
    //       await request({
    //         url: 'SYS/Sys_File/SecurityToken',
    //         method: 'post'
    //       }).then(res => {
    //         if (res.IsSucceed) {
    //           this.ossConfig = {
    //             region: 'oss-' + res.Data.AccessKeyId,
    //             accessKeyId: res.Data.AccessKeyId,
    //             accessKeySecret: res.Data.AccessKeySecret,
    //             stsToken: res.Data.SecurityToken,
    //             bucket: res.Data.regionId
    //           }
    //         }
    //       })
    //     },
    //     // 暂停分片上传。
    //     puassclient() {
    //       this.client.cancel()
    //     },
    //     // 恢复上传。
    //     async  resumeUpload(file) {
    //       try {
    //         const result = await this.resumeclient.multipartUpload('exampleobject.txt', file, {
    //           progress: function(p, checkpoint) {
    //             this.process = checkpoint
    //           },
    //           checkpoint: this.process,
    //           meta: { year: 2020, people: 'test' },
    //           mime: 'text/plain'
    //         })
    //       } catch (e) {
    //         console.log(e)
    //       }
    //     }
  },
};
</script>

