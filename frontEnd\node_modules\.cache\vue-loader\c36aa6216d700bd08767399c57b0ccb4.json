{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\generation.vue?vue&type=style&index=0&id=7517d6d6&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\energyManagement\\pJEnergyAnalysis\\eleNew\\components\\generation.vue", "mtime": 1754615596910}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1724304666593}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1724304687579}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1724304672268}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1724304662834}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmdlbmVyYXRpb24gewogIGhlaWdodDogMzkycHg7CiAgYmFja2dyb3VuZDogI2ZmZjsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgd2lkdGg6IDEwMCU7CiAgcGFkZGluZzogMTZweDsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIG1hcmdpbi1ib3R0b206IDE2cHg7CiAgLnRpdGxlIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgLmxlZnQgewogICAgICBjb2xvcjogIzY2NjsKICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgIH0KICAgIC5yaWdodCB7CiAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgY29sb3I6ICNiOGJlYzg7CiAgICB9CiAgfQogIC5jaGFydEJveCB7CiAgICBoZWlnaHQ6IDMyMHB4OwogIH0KfQo="}, {"version": 3, "sources": ["generation.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "generation.vue", "sourceRoot": "src/views/business/energyManagement/pJEnergyAnalysis/eleNew/components", "sourcesContent": ["<template>\n  <div v-loading=\"loading\" class=\"generation\" element-loading-text=\"加载中...\">\n    <div class=\"title\">\n      <div class=\"left\">发电及用电趋势图</div>\n      <div class=\"right\">不包含重钢工厂</div>\n    </div>\n    <div class=\"chartBox\">\n      <v-chart :option=\"lineChartOption\" :autoresize=\"true\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetElectricTrend } from '@/api/business/energyManagement.js'\nimport VChart from 'vue-echarts'\nimport { use } from 'echarts/core'\nimport { CanvasRenderer } from 'echarts/renderers'\nimport { <PERSON><PERSON><PERSON>, Line<PERSON>hart, PieChart } from 'echarts/charts'\nimport {\n  GridComponent,\n  LegendComponent,\n  TooltipComponent,\n  TitleComponent,\n  DataZoomComponent\n} from 'echarts/components'\nuse([\n  Canvas<PERSON>ender<PERSON>,\n  <PERSON><PERSON>hart,\n  <PERSON><PERSON>hart,\n  <PERSON><PERSON>hart,\n  DataZoomComponent,\n  Grid<PERSON>omponent,\n  LegendComponent,\n  TitleComponent,\n  TooltipComponent\n])\nexport default {\n  components: {\n    VChart\n  },\n  props: {\n    isPhotovoltaic: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      lineChartOption: {\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          selected: {\n            '总发电(光伏)': false,\n            '售卖(光伏)': false\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '1%',\n          bottom: '80'\n        },\n        xAxis: {\n          type: 'category',\n          data: [],\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          }\n        },\n\n        color: ['#FF902C', '#298DFF', '#01AD5E', '#00B0F0', '#FF5E7C'],\n        yAxis: [\n          {\n            type: 'value',\n            position: 'left',\n            logBase: 10\n          }\n        ],\n        dataZoom: {\n          type: 'slider'\n        },\n        series: [\n          {\n            name: '总用电',\n            data: [5, 6, 7, 8, 1, 2, 3, 4, 6],\n            type: 'line',\n            tooltip: {\n              valueFormatter: function(value) {\n                return `${value || 0}` + ' 度'\n              }\n            }\n          },\n          {\n            name: '用电(市电)',\n            data: [1, 2, 3, 4, 5, 6, 7, 8],\n            type: 'line',\n            tooltip: {\n              valueFormatter: function(value) {\n                return `${value || 0}` + ' 度'\n              }\n            }\n          }\n        ]\n      },\n      loading: true\n    }\n  },\n  computed: {\n    parentData() {\n      return {\n        DateType: this.DateType(),\n        StartTime: this.StartTime(),\n        randomInteger: this.randomInteger()\n      }\n    }\n  },\n  watch: {\n    parentData: {\n      handler(nv, ov) {\n        this.getElectricTrend()\n      }\n    }\n  },\n  created() {\n    this.getElectricTrend()\n  },\n  mounted() {\n\n  },\n  inject: ['DateType', 'StartTime', 'randomInteger'],\n  methods: {\n    async getElectricTrend() {\n      this.loading = true\n      const res = await GetElectricTrend(this.parentData)\n      if (res.IsSucceed) {\n        this.lineChartOption.xAxis.data = res.Data.map(item => item.Key)\n        this.lineChartOption.series[0].data = res.Data.map(item => item.Total ?? 0)\n        this.lineChartOption.series[1].data = res.Data.map(item => item.Electric ?? 0)\n        if (this.isPhotovoltaic) {\n          this.lineChartOption.series.push(\n            {\n              name: '用电(光伏)',\n              data: [3, 4, 5, 6, 7, 8, 1, 2],\n              type: 'line',\n              tooltip: {\n                valueFormatter: function(value) {\n                  return `${value || 0}` + ' 度'\n                }\n              }\n            },\n            {\n              name: '总发电(光伏)',\n              data: [7, 8, 3, 4, 5, 6, 1, 2],\n              type: 'line',\n              tooltip: {\n                valueFormatter: function(value) {\n                  return `${value || 0}` + ' 度'\n                }\n              }\n            },\n            {\n              name: '售卖(光伏)',\n              data: [9, 3, 4, 5, 6, 7, 8, 1],\n              type: 'line',\n              tooltip: {\n                valueFormatter: function(value) {\n                  return `${value || 0}` + ' 度'\n                }\n              }\n            }\n          )\n          this.lineChartOption.series[2].data = res.Data.map(item => item.PV ?? 0)\n          this.lineChartOption.series[3].data = res.Data.map(item => item.TotalPV ?? 0)\n          this.lineChartOption.series[4].data = res.Data.map(item => item.SellPV ?? 0)\n        }\n      }\n      this.loading = false\n    }\n  }\n}\n</script>\n<style scoped lang='scss'>\n.generation {\n  height: 392px;\n  background: #fff;\n  border-radius: 4px;\n  width: 100%;\n  padding: 16px;\n  box-sizing: border-box;\n  margin-bottom: 16px;\n  .title {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 16px;\n    .left {\n      color: #666;\n      font-weight: bold;\n      font-size: 16px;\n    }\n    .right {\n      font-size: 12px;\n      color: #b8bec8;\n    }\n  }\n  .chartBox {\n    height: 320px;\n  }\n}\n</style>\n"]}]}