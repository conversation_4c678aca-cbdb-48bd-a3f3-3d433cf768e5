import request from '@/utils/request'

// 获取当前用户消息列表(分页) (Auth)
export function GetNoticePageList(data) {
  return request({
    url: '/Platform/Notice/GetNoticePageList',
    method: 'post',
    data: data
  })
}

// 修改用户消息状态 (Auth)
export function UpdateNoticeStatus(data) {
  return request({
    url: '/Platform/Notice/UpdateNoticeStatus',
    method: 'post',
    data: data
  })
}
// 获取当前用户未读消息数量 (Auth)
export function GetNoticeCount() {
  return request({
    url: '/Platform/Notice/GetNoticeCount',
    method: 'post'
  })
}

