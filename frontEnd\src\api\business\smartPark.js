import request from '@/utils/request'
// 智慧园区模块

/** ************  领导驾驶舱 ****************/
// 获取驾驶舱分页列表
export function GetPageList(data) {
  return request({
    method: 'post',
    url: '/DF/CockpitReport/GetList',
    data
  })
}
// 下载驾驶舱报表模板
export function DownloadTemplete(data) {
  return request({
    method: 'get',
    url: '/DF/CockpitReport/DownloadTemplete',
    params: data,
    responseType: 'blob'
  })
}

// 导入驾驶舱报表
export function UploadFile(data) {
  return request({
    method: 'post',
    url: '/DF/CockpitReport/UploadFile',
    data
  })
}

// 撤回 提交
export function OperateData(data) {
  return request({
    method: 'post',
    url: '/DF/CockpitReport/OperateData',
    data
  })
}

// 历史记录
export function GetRecordList(data) {
  return request({
    method: 'post',
    url: '/DF/CockpitReport/GetRecordList',
    data
  })
}

// 下载
export function DownloadDataAsync(data) {
  return request({
    method: 'post',
    url: '/DF/CockpitReport/DownloadDataAsync',
    data,
    responseType: 'blob'
  })
}
