//
import request from '@/utils/request'

//  监测档案
// 分页查询设备详情
export function GetEquipmentList(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetEquipmentList',
    data
  })
}

// 删除设备
export function DeleteEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/DeleteEquipment',
    data
  })
}
// 新增/编辑设备
export function EditEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/EditEquipment',
    data
  })
}
// 查询设备详情
export function GetEquipmentEntity(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetEquipmentEntity',
    data
  })
}

// 导出数据
export function ExportHazchemEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/ExportHazchemEquipment',
    data
  })
}
// v2  导出数据
export function ExportEquipmentList(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/v2/ExportEquipmentList',
    data
  })
}

// 告警配置
// 获取告警配置列表
export function GetQuotaList(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetQuotaList',
    data
  })
}
// 获取告警配置详情
export function GetQuotaEntity(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetQuotaEntity',
    data
  })
}

// 新增/编辑告警配置
export function EditQuota(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/EditQuota',
    data
  })
}

// 删除告警配置
export function DeleteQuota(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/DeleteQuota',
    data
  })
}
// 获取设备配置项信息
export function GetEquipmentItemList(data) {
  return request({
    method: 'post',
    url: '/DF/Equipment/GetEquipmentItemList',
    data
  })
}

export function DeleteAllQuota(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/DeleteAllQuota',
    data
  })
}

// 告警信息

// 获取设备配置项信息
export function GetWarningList(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetWarningList',
    data
  })
}
// 危化品告警信息类型列表
export function GetWarningType(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetWarningType',
    data
  })
}
// 导出危化品告警
export function ExportWarning(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/ExportWarning',
    data
  })
}

// 监测数据
// 获取监测数据
export function GetDataList(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetDataList',
    data
  })
}

// 导出设备监测数据
export function ExportDataList(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/ExportDataList',
    data
  })
}

// 查询历史监测数据
export function GetHistoryDataList(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetHistoryDataList',
    data
  })
}

// 查询设备类型
export function GetDictionaryDetailListByCode(data) {
  return request({
    method: 'post',
    url: '/Platform/Dictionary/GetDictionaryDetailListByCode',
    data
  })
}
// 获取园区地址
export function GetParkArea(data) {
  return request({
    method: 'post',
    url: '/DF/DFPark/GetParkArea',
    data
  })
}
// 获取门禁设备管理列表
export function GetEquipmentlList(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/GetEquipmentlList',
    data
  })
}
// 删除门禁设备管理
export function DelEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/DelEquipment',
    data
  })
}
// 新增编辑门禁设备管理
export function SubEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/SubEquipment',
    data
  })
}
// 导出门禁设备管理
export function ExportEntrancePersonnel(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/ExportEntrancePersonnel',
    data
  })
}
// 手动同步
export function SyncEquipment(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/SyncEquipment',
    data
  })
}
// 告警列表
export function entranceWarningGetWarningList(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceWarning/GetWarningList',
    data
  })
}
// 导出告警列表
export function ExportEntranceWarning(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceWarning/ExportEntranceWarning',
    data
  })
}
// 配置授权名单列表
export function GetAuthorizedList(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/GetAuthorizedList',
    data
  })
}
// 配置授权名单树列表
export function GetAuthorizedTree(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/GetAuthorizedTree',
    data
  })
}
// 新增配置授权名单
export function AddAuthorized(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/AddAuthorized',
    data
  })
}
// 删除配置授权名单
export function DeleteAuthorized(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/DeleteAuthorized',
    data
  })
}
// 设备连接
export function EquipmentConnect(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/EquipmentConnect',
    data
  })
}
// 下载模板
export function AuthorizedImportTemplate(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/AuthorizedImportTemplate',
    data
  })
}
// 导入模板
export function AuthorizedEquipmentImport(data) {
  return request({
    method: 'post',
    url: '/DF/EntranceEquipment/AuthorizedEquipmentImport',
    data
  })
}
// 获取设备类型配置数据
export function GetHazchemDTCList(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetHazchemDTCList',
    data
  })
}
// 新增编辑设备类型配置数据
export function EditHazchemDTC(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/EditHazchemDTC',
    data
  })
}
// 删除设备类型配置数据
export function DeleteHazchemDTC(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/DeleteHazchemDTC',
    data
  })
}
// 关闭告警
export function UpdateWarningStatus(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/UpdateWarningStatus',
    data
  })
}
// 门禁关闭告警
export function UpdateEntranceWarningStatus(data) {
  return request({
    method: 'post',
    url: 'DF/EntranceWarning/UpdateWarningStatus',
    data
  })
}

// 危化品模板下载
export function HazchemImportTemplate(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/HazchemImportTemplate',
    data
  })
}

// 导入
export function HazchemEquipmentImport(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/HazchemEquipmentImport',
    data
  })
}

// 危化品分析
// 获取告警状态分类数量统计
export function GetWarningCountTotal(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetWarningCountTotal',
    data
  })
}

// 获取最新的告警信息数据
export function GetWarningInfoList(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetWarningInfoList',
    data
  })
}

// 获取区域告警处理情况统计
export function GetAreaWarningHandleTotal(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetAreaWarningHandleTotal',
    data
  })
}

// 获取区域告警排行统计
export function GetAreaWarningRank(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetAreaWarningRank',
    data
  })
}

// 获取告警趋势统计
export function GetWarningTrendTotal(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetWarningTrendTotal',
    data
  })
}

// 获取告警分析页总数据
export function GetHazchemAnalysisData(data) {
  return request({
    method: 'post',
    url: '/DF/Hazchem/GetHazchemAnalysisData',
    data
  })
}
