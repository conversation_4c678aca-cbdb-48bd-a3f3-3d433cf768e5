<template>
  <div class="fill-set">
    <el-form ref="form" label-width="150px">
      <el-form-item label="当前计划">
        <strong>{{ plan.name }}</strong>
      </el-form-item>
      <el-form-item label="填报任务推送时间">
        <el-date-picker
          v-model="form.Push_Date"
          style="width:160px;"
          value-format="yyyy-MM-dd"
          type="date"
          placeholder="选择日期"
        />
        <el-time-select
          v-model="form.Push_Time"
          style="width:120px;margin-left:8px;"
          value-format="HH:mm"
          :picker-options="{
            start: '00:00',
            step: '00:15',
            end: '23:59'
          }"
          placeholder="选择时间"
        />
      </el-form-item>
      <el-form-item label="数据日期">
        <el-date-picker
          v-model="form.Data_Date"
          style="width:288px;"
          value-format="yyyy-MM-dd"
          type="date"
          placeholder="选择日期"
        />
      </el-form-item>
      <el-form-item label="" align="right">
        <el-button @click="$emit('dialogCancel')">取消</el-button>
        <el-button type="primary">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'FillSet',
  props: {
    plan: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {}
    }
  }
}
</script>
<style lang="scss" scoped>
.fill-set {
  margin-top: -16px;
}
</style>
