import request from "@/utils/request";
//获取访客设备列表
export function GetVisitorEquipmentPageList(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/GetVisitorEquipmentPageList",
    data,
  });
}
//访客配置
export function EditSettings(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/EditSettings",
    data,
  });
}
export function SaveSettings(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/SaveSettings",
    data,
  });
}

//获取访客配置
export function GetSettingsEntity(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/GetSettingsEntity",
    data,
  });
}
export function GetSettings(data) {
  return request({
    method: "get",
    url: "/DF/Visitor/GetSettings",
    params: data,
  });
}
//导出访客设备
export function ExportVisitorEquipment(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/ExportVisitorEquipment",
    data,
  });
}
//获取访客设备
export function GetVisitorEquipmentEntity(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/GetVisitorEquipmentEntity",
    data,
  });
}
//删除访客设备
export function DeleteVisitorEquipment(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/DeleteVisitorEquipment",
    data,
  });
}
//新增访客设备
export function EditVisitorEquipment(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/EditVisitorEquipment",
    data,
  });
}
//获取访客列表
export function GetVisitorsPageList(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/GetVisitorsPageList",
    data,
  });
}
export function GetPageList(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/GetPageList",
    data,
  });
}
//导出访客列表
export function ExportVisitors(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/ExportVisitors",
    data,
  });
}
export function ExportData(data) {
  return request({
    method: "get",
    url: "/DF/Visitor/ExportData",
    params: data,
    responseType: 'blob'
  });
}
//  v2 访客列表导出
export function ExportVisitorsList(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/v2/ExportVisitorsList",
    data,
  });
}
//获取访客
export function GetVisitorsEntity(data) {
  return request({
    method: "get",
    url: "/DF/Visitor/GetVisitorDetail",
    params: data,
  });
}
//新增修改设备
export function EditVisitors(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/EditVisitors",
    data,
  });
}
//审批
export function ApprovalVisitors(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/ApprovalVisitors",
    data,
  });
}
//发送通行证
export function SendPassCheck(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/SendPassCheck",
    data,
  });
}
//再次生成通行证
export function SendPassCheckAgain(data) {
  return request({
    method: "post",
    url: "/DF/Visitor/SendPassCheckAgain",
    data,
  });
}

export function GetVisitorDetail(data) {
  return request({
    method: "get",
    url: "/DF/Visitor/GetVisitorDetail",
    params: data,
  });
}


