{"remainingRequest": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\realVideo\\Screen.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\src\\views\\business\\safetyManagement\\realVideo\\Screen.vue", "mtime": 1754618172895}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1724304672280}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1724304662819}, {"path": "D:\\project\\platform_framework_hlj\\hljbimdigitalfactory\\frontEnd\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1724304679223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Screen.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Screen.vue", "sourceRoot": "src/views/business/safetyManagement/realVideo", "sourcesContent": ["<template>\n  <div class=\"screenContent app-container abs100\">\n    <el-card class=\"box-card h100\">\n      <div class=\"text-center\">\n        <el-button-group class=\"player-btn-group\">\n          <el-button\n            v-for=\"list in playerBtnGroup\"\n            :key=\"list.num\"\n            type=\"primary\"\n            :class=\"{ active: playerLength == list.num }\"\n            @click.prevent=\"setPlayerLength(list.num)\"\n          >{{ list.name }}</el-button>\n        </el-button-group>\n      </div>\n      <el-container class=\"customContainer\">\n        <el-aside class=\"aside\" width=\"400px\">\n          <el-input v-model=\"filterText\" placeholder=\"输入设备名称搜索\" />\n          <el-tree\n            ref=\"tree\"\n            class=\"filter-tree\"\n            :data=\"treeData\"\n            :props=\"defaultProps\"\n            highlight-current\n            node-key=\"Id\"\n            :filter-node-method=\"filterNode\"\n            :default-expanded-keys=\"defaultExpandedKeys\"\n            @node-click=\"treeNodeClick\"\n          />\n        </el-aside>\n        <el-main>\n          <el-row class=\"video-show\">\n            <el-col\n              v-for=\"(player, index) in players\"\n              :key=\"index\"\n              :span=\"colSpan\"\n              class=\"video\"\n              @click=\"clickPlayer(player, index, $event)\"\n              @touchend=\"clickPlayer(player, index, $event)\"\n            >\n              <LivePlayer\n                :video-url=\"player.url\"\n                :water-mark=\"player.osd\"\n                v-loading=\"player.bLoading\"\n                :smart=\"player.bSmart\"\n                :poster=\"player.poster\"\n                :controls=\"player.bControls && !loopPlaying\"\n                live\n                muted\n                stretch\n                :loading.sync=\"player.bLoading\"\n                element-loading-text=\"加载中...\"\n                element-loading-background=\"#000\"\n                @fullscreen=\"onFullscreenChange(player, index, $event)\"\n                @error=\"onError(player, index, $event)\"\n                @play=\"onPlay(player, index, $event)\"\n                @message=\"$message\"\n              />\n              <div\n                v-if=\"bVideoTitle && player.title\"\n                class=\"video-title\"\n                :title=\"player.title\"\n              >\n                {{ player.title }}\n              </div>\n              <div\n                v-show=\"player.url && player.bCloseShow && !loopPlaying\"\n                class=\"video-close\"\n                @click=\"closeVideo(index, true)\"\n              >\n                关闭\n              </div>\n            </el-col>\n          </el-row>\n        </el-main>\n      </el-container>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport LivePlayer from '@liveqing/liveplayer'\nimport { GetEquipmentTree, LookVideo } from '@/api/business/safetyManagement'\n\nexport default {\n  components: {\n    LivePlayer\n  },\n  data() {\n    return {\n      q: '',\n      players: [],\n      playerIdx: 0,\n      colSpan: 12,\n      playerLength: 1,\n      loadedData: false,\n      localData: {\n        num1: {},\n        num4: {},\n        num9: {}\n      },\n      channelListDlgTitle: '',\n      protocol: '',\n      showTree: true,\n      showGroupTree: false, // lazy load group tree\n      showTip: false,\n      treeLoading: false,\n      groupTreeLoading: false,\n      queryDevTreeLoading: false,\n      queryGroupTreeLoading: false,\n      defExpandDevs: [],\n      devLevelFilter: false,\n      groupLevelFilter: false,\n      fullscreenFlag: false,\n      contextMenuTarget: null,\n      contextMenuVisible: false,\n      contextMenuNodeData: null,\n      treeProps: {\n\n      },\n      bSmartStream: false,\n      bVideoTitle: false,\n      level: 0,\n      bPlayerFullscreen: false, // any player is fullscreen\n      outHevcTipIdx: -1, // idx of player is out hevc stuck\n      activeTab: 'dev',\n      filterText: '',\n      treeData: [],\n      defaultProps: {\n        children: 'Children',\n        label: 'Name'\n      },\n      defaultExpandedKeys: []\n    }\n  },\n  computed: {\n    playerBtnGroup() {\n      var list = [{\n        num: 1,\n        name: '单屏'\n      }, {\n        num: 4,\n        name: '四分屏'\n      }, {\n        num: 9,\n        name: '九分屏'\n      }]\n      return list\n    },\n    playing() {\n      var player = this.players[this.playerIdx] || {}\n      return !!player.url\n    },\n    treeEmptyText() {\n      return this.treeLoading ? '加载中...' : '暂无数据'\n    },\n    showQueryDevTree() {\n      if (!this.q) return false\n      if (this.activeTab === 'dev' && this.devLevelFilter) {\n        this.queryDevTreeLoading = true\n        return true\n      }\n      return false\n    },\n    showQueryGroupTree() {\n      if (!this.q) return false\n      if (this.activeTab === 'group' && this.groupLevelFilter) {\n        this.queryGroupTreeLoading = true\n        return true\n      }\n      return false\n    }\n  },\n  watch: {\n    bSmartStream: function(newVal, oldVal) {\n      for (const idx in this.players) {\n        const player = this.players[idx]\n        if (!player) continue\n        const _url = player.url\n        if (!_url) continue\n        player.url = ''\n        player.bSmart = newVal\n        this.$nextTick(() => {\n          player.url = _url\n        })\n      }\n    },\n    filterText(val) {\n      this.$refs.tree.filter(val)\n    }\n  },\n  mounted() {\n    this.setPlayerLength(this.playerLength)\n    this.contextMenuTarget = document.querySelector('#tab-tree-wrapper')\n    this.getEquipmentTree()\n  },\n  beforeRouteEnter(to, from, next) {\n    next(vm => {\n      if (to.query.protocol) {\n        vm.protocol = to.query.protocol\n      }\n    })\n  },\n  beforeRouteUpdate(to, from, next) {\n    this.clearVideos()\n    if (to.query.protocol) {\n      this.protocol = to.query.protocol\n    }\n    next()\n  },\n  beforeRouteLeave(to, from, next) {\n    this.clearVideos()\n    next()\n  },\n  methods: {\n    clearVideos() {\n      this.outHevcTipIdx = -1\n      for (var idx in this.players) {\n        this.closeVideo(idx)\n      }\n    },\n    play(index, channel, next) {\n      console.log(channel)\n      var i = 0\n      var player = null\n      for (var _player of this.players) {\n        if (index === i) {\n          player = _player\n          break\n        }\n        i++\n      }\n      if (!player) {\n        this.$message({\n          type: 'error',\n          message: '当前播放窗口已被占满！'\n        })\n        return\n      }\n      if (player.bLoading) return\n      player.bLoading = true\n      if (next) {\n        this.setPlayerIdx(index + 1)\n      }\n      if (channel.Node) {\n        player.node = channel.Node\n        this.$set(player.node, 'playing', true)\n        delete channel.Node\n      }\n      LookVideo({ id: channel.ID }).then(res => {\n        if (res.IsSucceed) {\n          const videoUrl = res.Data\n          player.bSmart = this.playerLength > 1 && this.bSmartStream\n          player.url = videoUrl\n          player.code = channel.ID\n          player.protocol = 'HLS'\n          player.title = channel.ID\n          if (player.node) {\n            this.$delete(player.node, 'playing')\n            var play = player.node.play || []\n            if (videoUrl) {\n              play.push(index)\n            }\n            if (play.length) {\n              this.$set(player.node, 'play', play)\n            }\n          }\n          if (!this.loadedData) {\n            this.localData['num' + this.playerLength] = {}\n          }\n          this.localData['num' + this.playerLength]['c' + index] = JSON.stringify(channel)\n          this.setLocalData()\n        } else {\n          this.closeVideo(this.playerIdx)\n          this.$message.error(res.Message)\n        }\n      })\n    },\n    closeVideo(idx, manual = false) {\n      var player = this.players[idx]\n      if (!player) return\n      if (player.closeTimer) {\n        clearTimeout(player.closeTimer)\n        player.closeTimer = 0\n      }\n      if (this.outHevcTipIdx === idx) {\n        this.outHevcTipIdx = -1\n      }\n      if (player.node) {\n        var play = player.node.play || []\n        play = play.filter(val => val !== idx)\n        if (play.length) {\n          this.$set(player.node, 'play', play)\n        } else {\n          this.$delete(player.node, 'play')\n          this.$delete(player.node, 'playing')\n        }\n        delete player.node\n      }\n      player.mediaInfo = null\n      player.bCloseShow = false\n      player.bControls = false\n      player.bLoading = false\n      player.bSmart = false\n      player.bFullscreen = false\n      player.poster = ''\n      player.title = ''\n      player.osd = ''\n      player.url = ''\n      player.ptzType = 0\n      player.serial = ''\n      player.code = ''\n      if (manual) {\n        delete this.localData['num' + this.playerLength]['c' + idx]\n        this.setLocalData()\n      }\n    },\n    setPlayerLength(len) {\n      if (len === this.players.length) return\n      this.clearVideos()\n      this.players = []\n      this.playerLength = len\n      len === 1 ? this.colSpan = 24 : len === 4 ? this.colSpan = 12 : this.colSpan = 8\n      this.loadedData = false\n      this.playerIdx = 0\n      for (var i = 0; i < len; i++) {\n        this.players.push({\n          serial: '',\n          code: '',\n          url: '',\n          ptzType: 0,\n          protocol: '',\n          poster: '',\n          title: '',\n          osd: '',\n          bLoading: false,\n          bCloseShow: false,\n          bControls: false,\n          bSmart: false,\n          bFullscreen: false,\n          closeTimer: 0,\n          closeInterval: 0,\n          mediaInfo: null\n        })\n      }\n    },\n    setPlayerIdx(idx) {\n      var newIdx = idx % this.players.length\n      this.playerIdx = newIdx\n    },\n    onError(player, idx, e) {\n      if (e === 'MediaError' && player.mediaInfo && String(player.mediaInfo.videoCodec).startsWith('hvc')) {\n        this.outHevcTipIdx = idx\n        console.log('提示: 正在播放 H265 FLV 直出流, 确保浏览器版本较新, 并且开启硬件加速')\n      }\n    },\n    onPlay(player, idx, t) {\n      if (this.outHevcTipIdx === idx && t >= 1) {\n        this.outHevcTipIdx = -1\n      }\n    },\n    onFullscreenChange(player, idx, bFullscreen) {\n      if (player) {\n        player.bFullscreen = bFullscreen\n        this.bPlayerFullscreen = bFullscreen\n        if (bFullscreen) {\n          $(`#dev-tree-right .video-show .video:eq(${idx}) .ptz-block-fs`).draggable({\n            handle: '.ptz-center',\n            cancel: '.ptz-cell',\n            containment: `#dev-tree-right .video-show .video:eq(${idx}) .video-js`,\n            delay: 100\n          })\n        } else {\n          $(`#dev-tree-right .video-show .video:eq(${idx}) .ptz-block-fs`).draggable('destroy')\n        }\n      }\n    },\n    fullscreen() {\n      // if (this.isMobile()) {\n      //   this.$message({\n      //     type: \"error\",\n      //     message: \"请在电脑浏览器上使用该功能\"\n      //   });\n      //   return;\n      // }\n      this.$fullscreen.enter(this.$el.querySelector(`.video-show > div`), {\n        wrap: false,\n        callback: f => {\n          this.fullscreenFlag = f\n        }\n      })\n    },\n    treeNodeClick(data, node) {\n      console.log(node.isLeaf && (node.data.ParentId ?? '') != '')\n      if (node.isLeaf && (node.data.ParentId ?? '') != '') {\n        this.contextMenuNodeData = null\n        this.contextMenuNode = null\n        if (node && !node.playing) {\n          if (!node.clickCnt || node.clickCnt > 1) {\n            node.clickCnt = 1\n          } else {\n            node.clickCnt++\n          }\n          var player = this.players[this.playerIdx] || {}\n          if (player.bLoading) return\n          this.closeVideo(this.playerIdx)\n          this.$nextTick(() => {\n            this.play(this.playerIdx, {\n              ID: data.Id,\n              Name: data.Name,\n              Node: node\n            }, true)\n          })\n        }\n      }\n    },\n    async getEquipmentTree() {\n      const res = await GetEquipmentTree()\n      console.log('res', res)\n      if (res.IsSucceed) {\n        this.treeData = res.Data\n        if ((res.Data ?? []).length > 0) {\n          this.$nextTick(() => {\n            this.defaultExpandedKeys = [res.Data[0]?.Id, res.Data[0]?.Children[0]?.Id, res.Data[0]?.Children[0]?.Children[0]?.Id]\n            this.play(this.playerIdx, {\n              ID: res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id,\n              Name: res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Name,\n              Node: this.$refs.tree.getNode(res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id)\n            }, true)\n            this.$refs.tree.setCurrentKey(res.Data[0]?.Children[0]?.Children[0]?.Children[0]?.Id)\n          })\n        }\n      } else {\n        this.$message.error(res.Message)\n      }\n    },\n    filterNode(value, data) {\n      if (!value) return true\n      return data.Name.indexOf(value) !== -1\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.screenContent {\n  .text-center {\n    text-align: center;\n  }\n  .player-btn-group {\n    .el-button--primary {\n      color: #567;\n      background: #fff;\n      border: 1px solid #dcdfe6 !important;\n    }\n    .active {\n      background-color: #298dff;\n      border: 1px solid #298dff !important;\n      color: #fff;\n    }\n  }\n  #screen-sticky > #screen-sticky-bottom {\n    display: none;\n  }\n  #screen-sticky-wrapper.sticky > #screen-sticky > #screen-sticky-bottom {\n    display: block;\n  }\n  .customContainer {\n    height: 100%;\n    .aside {\n      display: flex;\n      flex-direction: column;\n      .filter-tree {\n        flex: 1;\n        overflow-y: auto;\n        padding: 20px 0;\n      }\n    }\n  }\n  .video-show {\n    .video {\n      border: 1px solid #fff;\n    }\n  }\n}\n</style>\n"]}]}