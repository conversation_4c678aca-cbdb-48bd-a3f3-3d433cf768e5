import request from "@/utils/request";

//巡更计划列表
export function GetPatrolPlan(data) {
  return request({
    method: "post",
    url: "/DF/PatrolPlan/GetPatrolPlan",
    data,
  });
}
export function GetPatrolPlanSZCJ(data) {
  return request({
    method: "post",
    url: "/DF/PatrolPlan/GetPatrolPlan/szcj",
    data,
  });
}
//巡更记录列表
export function GetPatrolLog(data) {
  return request({
    method: "post",
    url: "/DF/PatrolLog/GetPatrolLog",
    data,
  });
}
export function GetPatrolLogSZCJ(data) {
  return request({
    method: "post",
    url: "/DF/PatrolLog/GetPatrolLog/szcj",
    data,
  });
}
//巡更记录导出
export function ExportPatrolLog(data) {
  return request({
    method: "post",
    url: "/DF/PatrolLog/ExportPatrolLog",
    data,
  });
}
//巡更路线
export function GetPatrolRoute(data) {
  return request({
    method: "post",
    url: "/DF/PatrolRoute/GetPatrolRoute",
    data,
  });
}
export function GetPatrolRouteSZCJ(data) {
  return request({
    method: "post",
    url: "/DF/PatrolRoute/GetPatrolRoute/szcj",
    data,
  });
}
//巡更点
export function GetPatrolPoints(data) {
  return request({
    method: "post",
    url: "/DF/PatrolRoute/GetPatrolPoints",
    data,
  });
}
//部门
export function GetPartolDepartment(data) {
  return request({
    method: "get",
    url: "/DF/PatrolLog/GetPartolDepartment"
  });
}
//统计
export function GetPatrolStatistics(data) {
  return request({
    method: "post",
    url: "/DF/PatrolLog/GetPatrolStatistics",
    data,
  });
}
//详情
export function GetPatrolLogInfo(data) {
  return request({
    method: "get",
    url: "/DF/PatrolLog/GetPatrolLogInfo",
    params: data,
  });
}
