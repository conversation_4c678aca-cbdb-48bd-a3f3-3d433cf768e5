<template>
  <el-tabs v-model="activeName" tab-position="left">

    <!--    <el-tab-pane label="用户群组" name="group">
      <user-group
        ref="group"
        :group-names.sync="groupNames"
        :group.sync="selectGroupList"
        @handleUpdate="handleUpdate"
      />
    </el-tab-pane>-->
    <el-tab-pane label="角色" name="roles">
      <roles
        ref="roles"
        :show.sync="showDialog"
        :role-names.sync="roleNames"
        :roles.sync="selectRoleList"
        @handleUpdate="handleUpdate"
      />
    </el-tab-pane>
    <el-tab-pane label="用户" name="users">
      <users
        ref="users"
        :status.sync="status[1]"
        :show.sync="showDialog"
        :user-names.sync="userNames"
        :users.sync="selectUserList"
        @handleUpdate="handleUpdate"
      />
    </el-tab-pane>
    <el-tab-pane name="departments" label="部门">
      <department
        ref="departments"
        :show.sync="showDialog"
        :status.sync="status[2]"
        :department-names.sync="departmentNames"
        :departments.sync="selectDepartmentList"
        @handleUpdate="handleUpdate"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import users from './users'
import Roles from './roles'
import department from './department'
import userGroup from './userGroup'

export default {
  components: {
    users,
    Roles,
    department,
    userGroup
  },
  props: {
    group: {
      type: Array,
      default() {
        return []
      }
    },
    users: {
      type: Array,
      default() {
        return []
      }
    },
    roles: {
      type: Array,
      default() {
        return []
      }
    },
    departments: {
      type: Array,
      default() {
        return []
      }
    },
    groupNames: {
      type: String,
      default: ''
    },
    userNames: {
      type: String,
      default: ''
    },
    roleNames: {
      type: String,
      default: ''
    },
    departmentNames: {
      type: String,
      default: ''
    },
    orgId: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeName: 'roles',
      selectGroupList: [],
      selectUserList: [],
      selectRoleList: [],
      selectDepartmentList: [],
      status: [false, false, false, false]
    }
  },
  computed: {
    selectGroup: {
      get() {
        return this.group
      },
      set(val) {
        console.log('ccc', val)
        this.$emit('update:group', val)
      }
    },
    selectUsers: {
      get() {
        return this.users
      },
      set(val) {
        this.$emit('update:users', val)
      }
    },
    selectRoles: {
      get() {
        return this.roles
      },
      set(val) {
        this.$emit('update:roles', val)
      }
    },
    selectDepartments: {
      get() {
        return this.departments
      },
      set(val) {
        this.$emit('update:departments', val)
      }
    },
    groupNamesTxt: {
      get() {
        return this.groupNames
      },
      set(val) {
        this.$emit('update:groupNames', val)
      }
    },
    userNamesTxt: {
      get() {
        return this.userNames
      },
      set(val) {
        this.$emit('update:userNames', val)
      }
    },
    roleNamesTxt: {
      get() {
        return this.roleNames
      },
      set(val) {
        this.$emit('update:roleNames', val)
      }
    },
    departmentNamesTxt: {
      get() {
        return this.departmentNames
      },
      set(val) {
        this.$emit('update:departmentNames', val)
      }
    },
    showDialog: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  watch: {
    groupNames() {
      this.groupNamesTxt = this.groupNames
      this.getGroupList()
    },
    userNames() {
      this.userNamesTxt = this.userNames
      this.getUserGroupList()
    },
    roleNames() {
      this.roleNamesTxt = this.roleNames
      this.getRoleGroupList()
    },
    departmentNames() {
      this.departmentNamesTxt = this.departmentNames
      this.getDepartmentGroupList()
    },
    selectGroupList(val) {
      this.selectGroup = val && val.length > 0 && val.map(item => item.Id) || []
      this.groupNamesTxt = val && val.length > 0 && val.map(item => item.Label).join(',') || ''
    },
    selectUserList(val) {
      this.selectUsers = val && val.length > 0 && val.map(item => item.Id) || []
      this.userNamesTxt = val && val.length > 0 && val.map(item => item.Display_Name).join(',') || ''
    },
    selectRoleList(val) {
      this.selectRoles = val && val.length > 0 && val.map(item => item.Id) || []
      this.roleNamesTxt = val && val.length > 0 && val.map(item => item.Label).join(',') || ''
    },
    selectDepartmentList(val) {
      this.selectDepartments = val && val.length > 0 && val.map(item => item.Id) || []
      this.departmentNamesTxt = val && val.length > 0 && val.map(item => item.Label).join(',') || ''
    }
  },
  mounted() {
    this.getGroupList()
    this.getUserGroupList()
    this.getRoleGroupList()
    this.getDepartmentGroupList()
  },
  methods: {
    getGroupList() {
      if (!this.groupNames) {
        this.selectGroupList = []
        return
      }
      const nameArr = this.groupNames && this.groupNames.split(',')
      this.selectGroupList = this.selectGroup.map((item, index) => { return { Id: item, Label: nameArr[index] } })
    },
    getUserGroupList() {
      if (!this.userNames) {
        this.selectUserList = []
        return
      }
      const nameArr = this.userNames && this.userNames.split(',')
      this.selectUserList = this.selectUsers.map((item, index) => { return { Id: item, Display_Name: nameArr[index] } })
    },
    getRoleGroupList() {
      if (!this.roleNames) {
        this.selectRoleList = []
        return
      }
      const nameArr = this.roleNames && this.roleNames.split(',')
      this.selectRoleList = this.selectRoles.map((item, index) => { return { Id: item, Label: nameArr[index] } })
    },
    getDepartmentGroupList() {
      if (!this.departmentNames) {
        this.selectDepartmentList = []
        return
      }
      const nameArr = this.departmentNames && this.departmentNames.split(',')
      this.selectDepartmentList = this.selectDepartments.map((item, index) => { return { Id: item, Label: nameArr[index] } })
    },
    handleOpen() {
      this.dialogVisible = true
    },
    async handleSubmit() {
      console.log('submit')
      await this.$refs.users.getCheckedData()
      await this.$refs.roles.getCheckedData()
      await this.$refs.departments.getCheckedData()
      // this.$refs.group.getCheckedData()
    },
    // 等待所有数据处理完毕，销毁弹窗
    handleUpdate(v) {
      this.status[v] = true
      if (this.status.every(item => !!item)) {
        this.$nextTick(_ => {
          this.showDialog = false
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
