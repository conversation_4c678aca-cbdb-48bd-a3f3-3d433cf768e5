import request from '@/utils/request'
// 获取工序列表
export function GetProcessList(data) {
    return request({
        method: 'post',
        url: '/DF/Report/GetProcessList',
        data
    })
}
// 获取所有工序列表
export function GetAllProcessList(data) {
  return request({
      method: 'post',
      url: '/DF/Report/GetAllProcessList',
      data
  })
}
// 获取所有班组列表
export function GetTeamList(data) {
  return request({
      method: 'post',
      url: '/DF/Report/GetTeamList',
      data
  })
}
// 看板基础
export function GetProductionBoardBase(data) {
  return request({
      method: 'post',
      url: '/DF/Report/GetProductionBoardBase',
      data
  })
}
// 工序详情
export function GetProductionBoardProcess(data) {
  return request({
      method: 'post',
      url: '/DF/Report/GetProductionBoardProcess',
      data
  })
}
// 班组详情
export function GetProductionBoardTeam(data) {
  return request({
      method: 'post',
      url: '/DF/Report/GetProductionBoardTeam',
      data
  })
}
