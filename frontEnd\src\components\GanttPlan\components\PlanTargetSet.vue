<template>
  <div class="target-plan-set">
    <el-form ref="form" label-width="120px">
      <el-form-item label="当前计划">
        <strong>{{ plan.name }}</strong>
      </el-form-item>
      <el-form-item label="选择目标计划">
        <el-select v-model="form.target" placeholder="请选择目标计划">
          <el-option label="区域一" value="shanghai" />
          <el-option label="区域二" value="beijing" />
        </el-select>
      </el-form-item>
      <el-form-item label="说明">
        <ol style="margin:0;paddiing-left:0;margin-right:24px;">
          <li>
            设计目标计划后，目标计划以出现在横道图中，前锋线也会根据计划与目标对比显示折线；
          </li>
          <li>
            选择“当前进度计划副本”，确认后将在进度计划列表创建一个当前进度计划的副本，并标记为目标计划。
          </li>
        </ol>
      </el-form-item>
      <el-form-item label="" align="right">
        <el-button @click="$emit('dialogCancel')">取消</el-button>
        <el-button type="primary">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'PlanTargetSet',
  props: {
    plan: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        target: ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.target-plan-set {
  margin-top: -16px;
}
</style>
