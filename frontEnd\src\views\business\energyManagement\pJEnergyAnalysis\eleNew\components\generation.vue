<template>
  <div v-loading="loading" class="generation" element-loading-text="加载中...">
    <div class="title">
      <div class="left">发电及用电趋势图</div>
      <div class="right">不包含重钢工厂</div>
    </div>
    <div class="chartBox">
      <v-chart :option="lineChartOption" :autoresize="true" />
    </div>
  </div>
</template>

<script>
import { GetElectricTrend } from '@/api/business/energyManagement.js'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON>, Line<PERSON>hart, PieChart } from 'echarts/charts'
import {
  GridComponent,
  LegendComponent,
  TooltipComponent,
  TitleComponent,
  DataZoomComponent
} from 'echarts/components'
use([
  Canvas<PERSON>ender<PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  DataZoomComponent,
  Grid<PERSON>omponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent
])
export default {
  components: {
    VChart
  },
  props: {
    isPhotovoltaic: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      lineChartOption: {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          selected: {
            '总发电(光伏)': false,
            '售卖(光伏)': false
          }
        },
        grid: {
          left: '3%',
          right: '1%',
          bottom: '80'
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },

        color: ['#FF902C', '#298DFF', '#01AD5E', '#00B0F0', '#FF5E7C'],
        yAxis: [
          {
            type: 'value',
            position: 'left',
            logBase: 10
          }
        ],
        dataZoom: {
          type: 'slider'
        },
        series: [
          {
            name: '总用电',
            data: [5, 6, 7, 8, 1, 2, 3, 4, 6],
            type: 'line',
            tooltip: {
              valueFormatter: function(value) {
                return `${value || 0}` + ' 度'
              }
            }
          },
          {
            name: '用电(市电)',
            data: [1, 2, 3, 4, 5, 6, 7, 8],
            type: 'line',
            tooltip: {
              valueFormatter: function(value) {
                return `${value || 0}` + ' 度'
              }
            }
          }
        ]
      },
      loading: true
    }
  },
  computed: {
    parentData() {
      return {
        DateType: this.DateType(),
        StartTime: this.StartTime(),
        randomInteger: this.randomInteger()
      }
    }
  },
  watch: {
    parentData: {
      handler(nv, ov) {
        this.getElectricTrend()
      }
    }
  },
  created() {
    this.getElectricTrend()
  },
  mounted() {

  },
  inject: ['DateType', 'StartTime', 'randomInteger'],
  methods: {
    async getElectricTrend() {
      this.loading = true
      const res = await GetElectricTrend(this.parentData)
      if (res.IsSucceed) {
        this.lineChartOption.xAxis.data = res.Data.map(item => item.Key)
        this.lineChartOption.series[0].data = res.Data.map(item => item.Total ?? 0)
        this.lineChartOption.series[1].data = res.Data.map(item => item.Electric ?? 0)
        if (this.isPhotovoltaic) {
          this.lineChartOption.series.push(
            {
              name: '用电(光伏)',
              data: [3, 4, 5, 6, 7, 8, 1, 2],
              type: 'line',
              tooltip: {
                valueFormatter: function(value) {
                  return `${value || 0}` + ' 度'
                }
              }
            },
            {
              name: '总发电(光伏)',
              data: [7, 8, 3, 4, 5, 6, 1, 2],
              type: 'line',
              tooltip: {
                valueFormatter: function(value) {
                  return `${value || 0}` + ' 度'
                }
              }
            },
            {
              name: '售卖(光伏)',
              data: [9, 3, 4, 5, 6, 7, 8, 1],
              type: 'line',
              tooltip: {
                valueFormatter: function(value) {
                  return `${value || 0}` + ' 度'
                }
              }
            }
          )
          this.lineChartOption.series[2].data = res.Data.map(item => item.PV ?? 0)
          this.lineChartOption.series[3].data = res.Data.map(item => item.TotalPV ?? 0)
          this.lineChartOption.series[4].data = res.Data.map(item => item.SellPV ?? 0)
        }
      }
      this.loading = false
    }
  }
}
</script>
<style scoped lang='scss'>
.generation {
  height: 392px;
  background: #fff;
  border-radius: 4px;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  margin-bottom: 16px;
  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .left {
      color: #666;
      font-weight: bold;
      font-size: 16px;
    }
    .right {
      font-size: 12px;
      color: #b8bec8;
    }
  }
  .chartBox {
    height: 320px;
  }
}
</style>
